<?php

global $di;

use Phalcon\Mvc\Micro;
use Phalcon\Loader;
use Phalcon\Di\FactoryDefault;
use Phalcon\Db\Adapter\Pdo\Mysql as PdoMysql;
use Phalcon\Http\Response;
use Phalcon\Mvc\Micro\Collection as MicroCollection;
use Phalcon\Mvc\Micro\Exception;
use Phalcon\Events\Manager as Manager;
use FirewallMiddleware as firewall;
use NotFoundMiddleware as notFound;

define('S_HOST', gethostname());
$env = 'PROD';
$phalcon = 4;
$err = 0;

if (in_array(S_HOST, ['ke-pr-web-1'])) {
    $env = 'DEV';
    $phalcon = 3;
    $err = 1;
    error_reporting(E_ALL);
}

define('APP_PATH', realpath(''));
define('PHALCON_VERSION', $phalcon);
define('ENVIRONMENT', $env);

ini_set('display_errors', $err);
ini_set('display_startup_errors', $err);
ini_set("date.timezone", "Africa/Nairobi");
ini_set('default_socket_timeout', 160);

/**
 * Read auto-loader
 */
include APP_PATH . "/vendor/autoload.php";

/**
 * Read the configuration
 */
$config = include APP_PATH . "/app/config/config.php";

/**
 * Read auto-loader
 */
include APP_PATH . "/app/config/loader.php";

/**
 * Read services
 */
include APP_PATH . "/app/config/services.php";

/**
 * create and bind the DI to the application
 */
$di = new FactoryDefault();
$app = new Micro($di);

/**
 * Create a new Events Manager.
 */
$manager = new Manager();



/**
 * Dashboard
 */
$ds = new MicroCollection();
$ds->setPrefix('/ds/');
$ds->setHandler(DashboardController::class, true);
$ds->mapVia('v1/view', 'ViewDashboardStats', ['POST', 'GET']);
$ds->mapVia('v1/summary', 'ViewSummaryReports', ['POST', 'GET']);
$ds->mapVia('v1/summary/averages', 'ViewSummaryAverageReports', ['POST', 'GET']);
$ds->mapVia('v1/game/summary', 'GameSummary', ['POST', 'GET']);

$ds->mapVia('v1/greener_monday', 'GetGreenerMonday', ['POST', 'GET']);
$ds->mapVia('v1/sports_bets_summary', 'GetSportsBetsSummary', ['POST', 'GET']);
$ds->mapVia('v1/virtual_bets_summary', 'GetVirtualBetsSummary', ['POST', 'GET']);
$ds->mapVia('v1/softgames_bets_summary', 'GetSoftGamingSummary', ['POST', 'GET']);


/**
 * Reports
 */
$reports = new MicroCollection();
$reports->setPrefix('/rpt/');
$reports->setHandler(ReportController::class, true);
$reports->mapVia('v1/list/outbox', 'GetSmsOutbox', ['POST', 'GET']);
$reports->mapVia('v1/list/blast_sms', 'GetBlastSmsOutbox', ['POST', 'GET']);
$reports->mapVia('v1/list/sms_filters', 'BlastSMSFilters', ['GET', 'POST']);
$reports->mapVia('v1/list/referrals', 'Referrals', ['POST', 'GET']);
$reports->mapVia('v1/list/paybills', 'GetPaybills', ['POST', 'GET']);
$reports->mapVia('v1/list/channels', 'GetAuthChannels', ['POST', 'GET']);
$reports->mapVia('v1/list/taxes', 'GetKRATaxes', ['POST', 'GET']);
$reports->mapVia('v1/list/tax_summary', 'GetKRATaxSummary', ['POST', 'GET']);
$reports->mapVia('v1/list/tax_payments', 'GetKRATaxPaymentSummary', ['POST', 'GET']);

$reports->mapVia('v1/special_reports', 'GetSpecialReports', ['POST', 'GET']);

/**
 * Bets
 */
$bets = new MicroCollection();
$bets->setPrefix('/bets/');
$bets->setHandler(BetsController::class, true);
# Sports-Bets
$bets->mapVia('v1/sports', 'GetSportsBets', ['POST', 'GET']);
$bets->mapVia('v1/virtual', 'GetVirtualBets', ['POST', 'GET']);
$bets->mapVia('v1/soft_gaming', 'GetSoftGamingBets', ['POST', 'GET']);

$bets->mapVia('v1/casino/{typeId}', 'GetCasinoBets', ['POST', 'GET']);

#ResettleBet
$bets->mapVia('v1/resettle/{betId}', 'ResettleBet', ['POST', 'GET']);
# Bet Slip
$bets->mapVia('v1/bet_slip', 'GetSportsBetSlips', ['POST', 'GET']);
# Bet Audits
$bets->mapVia('v1/audits', 'GetBetAudits', ['POST', 'GET']);
$bets->mapVia('v1/audits/update/{id}', 'UpdateBetAudit', ['POST', 'GET']);
# Bet Limits
$bets->mapVia('v1/limits', 'GetBetLimits', ['POST', 'GET']);
$bets->mapVia('v1/limits/update/{id}', 'UpdateBetLimit', ['POST', 'GET']);
# Bet Games
$bets->mapVia('v1/games', 'GetBetGames', ['POST', 'GET']);
$bets->mapVia('v1/games/update/{id}', 'UpdateBetGame', ['POST', 'GET']);

$bets->mapVia('v1/blocked_ips', 'GetBlockedIPs', ['POST', 'GET']);
$bets->mapVia('v1/blocked_ips/update/{id}', 'UpdateBlockedIP', ['POST', 'GET']);
$bets->mapVia('v1/blocked_ips/profiles', 'GetBlockedIPProfiles', ['POST', 'GET']);


/**
 * Customer
 */
$cust = new MicroCollection();
$cust->setPrefix('/customer/');
$cust->setHandler(CustomerController::class, true);
$cust->mapVia('v1/profiles', 'GetProfiles', ['POST', 'GET']);
$cust->mapVia('v1/profiles/old', 'GetProfilesOld', ['POST', 'GET']);
$cust->mapVia('v1/profiles/self_exclusion', 'GetSelfExclusionCustomers', ['POST', 'GET']);
$cust->mapVia('v1/profiles/self_exclusion/add', 'AddSelfExclusionCustomer', ['POST', 'GET']);
$cust->mapVia('v1/profiles/self_exclusion/update/{id}', 'UpdateSelfExclusionCustomer', ['POST', 'GET']);
#AddFreebetsToCustomer
$cust->mapVia('v1/award_customer_freebet', 'AwardFreebetsToCustomer', ['POST', 'GET']);
#AddFreebetsToCustomers
$cust->mapVia('v1/award_customers_freebet', 'AwardFreebetsToCustomers', ['POST', 'GET']);

$cust->mapVia('v1/blacklist/{profileId}', 'BlacklistCustomer', ['POST']);
$cust->mapVia('v1/withdrawals/{profileId}', 'EnableDisableCustomerWithdrawals', ['POST']);
$cust->mapVia('v1/bulk_sms/{profileId}', 'EnableDisableCustomerSMS', ['POST']);
$cust->mapVia('v1/resend_otp/{ProfileId}', 'ResendOTPCustomer', ['POST']);

#SMS
$cust->mapVia('v1/send_individual_sms', 'SendProfileSMS', ['POST']);
$cust->mapVia('v1/send_blast', 'SendBlastSMS', ['GET','POST']);
$cust->mapVia('v1/schedule_sms', 'ScheduleSMS', ['POST']); // Schedule SMS for future delivery
$cust->mapVia('v1/get_scheduled_sms', 'GetScheduledSMS', ['GET', 'POST']); // View scheduled SMS (debugging)
$cust->mapVia('v1/process_scheduled_sms', 'ProcessScheduledSMS', ['POST', 'GET']); // Cron job route
$cust->mapVia('v1/test_scheduled_sms', 'TestScheduledSMS', ['GET', 'POST']); // Test scheduled SMS functionality
$cust->mapVia('v1/debug_scheduled_sms', 'DebugScheduledSMS', ['GET', 'POST']); // Debug scheduled SMS functionality



/**
 * Account
 */
$ac = new MicroCollection();
$ac->setPrefix('/adm/');
$ac->setHandler(AccountController::class, true);
#Paybills
$ac->mapVia('v1/paybill_create', 'CreatePaybillEntry', ['POST']);
$ac->mapVia('v1/paybill_edit/{paybillId}', 'EditPaybillEntry', ['POST']);
$ac->mapVia('v1/paybill_enable/{paybillId}', 'EnableDisablePaybill', ['POST']);
$ac->mapVia('v1/paybill_ipaddress/{paybillId}', 'AddDeletePaybillIPs', ['POST']);
$ac->mapVia('v1/paybill_pull/{paybillId}', 'RegisterPaybillForPullService', ['POST', 'GET']);
#Deposits Reconciliation
$ac->mapVia('v1/depost/recon', 'CreateReconCiliation', ['POST']);
$ac->mapVia('v1/deposit/manual', 'DepositManualReconcile', ['POST']);
#Payouts
$ac->mapVia('v1/payouts/route/{paybillId}', 'EnableDisablePayoutRouting', ['POST']);
$ac->mapVia('v1/payouts/repost', 'PayoutsCallbackRepost', ['POST']);
#Auth Channels
$ac->mapVia('v1/channel_create', 'CreateAuthChannel', ['POST']);
$ac->mapVia('v1/channel_edit/{channelId}', 'EditAuthChannel', ['POST']);

/**
 * User
 */
$user = new MicroCollection();
$user->setPrefix('/user/');
$user->setHandler(UserController::class, true);
$user->mapVia('v1/user_login', 'UserAccountLogin', ['POST']);
$user->mapVia('v1/verify_login', 'VerifyLoginOTP', ['POST']);
$user->mapVia('v1/reset_password', 'ResetUserAccountPassword', ['POST']);
$user->mapVia('v1/change_password', 'ChangeUserAccountPassword', ['POST']);
$user->mapVia('v1/validate_otp', 'ValidateAuthOTP', ['POST']);
$user->mapVia('v1/create', 'CreateUserAccount', ['POST']);
$user->mapVia('v1/edit/{user_id}', 'EditUserAccount', ['POST', 'GET']);
#Roles
$user->mapVia('v1/system/roles', 'GetUserRoles', ['GET']);
$user->mapVia('v1/system/roles/create', 'CreateUserRoles', ['POST']);
$user->mapVia('v1/system/roles/update/{roleId}', 'EditUserRoles', ['POST']);
#Permissions
$user->mapVia('v1/system/permissions', 'GetUserPermissions', ['GET']);
$user->mapVia('v1/system/permission/create', 'CreatePermission', ['POST']);
$user->mapVia('v1/system/permission/update/{permissionId}', 'EditPermission', ['POST']);
#User management
$user->mapVia('v1/system/users/list', 'GetSystemUsers', ['POST', 'GET']);
$user->mapVia('v1/system/users/edit/{user_id}', 'EditUserAccount', ['POST']);
$user->mapVia('v1/resend_otp/{user_id}', 'ResendUserOTP', ['POST', 'GET']);
$user->mapVia('v1/system/users/blacklist/{user_id}', 'BlacklistUser', ['POST']);


/**
 * Bonus
 */
$bonus = new MicroCollection();
$bonus->setPrefix('/bonus/');
$bonus->setHandler(BonusController::class, true);
$bonus->mapVia('v1/promotions', 'GetPromotions', ['POST', 'GET']);
$bonus->mapVia('v1/promotions/types', 'GetPromotionTypes', ['POST', 'GET']);
$bonus->mapVia('v1/promotions/create', 'CreatePromotions', ['POST', 'GET']);
$bonus->mapVia('v1/promotions/edit/{promoId}', 'EditPromotions', ['POST', 'GET']);
$bonus->mapVia('v1/menu_highlights', 'GetMenuHighlights', ['POST', 'GET']);
$bonus->mapVia('v1/menu_highlights/edit/{highlightId}', 'EditMenuHighlights', ['POST']);
$bonus->mapVia('v1/campaign_origins', 'GetCampaignOrigins', ['GET']);
$bonus->mapVia('v1/campaign_origins/transactions/{campaignOriginId}', 'GetCampaignOriginTransactions', ['POST', 'GET']);
#Leaderboard
$bonus->mapVia('v1/leaderboard', 'GetLeaderboard', ['POST', 'GET']);
$bonus->mapVia('v1/leaderboard/update/{leaderboardId}', 'UpdateLeaderboard', ['POST', 'GET']);

#Award Bonus
$bonus->mapVia('v1/award_bonus_freebet', 'AwardCustomerBonusFreebet', ['POST', 'GET']);

/**
 * Games
 */
$games = new MicroCollection();
$games->setPrefix('/games/');
$games->setHandler(GamesController::class, true);
$games->mapVia('v1/game_categories/create', 'CreateGameCategories', ['POST', 'GET']);
$games->mapVia('v1/game_categories/edit/{categoryId}', 'EditGameCategories', ['POST', 'GET']);

$games->mapVia('v1/game_categories', 'GetGameCategories', ['POST', 'GET']);
$games->mapVia('v1/list', 'GetGames', ['POST', 'GET']);
$games->mapVia('v1/add_games_to_category', 'UpdateGame', ['POST', 'GET']);
$games->mapVia('v1/update/{gameId}', 'UpdateGame', ['POST', 'GET']);

$games->mapVia('v1/manual_results', 'GetManualResultFixtures', ['POST', 'GET']);
$games->mapVia('v1/manual_results/archive', 'GetManualResultFixturesArchive', ['POST', 'GET']);
$games->mapVia('v1/update_scores/{fixtureId}', 'UpdateFixtureScores', ['POST', 'GET']);
$games->mapVia('v1/update_scores/archive/{fixtureId}', 'UpdateFixtureScoresArchive', ['POST', 'GET']);

$games->mapVia('v1/bet_slip_count', 'GetBetSlipCount', ['POST', 'GET']);
$games->mapVia('v1/fixtures', 'GetFixtures', ['POST', 'GET']);
$games->mapVia('v1/edit/{fixtureId}', 'EditFixture', ['POST', 'GET']);
$games->mapVia('v1/list/countries', 'GetCountries', ['POST', 'GET']);
$games->mapVia('v1/list/sports', 'GetSports', ['POST', 'GET']);
$games->mapVia('v1/list/tournaments', 'GetTournaments', ['POST', 'GET']);
$games->mapVia('v1/list/tournaments/edit/{tournamentId}', 'EditTournament', ['POST', 'PUT']);
$games->mapVia('v1/list/markets', 'GetMarkets', ['POST', 'GET']);
$games->mapVia('v1/list/markets/edit/{marketId}', 'EditMarket', ['POST', 'PUT']);
#unused for now
$games->mapVia('v1/create', 'CreateGames', ['POST']);
$games->mapVia('v1/list/odds_live', 'GetOddsLive', ['POST', 'GET']);

/**
 * Sports book
 */
$sportsbook = new MicroCollection();
$sportsbook->setPrefix('/matches/');
$sportsbook->setHandler(GamesController::class, true);
$sportsbook->mapVia('v1/list/prematch', 'ViewPreMatches', ['POST', 'GET']);
$sportsbook->mapVia('v1/list/live', 'ViewLiveMatches', ['POST', 'GET']);
$sportsbook->mapVia('v1/list/outcomes', 'ViewMatchOutcomes', ['POST', 'GET']);
$sportsbook->mapVia('v1/list/placed_bets', 'ViewBetsPlaced', ['POST', 'GET']);
$sportsbook->mapVia('v1/view/boosted_odds', 'ViewBoosteOdds', ['POST', 'GET']);
$sportsbook->mapVia('v1/boosted_odds', 'SetBoosteOdds', ['POST', 'GET']);
$sportsbook->mapVia('v1/view/highlights', 'ViewHighlights', ['POST', 'GET']);
$sportsbook->mapVia('v1/highlights', 'SetHighlights', ['POST', 'GET']);
$sportsbook->mapVia('v1/list/sports', 'ViewSports', ['POST', 'GET']);
$sportsbook->mapVia('v1/list/countries', 'ViewCountries', ['POST', 'GET']);
$sportsbook->mapVia('v1/list/tournament', 'ViewTournament', ['POST', 'GET']);
$sportsbook->mapVia('v1/list/markets', 'ViewMarkets', ['POST', 'GET']);

/**
 * Transactions
 */
$wallet = new MicroCollection();
$wallet->setPrefix('/trxns/');
$wallet->setHandler(TransactionsController::class, true);
$wallet->mapVia('v1/all', 'GetTransactions', ['POST', 'GET']);
$wallet->mapVia('v1/deposits', 'GetDeposits', ['POST', 'GET']);
$wallet->mapVia('v1/withdrawals', 'GetWithdrawals', ['POST', 'GET']);
$wallet->mapVia('v1/wallet-approvals', 'GetWalletApprovals', ['POST', 'GET']);
$wallet->mapVia('v1/approval-risk/{trxId}', 'RiskApprovals', ['POST', 'GET']);
$wallet->mapVia('v1/approve', 'ApproveWalletTransactions', ['POST', 'GET']);
$wallet->mapVia('v1/upload/risk_approvals', 'UploadRiskApprovals', ['POST', 'GET']);


/**
 * Patner
 */
$partner = new MicroCollection();
$partner->setPrefix('/ptns/');
$partner->setHandler(PatnerController::class, true);
$partner->mapVia('v1/partners', 'GetPartners', ['POST', 'GET']);
$partner->mapVia('v1/partner_services', 'GetDeposits', ['POST', 'GET']);
$partner->mapVia('v1/partners_bet_slips', 'GetDeposits', ['POST', 'GET']);

/**
 * Pragmatic
 */
$pragmatic = new MicroCollection();
$pragmatic->setPrefix('/pragmatic/');
$pragmatic->setHandler(PragmaticController::class, true);
$pragmatic->mapVia('v1/check_fbs', 'CheckFBS', ['POST', 'GET']);

#Mount Points
$app->mount($ds);
$app->mount($ac);
$app->mount($cust);
$app->mount($bets);
$app->mount($user);
$app->mount($bonus);
$app->mount($games);
$app->mount($wallet);
$app->mount($partner);
$app->mount($reports);
$app->mount($pragmatic);

try {
    if ($app->request->getMethod() == "OPTIONS") {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header("Access-Control-Allow-Headers: X-App-Key,X-Hash-Key,X-Access,X-Authorization"
                . ",X-Authorization-Key,X-Source,X-Campaign,X-Authorization-Key,Authorization,X-Requested-With,Content-Disposition,Origin,accept,X-Access,X-Signature,Content-Type,Access-Control-Request-Method,Access-Control-Request-Headers,Access-Control-Allow-Origin");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Expose-Headers: Content-Length,X-JSON');
        header("HTTP/1.1 200 OK");
        exit();
    }

    $app->after(function () use ($app) {
        $app->response
                ->setHeader("Accept", "*/*")
                ->setHeader("Accept-Encoding", "gzip")
                ->setHeader("Accept-Charset", "utf-8")
                ->setHeader("Access-Control-Allow-Credentials", true)
                ->setHeader("Access-Control-Allow-Methods", 'GET,POST')
                ->setHeader("Access-Control-Allow-Origin", '*')
                ->setHeader('Access-Control-Allow-Headers', 'X-App-Key,X-Hash-Key,X-Access,X-Authorization,X-Source,X-Campaign,X-Authorization-Key,Authorization,X-Requested-With,Content-Disposition,Origin,accept,X-Access,X-Signature,Content-Type,Access-Control-Request-Method,Access-Control-Request-Headers,Access-Control-Allow-Origin')
                ->setHeader('Access-Control-Expose-Headers', 'Content-Length,X-JSON');
        $app->response->sendHeaders();
        return true;
    });

    /**
     * Not Found URLs
     */
    $app->notFound(function () use ($app) {
        $nfm = new notFound();
        $nfm->before($app);
    });

    $app->setEventsManager($manager);

    // Handle the request
    header('Access-Control-Allow-Origin:*');

    if (PHALCON_VERSION == 4) {
        $response = $app->handle($_SERVER['REQUEST_URI']);
    } else {
        $response = $app->handle();
    }
} catch (Exception $e) {
    $res = new \stdClass();
    $res->code = 500;
    $res->statusDescription = "Request is not successful. ";
    $res->data = ['code' => 500, 'message' => "[SEVERE ERROR] Internal server error!"];

    $app->response->setStatusCode(500, 'Internal Server Error')
            ->setJsonContent($res)
            ->send();
}

// ATBBXfQWyY4rPJFTwQyfQR59S6YhEA45CE6D


