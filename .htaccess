<IfModule mod_php7.c>
    php_value max_execution_time 600
    php_value upload_max_filesize 2M
    php_value post_max_size 20M
    php_value memory_limit 2500M
</IfModule>

<ifModule mod_headers.c>
    Header set Connection keep-alive
    Header always set Access-Control-Allow-Methods "POST, GET,OPTIONS"
</ifModule>

<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^((?s).*)$ index.php?_url=/$1 [QSA,L]
</IfModule>

<Files ~ "^.*\.([Hh][Tt][Aa])">
    order allow,deny
    deny from all
    satisfy all
</Files>

<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|config|git)$">
Order Allow,Deny
Deny from all
</FilesMatch>