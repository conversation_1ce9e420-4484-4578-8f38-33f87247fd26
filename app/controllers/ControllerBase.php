<?php

use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Formatter\LineFormatter;
use \Firebase\JWT\JWT as jwt;
use Phalcon\Mvc\Controller;
use Phalcon\Http\Response;
use Phalcon\Http\Request;
use Phalcon\Mvc\Dispatcher as Dispatcher;

class ControllerBase extends Controller {

    public $request;
    public $ipAddress;
    public $infologger;
    public $errorlogger;

    function onConstruct() {
        $this->request = new Request();
        $this->infologger = $this->getLogFile('info');
        $this->errorlogger = $this->getLogFile('error');
        $this->ipAddress = $this->getClientIPServer();
    }

    /**
     * getWeekday
     * @param type $date
     * @return array
     */
    function getWeekday($date):array {
        return [
            'name' => date('l', strtotime($date)),
            'value' => date('w', strtotime($date)) + 1
        ];
    }

    /**
     * compareMatchingXcters
     * @param type $actualstr
     * @param type $stringCmp
     * @return int
     */
    function compareMatchingXcters($actualstr, $stringCmp) {
        $count = 0;
        $actualArr = explode(',', $actualstr);
        $cmpArr = explode(',', $stringCmp);

        for ($i = 0; $i < count($cmpArr); $i++) {
            if ($cmpArr[$i] === $actualArr[$i]) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * base64Encode
     * @param type $data
     * @return type
     */
    public function base64Encode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * beforeExecuteRoute
     * @param \Dispatcher $dispatcher
     * @param \Event $event
     * @return \Dispatcher
     */
    public function beforeExecuteRoute() {

    }

    /**
     * afterExecuteRoute
     * @param Dispatcher $dispatcher
     */
    public function afterExecuteRoute(Dispatcher $dispatcher) {

    }

    /**
     * maskMobile
     * @param type $mobile
     * @return type
     */
    public function maskMobile($mobile) {
        return substr($mobile, 0, -4) . "****";
    }

    /**
     * SystemReference
     * @param type $id
     * @return type
     */
    function SystemReference($id, $concat = 'mBG_') {
        return $concat . '' . $this->Encrypt($id);
    }

    /**
     * UnSystemReference
     * @param type $id
     * @return type
     */
    function GetSystemReference($id) {
        $data = explode('_', $id);
        return $this->Decrypt($data[1]);
    }

    /**
     * CalculateTAT
     * @param type $start
     * @return type
     */
    public function CalculateTAT($start) {
        $tat = $this->getMicrotime() - $start;

        return round($tat, 5);
    }

    /**
     * This function encrypts the data passed into it and returns the cipher data
     * with the IV embedded within it.The initialization vector (IV) is appended
     * to the cipher data with the use of two colons serve to delimited between the two.
     * @param type $ClearTextData
     * @return type
     */
    public function Encrypt($ClearTextData) {
        try {
            $EncryptionKey = base64_decode($this->settings['Authentication']['SecretKey']);
            $InitializationVector = openssl_random_pseudo_bytes(openssl_cipher_iv_length('AES-256-CBC'));
            $EncryptedText = openssl_encrypt($ClearTextData, 'AES-256-CBC', $EncryptionKey, 0, $InitializationVector);
            return base64_encode($EncryptedText . '::' . $InitializationVector);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * This function decrypts the cipher data (with the IV embedded within)
      passed into it  and returns the clear text (unencrypted) data. The
      initialization vector (IV) is appended to the cipher data by the Encrypt.
      This function (see above). There are two colons that serve to delimited
      between the cipher data and the IV.
     * @param type $CipherData
     * @return type
     */
    public function Decrypt($CipherData) {
        try {
            $EncryptionKey = base64_decode($this->settings['Authentication']['SecretKey']);
            list($Encrypted_Data, $InitializationVector ) = array_pad(explode('::', base64_decode($CipherData), 2), 2, null);
            return openssl_decrypt($Encrypted_Data, 'AES-256-CBC', $EncryptionKey, 0, $InitializationVector);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * sendGetRequestWithHeaders
     * @param type $Url
     * @param type $headers
     * @return type
     */
    public function sendGetRequestWithHeaders($Url, $headers) {
        $httpRequest = curl_init($Url);
        curl_setopt($httpRequest, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($httpRequest, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($httpRequest, CURLOPT_ENCODING, "");
        curl_setopt($httpRequest, CURLOPT_TIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_CONNECTTIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
//accept SSL settings
        curl_setopt($httpRequest, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($httpRequest);
        $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
        $curlError = curl_error($httpRequest);
        curl_close($httpRequest);

        return ["statusCode" => $status, "response" => $response, 'error' => $curlError];
    }

    /**
     * sendPostDataWithHeaders
     * @param type $postUrl
     * @param type $postData
     * @param type $headers
     * @return type
     */
    public function sendPostDataWithHeaders($postUrl, $postData, $headers) {
        $headers[] = 'Content-Length: ' . strlen(json_encode($postData));
        $headers[] = 'X-Requested-With: XMLHttpRequest';
        $headers[] = 'Content-Type: application/json';

        $httpRequest = curl_init($postUrl);
        curl_setopt($httpRequest, CURLOPT_NOBODY, true);
        curl_setopt($httpRequest, CURLOPT_POST, true);
        curl_setopt($httpRequest, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($httpRequest, CURLOPT_TIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_CONNECTTIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
//accept SSL settings
        curl_setopt($httpRequest, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($httpRequest, CURLOPT_USERPWD, '<EMAIL>:' . md5('<EMAIL>'));
        curl_setopt($httpRequest, CURLOPT_USERAGENT, $this->settings['appName'] . "/3.0");
        $results = curl_exec($httpRequest);
        $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
        $curlError = curl_error($httpRequest);
        curl_close($httpRequest);

        return ["statusCode" => $status, "response" => $results, 'error' => $curlError];
    }

    /**
     * strip_tags_content
     * @param type $string
     * @return type
     */
    public function strip_tags_content($string) {
        $string = preg_replace('/<[^>]*>/', ' ', $string);
// ----- remove control characters -----
        $string = str_replace("\r", '', $string);
        $string = str_replace("\n", ' ', $string);
        $string = str_replace("\t", ' ', $string);
        $string = str_replace("&nbsp;", ' ', $string);
        $string = str_replace("&quot;", ' ', $string);
        $string = preg_replace('/[\s]+/mu', ' ', $string);
        $string = preg_replace('/[\t\n\r\0\x0B]/', '', $string);
        $string = preg_replace('/([\s])\1+/', ' ', $string);
// ----- remove multiple spaces -----
        $string = trim(preg_replace('/ {2,}/', ' ', $string));
        return $string;
    }

    /**
     * clean variable string
     *
     * @param type $text
     * @return type
     */
    public function cleanString($text) {
        $utf8 = ['/[áàâãªä]/u' => 'a',
            '/[ÁÀÂÃÄ]/u' => 'A',
            '/[ÍÌÎÏ]/u' => 'I',
            '/[íìîï]/u' => 'i',
            '/[éèêë]/u' => 'e',
            '/[ÉÈÊË]/u' => 'E',
            '/[óòôõºö]/u' => 'o',
            '/[ÓÒÔÕÖ]/u' => 'O',
            '/[úùûü]/u' => 'u',
            '/[ÚÙÛÜ]/u' => 'U',
            '/ç/' => 'c',
            '/Ç/' => 'C',
            '/ñ/' => 'n',
            '/Ñ/' => 'N',
            '/–/' => '-', // UTF-8 hyphen to "normal" hyphen
            '/[’‘‹›‚]/u' => ' ', // Literally a single quote
            '/[“”«»„]/u' => ' ', // Double quote
            '/ /' => ' ', // nonbreaking space (equiv. to 0x160)
        ];
        $string = preg_replace(array_keys($utf8), array_values($utf8), $text);
        $string = stripslashes($string);
// $string = htmlspecialchars($string);

        return preg_replace('/[[:^print:]]/', '', trim($string));
    }

    /**
     * ValidateDateOnly
     * @param type $date
     * @return boolean
     */
    public function ValidateDateOnly($date) {
        try {
            $dt = DateTime::createFromFormat("Y-m-d", "$date 00:00:00");
            $return = ($dt !== false) && (!array_sum($dt::getLastErrors()));
            if (!$return) {
                return true;
            }

            return false;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * clean variable string
     *
     * @param type $text
     * @return type
     */
    public function cleanStrSQL($text) {
        $utf8 = array(
            '/[áàâãªä]/u' => 'a',
            '/[ÁÀÂÃÄ]/u' => 'A',
            '/[ÍÌÎÏ]/u' => 'I',
            '/[íìîï]/u' => 'i',
            '/[éèêë]/u' => 'e',
            '/[ÉÈÊË]/u' => 'E',
            '/[óòôõºö]/u' => 'o',
            '/[ÓÒÔÕÖ]/u' => 'O',
            '/[úùûü]/u' => 'u',
            '/[ÚÙÛÜ]/u' => 'U',
            '/ç/' => 'c',
            '/Ç/' => 'C',
            '/ñ/' => 'n',
            '/Ñ/' => 'N',
            '/–/' => '-', // UTF-8 hyphen to "normal" hyphen
            '/[’‘‹›‚]/u' => ' ', // Literally a single quote
            '/[“”«»„]/u' => ' ', // Double quote
            '/ /' => ' ', // nonbreaking space (equiv. to 0x160)
        );

        $string = preg_replace(array_keys($utf8), array_values($utf8), $text);
        $string = str_replace("'", '', $string);
//  $string = str_replace(" ", '', $string);
        $string = str_replace('drop', '', strtolower($string));
        $string = str_replace('delete', '', strtolower($string));
        $string = str_replace('select', '', strtolower($string));
        $string = str_replace('alter', '', strtolower($string));
        $string = str_replace('show', '', strtolower($string));
        $string = str_replace('set', '', strtolower($string));
        $string = str_replace('create', '', strtolower($string));
        $string = str_replace('grant', '', strtolower($string));
        $string = stripslashes($string);
// $string = htmlspecialchars($string);
        $string = strtoupper($string);

        return preg_replace('/[[:^print:]]/', '', trim($string));
    }

    /**
     * CompareDateGetMinuteDiffAndNow
     * @param type $from
     * @param type $now
     * @return type
     */
    public function CompareDateGetMinuteDiffAndNow($from, $now = null) {
        if (!$now) {
            $now = $this->now();
        }

        return (int) number_format(((new \DateTime($from))->getTimestamp() - (new \DateTime($now))->getTimestamp()) / 60);
    }

    /**
     * ValidatePassword
     * @param type $password
     * @return boolean|string
     */
    public function ValidatePassword($password) {
        $uppercase = preg_match('@[A-Z]@', $password);
        $lowercase = preg_match('@[a-z]@', $password);
        $number = preg_match('@[0-9]@', $password);
        $specialChars = preg_match('@[^\w]@', $password);
        if ((!$uppercase && !$lowercase) || (!$number && !$specialChars) || strlen($password) < 6) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * Generates a random integer between 48 and 122.
     * <p>
     * @return int Non-cryptographically generated random number.
     */
    function findRandom() {
        $mRandom = rand(48, 122);
        return $mRandom;
    }

    /**
     * Checks if $random equals ranges 48:57, 56:90, or 97:122.
     * <p>
     * This function is being used to filter $random so that when used in:
     * '&#' . $random . ';' it will generate the ASCII characters for ranges
     * 0:8, a-z (lowercase), or A-Z (uppercase).
     * <p>
     * @param int $mRandom Non-cryptographically generated random number.
     * @return int 0 if not within range, else $random is returned.
     */
    function isRandomInRange() {
        $mRandom = $this->findRandom();
        if (($mRandom >= 58 && $mRandom <= 64) ||
                (($mRandom >= 91 && $mRandom <= 96))) {
            return 0;
        } else {
            return $mRandom;
        }
    }

    /**
     * GenerateApiKey
     * @return type
     */
    public function GenerateApiKey($i, $j = 31) {
        $output = "";
        for ($loop = $i; $loop <= $j; $loop++) {
            for ($isRandomInRange = 0; $isRandomInRange === 0;
            ) {
                $isRandomInRange = $this->isRandomInRange($this->findRandom());
            }
            $output .= html_entity_decode('&#' . $isRandomInRange . ';');
        }

        return $output;
    }

    /**
     * Creates NewAuthToken
     * @param type $payload
     * @return type
     */
    public function createNewAuthToken($payload, $token = null) {
        if ($token == null) {
            $token = ['token' => "55abe029fdebae5e1d417e2ffb2a003a0cd8b54763051cef08bc55abe029"];
        }

        $secretKey = base64_encode($token);
        $jwtToken = jwt::encode($payload, $secretKey, 'HS512');

        return $jwtToken;
    }

    /**
     * HttpResponse
     * @param type $function
     * @param type $statusCode
     * @param type $message
     * @param type $data
     * @param type $iserror
     * @param type $is_secure
     * @param type $extra_headers
     */
    public function HttpResponse($function, $statusCode, $message, $data = null,
            $iserror = null, $is_secure = false, $extra_headers = false) {
        $response = new Response();


        $res = new \stdClass();
        $res->code = "Success";
        if ($iserror) {
            $res->code = "Error";
        }

        $res->statusDescription = $message;
        if ($data) {
            $res->data = $data;
        }

        $res = json_encode($res);
        $response->setContent($res);

        if ($extra_headers) {
            foreach ($extra_headers as $extra_header) {
                $response->setHeader($extra_header['header'], $extra_header['value']);
            }
        }

        $response->setHeader("X-Signature", md5(json_encode($data)
                        . '' . $this->settings['Authentication']['HttpRequestKey']));
        $response->setHeader("Content-Type", "application/json");
        $response->setHeader("Access-Control-Allow-Origin", "*");
        $response->setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        $response->setHeader("Access-Control-Allow-Headers", "X-App-Key,X-Hash-Key,X-Access,X-Authorization,X-Source,X-Campaign,X-Authorization-Key,Authorization,X-Requested-With,Content-Disposition,Origin,accept,X-Access,X-Signature,Content-Type,Access-Control-Request-Method,Access-Control-Request-Headers,Access-Control-Allow-Origin");
        $response->setHeader("Access-Control-Allow-Credentials", "true");
        $response->setHeader("Access-Control-Expose-Headers", "Content-Length,X-JSON");
        $response->setStatusCode($statusCode, $this->HttpReason($statusCode));

        if ($is_secure) {
            $res = '{***Secure***}';
        }

        $this->getLogFile('debug')->info("$function - Response:" . $res);

        return $response;
    }

    /**
     * getDatetimeNow
     * @return type
     */
    public function getDatetimeNow() {
        $tz_object = new DateTimeZone('Africa/Nairobi');
        $datetime = new DateTime();
        $datetime->setTimezone($tz_object);

        return $datetime->format('Y\-m\-d\ H:i:s');
    }

    /**
     * getMicrotime
     * @return type
     */
    public function getMicrotime() {
        list ($msec, $sec) = explode(" ", microtime());
        return ((float) $msec + (float) $sec);
    }

    /**
     * Return the current Date and time in the standard format
     * @param string $format the format in which to return the date
     * @return string
     */
    public function now($format = 'Y-m-d H:i:s', $timestamp = null) {
        if ($timestamp == null) {
            $timestamp = time();
        }
        return date($format, $timestamp);
    }

    /**
     * getStartAndEndDate
     * @param type $week
     * @param type $year
     * @return type
     */
    public function getStartAndEndDate($week) {
        $dto = new DateTime();
        $dto->setISODate($this->now('Y'), $week);
        $ret['week_start'] = $dto->format('Y-m-d');
        $dto->modify('+6 days');
        $ret['week_end'] = $dto->format('Y-m-d');
        return $ret;
    }

    /**
     * isValidTimeStamp
     * @param string $timestamp
     * @return boolean
     */
    public function isValidTimeStamp(string $timestamp) {
        try {
            new DateTime('@' . $timestamp);
            return ((string) (int) $timestamp === $timestamp) &&
                    ($timestamp <= PHP_INT_MAX) && ($timestamp >= ~PHP_INT_MAX);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Checks validity of the date
     * @param DateTime $futureDate
     * @param DateTime $startDate
     * @return type
     */
    public function isDateBetweenDates($futureDate, $startDate) {
        $futureDate = new DateTime($futureDate);
        $startDate = new DateTime($startDate);

        return $futureDate > $startDate;
    }

    /**
     * validateDate
     * @param type $birthDate
     * @return type
     */
    public function validateDate($birthDate) {
        $validateFlag = true;
        $convertBirthDate = DateTime::createFromFormat('Y-m-d H:i:s', $birthDate);
        $birthDateErrors = DateTime::getLastErrors();

        $status = "";
        if ($birthDateErrors['warning_count'] + $birthDateErrors['error_count'] > 0) {
            $status = "The date format is wrong.";
        } else {
            $testBirthDate = explode('-', $birthDate);
            if ($testBirthDate[0] < 1900) {
                $validateFlag = false;
                $status = "We suspect that you did not born before XX century.";
            }
        }

        return ['status' => $validateFlag, 'desc' => $status];
    }

    /**
     * getAge
     * @param type $date
     * @return type
     */
    public function getAge($date) {
        $age = date('Y') - $date;
        if (date('md') < date('md', strtotime($date))) {
            return $age - 1;
        }
        return $age;
    }

    /**
     * DayDiff
     * @param type $last_won
     * @return type
     */
    protected function DayDiff($from_date, $to_date) {
//Convert it into a timestamp.
        $from_date = strtotime($from_date);
        $to_date = strtotime($to_date);

//Calculate the difference.
        $difference = $to_date - $from_date;

//Convert seconds into days.
        $days = floor($difference / (60 * 60 * 24));

        return $days;
    }

    /**
     * MinuteDiff
     * @param type $from_date
     * @param type $to_date
     * @return type
     */
    public function MinuteDiff($from_date, $to_date) {
//Convert it into a timestamp.
        $from_date = strtotime($from_date);
        $to_date = strtotime($to_date);

//Calculate the difference.
        $difference = $to_date - $from_date;

//Convert seconds into days.
        $days = floor($difference / (60 * 24));

        return $days;
    }

    /**
     * dayDifference
     * @param type $last_won
     * @return type
     */
    public function dayDifference($last_won) {
//Convert it into a timestamp.
        $last_won = strtotime($last_won);

//Get the current timestamp.
        $now = time();

//Calculate the difference.
        $difference = $now - $last_won;

//Convert seconds into days.
        $days = floor($difference / (60 * 60 * 24));

        return $days;
    }

    /**
     * truncate
     * @param type $string
     * @param type $length
     * @param type $dots
     * @return type
     */
    public function truncate($string, $length, $dots = "...") {
        return (strlen($string) > $length) ? substr($string, 0, $length - strlen($dots)) . $dots : $string;
    }

    /**
     * CheckValidateDate
     * @param type $date
     * @param type $format
     * @return type
     */
    public function CheckValidateDate($date, $format = 'Y-m-d H:i:s') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) == $date;
    }

    /**
     * @param string $haystack
     * @param string $needle
     * @return bool
     */
    public function startsWith($haystack, $needle) {
        $haystack = strtolower($haystack);
        $needle = strtolower($needle);

        $length = strlen($needle);
        return (substr($haystack, 0, $length) === $needle);
    }

    /**
     * @param string $haystack
     * @param string $needle
     * @return bool
     */
    public function endsWith($haystack, $needle) {
        $length = strlen($needle);
        return $length === 0 || (substr($haystack, -$length) === $needle);
    }

    /**
     * validateURL
     * @param type $url
     * @return boolean
     */
    public function validateURL($url) {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        return TRUE;
    }

    /**
     * validateEmail
     * @param type $email
     * @return boolean
     */
    public function validateEmail($email) {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }

        return TRUE;
    }

    /**
     * rawInsertBulk
     * @param type $db
     * @param type $table
     * @param type $table_data
     * @return type
     * @throws \Exception
     */
    public function rawInsertBulk($db, $table, $table_data, $replace = false) {
        $startTime = microtime(true);

        $arr = [];
        $sql = '';
        try {
            $values = '';
            $fields = '';


            foreach ($table_data as $k => $v) {
                $fields .= ',' . $k;
                $values .= ",:$k";
                $arr[":$k"] = $v;
            }

            $values = substr($values, 1);
            $fields = substr($fields, 1);

            $replaceTxt = 'INSERT';
            if ($replace) {
                $replaceTxt = 'REPLACE';
            }

            $sql = "$replaceTxt INTO $table ($fields) VALUES ($values)";

            $connection = $this->di->getShared($db);
            $connection->execute($sql, $arr);
            $insert = $connection->lastInsertId();

            $executionTime = (microtime(true) - $startTime);
            if ($executionTime > 1) {
                $this->getLogFile('fatal')->debug(__LINE__ . ":" . __CLASS__
                        . " | Took " . $executionTime . " Sec"
                        . " | inserting into $table"
                        . " | SQL:$sql");
            }

            return $insert;
        } catch (\Exception $e) {
            $this->getLogFile('fatal')->emergency(__LINE__ . ":" . __CLASS__
                    . " | INSERT_QUERY::$sql"
                    . " | Params:" . json_encode($arr)
                    . " | Exception::" . $e->getTraceAsString()
                    . " | Message:" . $e->getMessage());

            throw $e;
        }
    }

    /**
     * Mysql Function
     */

    /**
     * rawInsert
     * @param type $db
     * @param type $phql
     * @param type $params
     * @return type
     * @throws Exception
     */
    public function rawInsert($db, $phql, $params = null) {
        try {
            $connection = $this->di->getShared($db);
            $connection->execute($phql, $params);
            $last_insert_id = $connection->lastInsertId();
            return $connection->lastInsertId();
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * rawUpdate
     * @param type $db
     * @param type $statement
     * @return type
     * @throws Exception
     */
    public function rawUpdateWithParams($db, $statement, $params = null) {
        try {
            $connection = $this->di->getShared($db);
            $success = $connection->execute($statement, $params);
            return $connection->affectedRows();
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * rawSelect
     * @param type $db
     * @param type $statement
     * @param type $params
     * @return type
     * @throws Exception
     */
    public function rawSelect($db, $statement, $params = null) {
        try {
            $connection = $this->di->getShared($db);
            $success = $connection->query($statement, $params);
            $success->setFetchMode(((PHALCON_VERSION == 3) ?
                            Phalcon\Db::FETCH_ASSOC :
                            Phalcon\Db\Enum::FETCH_ASSOC));
            return $success->fetchAll($success);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * ReadOnlySelect
     * @param type $sql
     * @return type
     */
    public function ReadOnlySelect($db, $statement, $params = null) {
        try {
            $connection = $this->di->getShared($db);
            $success = $connection->query($statement, $params);
            $success->setFetchMode((PHALCON_VERSION == 3) ?
                            Phalcon\Db::FETCH_ASSOC :
                            Phalcon\Db\Enum::FETCH_ASSOC);

            return $success->fetchAll($success);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * rawSelectOneRecord
     * @param type $sql
     * @return type
     */
    public function rawSelectOneRecord($db, $sql, $params = null) {
        try {
            $connection = $this->di->getShared($db);
            return $connection->fetchOne($sql, ((PHALCON_VERSION == 3) ?
                            \Phalcon\Db::FETCH_ASSOC :
                            \Phalcon\Db\Enum::FETCH_ASSOC), $params);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * GenerateSql
     * @param type $params
     * @param type $table
     * @return type
     */
    public function GenerateSql($params, $table) {
        $arr = [];
        $values = '';
        $fields = '';
        foreach ($params as $k => $v) {
            $fields .= ',' . $k;
            $values .= ",:$k";
            $arr[":$k"] = $v;
        }

        $values = substr($values, 1);
        $fields = substr($fields, 1);

        return [
            'sql' => "INSERT INTO $table ($fields) VALUES ($values)",
            'sql_arr' => $arr];
    }

    /**
     * selectQuery
     * @param type $db
     * @param type $sql
     * @param type $params
     * @return type
     * @throws Exception
     */
    public function selectQuery($db, $sql, $params = null) {
        try {
            if (!in_array($db, ['dbTransactionsRead', 'dbProfileRead', 'dbCasinoRead',
                        'dbJpRead', 'dbVirtualsRead', 'dbBoxCon', 'dbTornamentRead'])) {
                throw new Exception('InValid Database Connection string', 4001);
            }

            $connection = $this->di->getShared($db);
            $success = $connection->query($sql, $params);
            $success->setFetchMode(((PHALCON_VERSION == 3) ?
                            \Phalcon\Db::FETCH_ASSOC :
                            \Phalcon\Db\Enum::FETCH_ASSOC));
            return $success->fetchAll($success);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * tableQueryBuilder
     * @param type $sort
     * @param type $order
     * @param int $page
     * @param int $limit
     * @param type $groupBy
     * @return type
     */
    public function tableQueryBuilder($sort = "", $order = "", $page = 0, $limit = 10, $groupBy = "") {

        $orderBy = $sort ? "ORDER BY $sort $order" : "";

        $sortClause = "$groupBy $orderBy";

        if (!$page || $page <= 0) {
            $page = 1;
        }
        if (!$limit) {
            $limit = 10;
        }

        $offset = (int) ($page - 1) * $limit;
        $limitQuery = "LIMIT $offset, $limit";

        return "$sortClause $limitQuery";
    }

    /**
     * whereQuery
     * @param type $whereArray
     * @param type $groupBy
     * @param type $searchColumns
     * @return type
     */
    public function whereQuery($whereArray, $groupBy = null, $searchColumns = []) {

        $whereQuery = "";
        $havingQuery = "";

        foreach ($whereArray as $key => $value) {

            if ($key == 'filter') {
                $valueString = "";
                foreach ($searchColumns as $searchColumn) {
                    $valueString .= $value ? "" . $searchColumn . " REGEXP '" . $value . "' ||" : "";
                }
                $valueString = chop($valueString, " ||");
                if ($valueString) {
                    $valueString = "(" . $valueString;
                    $valueString .= ") AND ";
                }
                $whereQuery .= $valueString;
            } else if ($key == 'having') {
                if (!empty($value[1]) && !empty($value[2])) {
                    $valueString = " $value[0] between $value[1] AND $value[2] AND ";
                    $havingQuery .= $valueString;
                }
            } else if (is_array($value)) {
                $type = isset($value[3]) ? $value[3] : 1;

                if (!empty($value[1]) && !empty($value[2])) {
                    $valueString = $type == 1 ? " $value[0] between '$value[1]' AND '$value[2]' AND " : " $value[0] between $value[1] AND $value[2] AND ";
                    $whereQuery .= $valueString;
                }
            } else {
                $valueString = $value ? "" . $key . "=" . $value . " AND " : "";
                $whereQuery .= $valueString;
            }
        }

        if ($whereQuery) {
            $whereQuery = chop($whereQuery, " AND ");
        }

        if ($havingQuery) {
            $havingQuery = chop($havingQuery, " AND ");
        }

        $whereQuery = $whereQuery ? "WHERE $whereQuery " : "";
        $havingQuery = $havingQuery ? " HAVING $havingQuery " : "";

        return $whereQuery . $groupBy . $havingQuery;
    }

    /**
     *
     * This function is searches through a string and try to find any placeholder variables,
     *  which would be place between two curly brackets {}. It grabs the value between the
     *  curly brackets and uses it to look through an array where it should match the key.
     *  Then it replaces the curly bracket variable in the string with the value in the
     *  array of the matching key.
     *
     * @param $template - string with placeholders
     * @param $data - replaceble values in an array
     */
    public function SMSTemplate($template, $data) {
        return strtr($template, $data);
    }

    /**
     * formatMobileNumber
     * @param type $mobile
     * @return type
     */
    public function formatMobileNumber($mobile, $dial_code = false) {

        if (!$dial_code) {
            $dial_code = $this->settings['mnoApps']['DefaultDialCode'];
        }

        $mobile = str_replace("+", "", $mobile);
        $mobile = preg_replace('/[\t\n\r\s]+/', '', $mobile);
        $mobile = preg_replace('/\s+/', '', $mobile);
        $mobile = preg_replace('~\D~', '', $mobile);
        $input = substr($mobile, 0, -strlen($mobile) + 1);

        $number = '';
        if ($input == '0') {
            $number = substr_replace($mobile, $dial_code, 0, 1);
        } elseif ($input == '+') {
            $number = substr_replace($mobile, $dial_code, 0, 1);
        } elseif ($input == '7') {
            $number = substr_replace($mobile, $dial_code . '7', 0, 1);
        } elseif ($input == '1') {
            $number = substr_replace($mobile, $dial_code . '1', 0, 1);
        } elseif ($input == '2' && (strlen($input) == 9)) {
            $number = substr_replace($mobile, $dial_code . '2', 0, 1);
        } else {
            $number = $mobile;
        }

        if (strlen($number) != 12) {
            return false;
        }

        if (in_array(substr($number, 0, 4),
                        [$this->settings['mnoApps']['DefaultDialCode'] . "1",
                            $this->settings['mnoApps']['DefaultDialCode'] . '2'])) {
            if (strlen($number) == 12) {
                return $number;
            }
        }

        return $number;
    }

    /**
     * validateMobile
     * @param type $number
     * @return boolean
     */
    public function validateMobile($number, $dialCode = null) {
        if (in_array(substr($number, 0, 4), ["2541", '2542'])) {
            if (strlen($number) == 12) {
                return $number;
            }
        }

        $regex = '/^(?:\+?(?:[1-9]{3})|0)?7([0-9]{8})$/';
        if (preg_match_all($regex, $number, $capture)) {
            $msisdn = $dialCode . '7' . $capture[1][0];
        } else {
            $msisdn = false;
        }

        return $msisdn;
    }

    /**
     * Gets the Mobile operator Network
     * @param type $MSISDN
     * @return type
     */
    public function getMobileNetwork($MSISDN, $dial_code = null) {
        if (!$dial_code) {
            $dial_code = substr($MSISDN, 0, 4);
        }

        if (!$this->validateMobile($MSISDN, $dial_code)) {
            return $network = 'UNKNOWN';
        }

        $network = "";
        $countryCode = substr($MSISDN, 0, 3);
        $mnoCode = substr($MSISDN, 3, 2);
        $mnoCode1 = substr($MSISDN, 3, 3);
        switch ($countryCode) {
            case 254://Kenya
                switch ($mnoCode) {
                    case 70:
                    case 71:
                    case 72:
                    case 74:
                    case 79:
                    case 11:
                    case 12:
                    case 13:
                    case 14:
                    case 15:
                        $network = 'SAFARICOM.KE';
                        break;
                    case 73:
                    case 78:
                    case 75:
                    case 10:
                        $network = 'UNKNOWN';
                        $countryCode = substr($MSISDN, 0, 6);
                        if ($countryCode == '254757' || $countryCode == '254758' || $countryCode == '254759') {
                            $network = 'SAFARICOM.KE';
                        }
                        break;
//                    case 77:
//                    case 20:
//                        $network = 'TELKOM.KE';
//                        break;
                    case 76:
                        $network = 'UNKNOWN';
                        $countryCode = substr($MSISDN, 0, 6);
                        if ($countryCode == '254768' || $countryCode == '254769') {
                            $network = 'SAFARICOM.KE';
                        }

                        if ($countryCode == '254762') {
                            $network = 'AIRTEL.KE';
                        }
                        break;
                    default:
                        $network = 'UNKNOWN';
                        break;
                }
                break;
            default:
                $network = 'UNKNOWN';
                break;
        }
        return $network;
    }



    /**
     * sendRawPostData
     * @param type $postUrl
     * @param type $postData
     * @param type $token
     * @param type $headers
     * @return type
     */
    public function sendRawPostData($postUrl, $postData, $headers = []) {
        $httpRequest = curl_init($postUrl);
        curl_setopt($httpRequest, CURLOPT_NOBODY, true);
        curl_setopt($httpRequest, CURLOPT_POST, true);;

        $contentType = null;
        foreach ($headers as $header) {
            if (stripos($header, 'Content-Type') !== false) {
                list($key, $value) = explode(":", $header);
                $contentType = trim($value);
                break;
            }
        }

        if ($contentType === 'application/x-www-form-urlencoded') {
            curl_setopt($httpRequest, CURLOPT_POSTFIELDS, http_build_query($postData));
        } else {
            curl_setopt($httpRequest, CURLOPT_POSTFIELDS, json_encode($postData));
        }

        curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($httpRequest, CURLOPT_TIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_CONNECTTIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_USERAGENT, $this->settings['appName'] . "/1.0");
        //accept SSL settings
        curl_setopt($httpRequest, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($httpRequest);
        $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
        $curlError = curl_error($httpRequest);
        curl_close($httpRequest);

        return ["statusCode" => $status, "response" => $response, 'error' => $curlError];
    }

    /**
     *
     * @param type $len
     * @return string
     */
    public function randStrGen($len) {
        $result = "";
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        $charArray = str_split($chars);
        for ($i = 0; $i < $len; $i++) {
            $randItem = array_rand($charArray);
            $result .= "" . $charArray[$randItem];
        }
        return strtoupper($result);
    }

    /**
     * getClientIPServer
     * @return string
     */
    public function getClientIPServer() {
        $ipaddress = '';
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else if (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        } else if (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } else if (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        } else if (isset($_SERVER['REMOTE_ADDR'])) {
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        } else {
            $ipaddress = 'UNKNOWN';
        }

        return $ipaddress;
    }

    /**
     * Gets the log file to use
     * @param type $action
     * @return type
     */
    public function getLogFile($action = "") {
        $logger = '';
        /**
         * Read the configuration
         */
        $logPathLocation = $this->logPath['location'];
        $dateFormat = $this->logPath['dateFormat'];
        $output = $this->logPath['output'];
        $filename = $this->settings['systemName'];

        switch ($action) {
            case 'queue_error':
                $streamFile = $logPathLocation . "TasksError.log";
                $stream = new StreamHandler($streamFile, Logger::ERROR);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('ERROR');
                $logger->pushHandler($stream);
                break;
            case 'queue':
                $streamFile = $logPathLocation . "TasksInfo.log";
                $stream = new StreamHandler($streamFile, Logger::ERROR);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('INFO');
                $logger->pushHandler($stream);
                break;
            case 'ussd_info':
                $streamFile = $logPathLocation . "USSDInfo.log";
                $stream = new StreamHandler($streamFile, Logger::ERROR);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('INFO');
                $logger->pushHandler($stream);
                break;
            case 'ussd_error':
                $streamFile = $logPathLocation . "USSDError.log";
                $stream = new StreamHandler($streamFile, Logger::ERROR);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('ERROR');
                $logger->pushHandler($stream);
                break;
            case 'info':
                $streamFile = $logPathLocation . "" . $filename . "Info.log";
                $stream = new StreamHandler($streamFile, Logger::INFO);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('INFO');
                $logger->pushHandler($stream);
                break;
            case 'error':
                $streamFile = $logPathLocation . "" . $filename . "Error.log";
                $stream = new StreamHandler($streamFile, Logger::ERROR);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('ERROR');
                $logger->pushHandler($stream);
                break;
            case 'fatal':
                $streamFile = $logPathLocation . "" . $filename . "Fatal.log";
                $stream = new StreamHandler($streamFile, Logger::EMERGENCY);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('FATAL');
                $logger->pushHandler($stream);
                break;
            case 'debug':
                $streamFile = $logPathLocation . "" . $filename . "Debug.log";
                $stream = new StreamHandler($streamFile, Logger::DEBUG);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('DEBUG');
                $logger->pushHandler($stream);
                break;
            default:
                $streamFile = $logPathLocation . "" . $filename . "Api.log";
                $stream = new StreamHandler($streamFile, Logger::INFO);
                $stream->setFormatter(new LineFormatter($output, $dateFormat));
                $logger = new Logger('INFO');
                $logger->pushHandler($stream);
                break;
        }

        return $logger;
    }

    /**
     * ReferenceNumber
     * @param type $uniqueId
     * @return type
     */
    function ReferenceNumber($uniqueId = false) {
        $referenceId = "";

        $year = [
            '2022' => 'Z', '2023' => 'Y', '2024' => 'X', '2025' => 'W',
            '2026' => 'V', '2027' => 'U', '2028' => 'T', '2029' => 'S',
            '2030' => 'R', '2031' => 'Q', '2032' => 'O', '2033' => 'N'];
        $referenceId .= $year[$this->now('Y')];

        $month = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L'];
        $referenceId .= $month[date('m') - 1];

        $d = (int) $this->now('d');
        if (($d >= 1) || ($d <= 10)) {
            $referenceId .= $d;
        } else {
            $referenceId .= strtoupper($this->num2alpha($d));
        }

        $referenceId .= strtoupper($this->num2alpha(((int) $this->now('H'))));
        $referenceId .= strtoupper($this->num2alpha(((int) $this->now('s'))));

        $rand = (strlen($referenceId) < 10) ? (10 - strlen($referenceId)) : 3;
        $referenceId .= substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstvwxyz', 36)), 0, $rand);

        return strtoupper($referenceId);
    }

    /**
     * AlphaNumericIdGenerator
     * @param type $input
     * @return type
     */
    public function AlphaNumericIdGenerator($input) {
        $alpha_array = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"
            , "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"];
        $number_array = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        $output = "";

        for ($i = 0; $i <= 5; $i++) {
            if ($i >= 4) {
                $divisor = pow(26, $i - 3) * pow(10, 3);
            } else {
                $divisor = pow(10, $i);
            }

            $pos = floor($input / $divisor);
            if ($i >= 3) {
                $digit = $pos % 26;
                $output .= $alpha_array[abs($digit)];
            } else {
                $digit = $pos % 10;
                $output .= $number_array[abs($digit)];
            }
        }

        return strrev($output);
    }

    /**
     * Converts an integer into the alphabet base (A-Z).
     *
     * @param int $n This is the number to convert.
     * @return string The converted number.
     * <AUTHOR>
     *
     */
    function num2alpha($n) {
        $r = '';
        for ($i = 1; $n >= 0 && $i < 10; $i++) {
            $r = chr(0x41 + ($n % pow(26, $i) / pow(26, $i - 1))) . $r;
            $n -= pow(26, $i);
        }
        return $r;
    }

    /**
     * Converts an alphabetic string into an integer.
     *
     * @param int $n This is the number to convert.
     * @return string The converted number.
     * <AUTHOR>
     *
     */
    function alpha2num($a) {
        $r = 0;
        $l = strlen($a);
        for ($i = 0; $i < $l; $i++) {
            $r += pow(26, $i) * (ord($a[$l - $i - 1]) - 0x40);
        }
        return $r - 1;
    }

    /**
     * SendHttpJsonPostData
     * @param type $postUrl
     * @param type $postData
     * @param type $postHeaders
     * @return type
     */
    public function SendHttpJsonPostData($postUrl, $postData, $postHeaders = []) {
        $postData = json_encode($postData);

        $postHeaders[] = 'Content-Length: ' . strlen($postData);
        $postHeaders[] = 'Content-Type: application/json';

        $httpRequest = curl_init($postUrl);
        curl_setopt($httpRequest, CURLOPT_NOBODY, true);
        curl_setopt($httpRequest, CURLOPT_POST, true);
        curl_setopt($httpRequest, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $postHeaders);
        curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($httpRequest, CURLOPT_TIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_CONNECTTIMEOUT, $this->settings['timeoutDuration']);
        //accept SSL settings
        curl_setopt($httpRequest, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($httpRequest, CURLOPT_USERAGENT, $this->settings['appName'] . "/3.0");

        $results = curl_exec($httpRequest);
        $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
        $curlError = curl_error($httpRequest);
        curl_close($httpRequest);

        return [
            "statusCode" => isset($status) ? $status : 0,
            "response" => $results,
            'error' => $curlError];
    }

    /**
     * SendHttpGetRequest
     * @param type $url
     * @param type $postData
     * @param type $headers
     * @return type
     */
    public function SendHttpGetRequest($postUrl, $postData, $headers) {
        $postHeaders[] = 'X-Service-Key: ' . md5($this->settings['Authentication']['HttpRequestKey']);
        $postHeaders[] = 'X-Hash-Key: ' . md5(json_encode($postData) . '' . $this->settings['Authentication']['HttpRequestKey']);

        $httpRequest = curl_init($postUrl . '?' . http_build_query($postData));
        curl_setopt($httpRequest, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($httpRequest, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($httpRequest, CURLOPT_ENCODING, "");
        curl_setopt($httpRequest, CURLOPT_TIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_CONNECTTIMEOUT, $this->settings['timeoutDuration']);
        curl_setopt($httpRequest, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);

        //accept SSL settings
        curl_setopt($httpRequest, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($httpRequest, CURLOPT_USERAGENT, $this->settings['appName'] . "/3.0");
        $response = curl_exec($httpRequest);
        $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
        curl_close($httpRequest);

        return [
            'statusCode' => isset($status) ? $status : 0,
            'response' => $response];
    }

    /**
     * HttpReason
     * @param type $code
     * @return type
     */
    public function HttpReason($code) {
        $arr = [
            '200' => 'OK',
            '201' => 'CREATED',
            '202' => 'ACCEPTED',
            '204' => 'NO CONTENT',
            '304' => 'NOT MODIFIED',
            '400' => 'BAD REQUEST',
            '401' => 'UNAUTHORIZED REQUEST',
            '402' => 'PAYMENT REQUIRED',
            '403' => 'REQUEST FORBIDDEN',
            '404' => 'NOT FOUND',
            '408' => 'REQUEST TIMED-OUT',
            '405' => 'METHOD NOT ALLOWED',
            '421' => 'MISDIRECTED REQUEST',
            '422' => 'UNPROCESSABLE ENTITY',
            '500' => 'INTERNAL SERVER ERROR',
            '502' => 'BAD GATEWAY',];

        return isset($arr[$code]) ? $arr[$code] : false;
    }

    /**
     * clean variable string
     *
     * @param type $text
     * @return type
     */
    public function cleanStr($text) {
        $utf8 = array(
            '/[áàâãªä]/u' => 'a',
            '/[ÁÀÂÃÄ]/u' => 'A',
            '/[ÍÌÎÏ]/u' => 'I',
            '/[íìîï]/u' => 'i',
            '/[éèêë]/u' => 'e',
            '/[ÉÈÊË]/u' => 'E',
            '/[óòôõºö]/u' => 'o',
            '/[ÓÒÔÕÖ]/u' => 'O',
            '/[úùûü]/u' => 'u',
            '/[ÚÙÛÜ]/u' => 'U',
            '/ç/' => 'c',
            '/Ç/' => 'C',
            '/ñ/' => 'n',
            '/Ñ/' => 'N',
            '/–/' => '-', // UTF-8 hyphen to "normal" hyphen
            '/[’‘‹›‚]/u' => ' ', // Literally a single quote
            '/[“”«»„]/u' => ' ', // Double quote
            '/ /' => ' ', // nonbreaking space (equiv. to 0x160)
        );

        $string = preg_replace(array_keys($utf8), array_values($utf8), $text);
        $string = str_replace("'", '', $string);
        $string = str_replace(" ", '', $string);
        $string = str_replace('porn', '', strtolower($string));
        $string = str_replace('porno', '', strtolower($string));
        $string = str_replace('sex', '', strtolower($string));
        $string = str_replace('fcuk', '', strtolower($string));
        $string = str_replace('fuck', '', strtolower($string));
        $string = str_replace('drop', '', strtolower($string));
        $string = str_replace('delete', '', strtolower($string));
        //  $string = str_replace('update', '', strtolower($string));
        $string = str_replace('alter', '', strtolower($string));
        $string = stripslashes($string);
        // $string = htmlspecialchars($string);
        $string = strtoupper($string);

        return preg_replace('/[[:^print:]]/', '', trim($string));
    }

}
