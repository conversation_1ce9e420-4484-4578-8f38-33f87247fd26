<?php

/**
 * Description of BonusController
 *
 * <AUTHOR>
 */
class BonusController extends \ControllerBase
{

    /**
     * CreatePromotions
     * @return type
     */
    function CreatePromotions()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "Create new system promotion";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $promo_name = $data['promo_name'] ?? false;
        $promo_url = $data['promo_url'] ?? false;
        $promo_details = $data['promo_details'] ?? false;
        $promo_images = $data['promo_images'] ?? false;
        $promo_starting_date = $data['starting_date'] ?? false;
        $promo_ending_date = $data['ending_date'] ?? false;
        $promotion_type_id = $data['promotion_type_id'] ?? false;
        $bonus_data = $data['bonus_data'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$promo_name
            || !$promo_url || !$promo_starting_date || !$promo_ending_date || !$promotion_type_id) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$this->validateURL($promo_url)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 400,
                    'message' => 'Invalid promotion URL!'], true);
        }

        $bonus_data = (array)$bonus_data;

        $min_odds = $data['min_odds'] ?? false;

        if (!is_numeric($min_odds) || ($min_odds < 1)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 400,
                    'message' => 'Invalid Minimum odds!'], true);
        }

        $min_odds_per_pick = $data['min_odds_per_pick'] ?? false;

        $bet_count = $data['bet_count'] ?? 1;

        $min_stake = $data['min_stake'] ?? false;


        $max_times = $data['max_times'] ?? false;

        $max_win = $data['max_win'] ?? false;

        $frequency = $data['frequency'] ?? false;

        $expiry_period = $data['expiry_period'] ?? false;
        if ($expiry_period < 1) {
            $expiry_period = 48; //hours
        }

        $deduct_stake = (int)($data['deduct_stake'] ?? 0);

        $bonus_amount = $data['bonus_amount'] ?? 0;

        $min_selections = $data['min_selections'] ?? false;

        $allow_duplicate_events = (int)($data['allow_duplicate_events'] ?? 0);

        if (is_array($promo_images)) {
            $promo_images = explode(",", $promo_images);
        }

        $restrict_withdrawals = (bool)($data['restrict_withdrawals'] ?? true);

        $allow_duplicate_events_per_ip = (bool)($data['allow_duplicate_events_per_ip'] ?? true);

        $market_conditions = $data['market_conditions'] ?? true;

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $res = $this->rawSelectOneRecord('dbBonus', "SELECT * FROM promotion_type "
                . "WHERE id=:id AND status=1", [':id' => $promotion_type_id]);
            if (!$res) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => 'Invalid promotion type!'], true);
            }

            $promo = $this->rawSelectOneRecord('dbBonus',
                "SELECT id,promo_name FROM promotion WHERE promo_type_id=:promo_type_id "
                . "AND promo_name=:promo_name AND status=1",
                [':promo_type_id' => $res['id'],
                    ':promo_name' => $promo_name]);

            $isNewPromotion = false;
            if (!$promo) {
                $promo['promo_name'] = $promo_name;

                // Get next available ID
                $nextId = $this->rawSelectOneRecord('dbBonus',
                    "SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM promotion");

                $promo['id'] = $this->rawInsertBulk('dbBonus',
                    "promotion",
                    ['id' => $nextId['next_id'],
                        'promo_type_id' => $res['id'],
                        'promo_name' => $promo_name,
                        'promo_url' => $promo_url,
                        'promo_details' => $promo_details,
                        'promo_images' => $promo_images,
                        'status' => 1,
                        'starting_date' => $promo_starting_date,
                        'ending_date' => $promo_ending_date,
                        'created_at' => $this->now()]);
                $isNewPromotion = true;
            }

            // Check if promotion_settings already exists for this promotion
            $existingSettings = $this->rawSelectOneRecord('dbBonus',
                "SELECT id FROM promotion_settings WHERE promo_id=:promo_id AND status=1",
                [':promo_id' => $promo['id']]);

            if ($existingSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 409,
                        'message' => 'Promotion settings already exist for this promotion!'], true);
            }

            // Validate promo_id before proceeding
            if (!$promo['id'] || $promo['id'] <= 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 500,
                        'message' => 'Invalid promotion ID generated!'], true);
            }

            // Get next available ID
            $nextId2 = $this->rawSelectOneRecord('dbBonus',
                "SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM promotion_settings");

            $promotionSettingsData = [
                'id' => $nextId2['next_id'],
                'promo_id' => $promo['id'],
                'bonus_amount' => $bonus_amount ?: 0,
                'bonus_type' => $bonus_data['bonus_type'] ?? null,
                'bet_count' => $bet_count ?: 1,
                'min_odds' => $min_odds ?: null,
                'min_odds_per_pick' => $min_odds_per_pick ?: null,
                'min_stake' => $min_stake ?: null,
                'max_times' => $max_times ?: null,
                'max_win' => $max_win ?: null,
                'frequency' => $frequency ?: null,
                'deduct_stake' => $deduct_stake,
                'allow_duplicate_events' => $allow_duplicate_events,
                'expiry_period' => $expiry_period ?: 48, // Default to 48 hours if not provided
                'restrict_withdrawals' => $restrict_withdrawals ?? 0,
                'market_conditions' => is_array($market_conditions) ? json_encode($market_conditions) : ($market_conditions ?: null),
                'status' => 1,
                'min_selections' => $min_selections ?: null,
                'created_at' => $this->now()
            ];

            // Filter out false values and replace with null for optional fields
            foreach ($promotionSettingsData as $key => $value) {
                if ($value === false && !in_array($key, ['deduct_stake', 'allow_duplicate_events', 'restrict_withdrawals', 'status'])) {
                    $promotionSettingsData[$key] = null;
                }
            }

            $id = $this->rawInsertBulk('dbBonus', 'promotion_settings', $promotionSettingsData);

            $logs = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 201,
                    'message' => $promo_name . ' promotion created successfully'], true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * EditPromotions
     * @param type $promoId
     * @return type
     */

    function EditPromotions($promoId)
    {
        $start_time = $this->getMicrotime();

        $permissionName = "Update system promotion";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request EditPromotions:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $promoId = $data['promo_id'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        // Validate Inputs
        $promo_name = isset($data['promo_name']) ? trim($data['promo_name']) : null;
        $promo_url = isset($data['promo_url']) ? trim($data['promo_url']) : null;
        $promo_details = isset($data['promo_details']) ? trim($data['promo_details']) : null;
        $promo_images = $data['promo_images'] ?? null;
        $promo_starting_date = $data['starting_date'] ?? null;
        $promo_ending_date = $data['ending_date'] ?? null;
        $promotion_type_id = $data['promotion_type_id'] ?? null;
        $promotion_type_id = $data['promo_type_id'] ?? null;

        // Bonus Data - Extract from root level of request with proper null/empty handling
        $bonus_amount = isset($data['bonus_amount']) && $data['bonus_amount'] !== '' ? $data['bonus_amount'] : null;
        $bonus_type = isset($data['bonus_type']) && $data['bonus_type'] !== '' ? $data['bonus_type'] : null;
        $bet_count = isset($data['bet_count']) && $data['bet_count'] !== '' ? (int)$data['bet_count'] : null;
        $min_odds = isset($data['min_odds']) && $data['min_odds'] !== '' ? $data['min_odds'] : null;
        $min_odds_per_pick = isset($data['min_odds_per_pick']) && $data['min_odds_per_pick'] !== '' ? $data['min_odds_per_pick'] : null;
        $min_stake = isset($data['min_stake']) && $data['min_stake'] !== '' ? $data['min_stake'] : null;
        $max_times = isset($data['max_times']) && $data['max_times'] !== '' ? (int)$data['max_times'] : null;
        $max_win = isset($data['max_win']) && $data['max_win'] !== '' ? $data['max_win'] : null;
        $frequency = isset($data['frequency']) && $data['frequency'] !== '' ? $data['frequency'] : null;
        $deduct_stake = isset($data['deduct_stake']) && $data['deduct_stake'] !== '' ? (int)$data['deduct_stake'] : null;
        $allow_duplicate_events = isset($data['allow_duplicate_events']) && $data['allow_duplicate_events'] !== '' ? (int)$data['allow_duplicate_events'] : null;
        $expiry_period = isset($data['expiry_period']) && $data['expiry_period'] !== '' ? (int)$data['expiry_period'] : null;
        $min_selections = isset($data['min_selections']) && $data['min_selections'] !== '' ? (int)$data['min_selections'] : null;
        $restrict_withdrawals = isset($data['restrict_withdrawals']) && $data['restrict_withdrawals'] !== '' ? (int)$data['restrict_withdrawals'] : null;
//        $market_conditions = isset($data['market_conditions']) && $data['market_conditions'] !== '' ? $data['market_conditions'] : null;
        $status = isset($data['status']) && $data['status'] !== '' ? (int)$data['status'] : null;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        /*  if (!$this->validateURL($promo_url)) {
              return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                  200,
                  'Request is not successful',
                  ['code' => 400,
                      'message' => 'Invalid promotion URL!'], true);
          }*/

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $promo = $this->rawSelectOneRecord(
                'dbBonus',
                "SELECT * FROM promotion WHERE id=:id AND status=1", [':id' => $promoId]
            );

            if (!$promo) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => 'Promotion not found!'], true);
            }

            // Get the promotion settings for this promotion
            $promoSettings = $this->rawSelectOneRecord(
                'dbBonus',
                "SELECT * FROM promotion_settings WHERE promo_id=:promo_id AND status=1", [':promo_id' => $promoId]
            );

            // Build Update Query Dynamically
            $updateParams = [];
            $setClauses = [];

            if ($promo_name) {
                $setClauses[] = "promo_name = :promo_name";
                $updateParams[':promo_name'] = $promo_name;
            } else {
                $promo_name = $promo['promo_name'];
            }
            if ($promo_url) {
                $setClauses[] = "promo_url = :promo_url";
                $updateParams[':promo_url'] = $promo_url;
            }
            if ($promo_details) {
                $setClauses[] = "promo_details = :promo_details";
                $updateParams[':promo_details'] = $promo_details;
            }
            if ($promo_images) {
                $setClauses[] = "promo_images = :promo_images";
                $updateParams[':promo_images'] = implode(",", (array)$promo_images);
            }
            if ($promo_starting_date) {
                $setClauses[] = "starting_date = :starting_date";
                $updateParams[':starting_date'] = $promo_starting_date;
            }
            if ($promo_ending_date) {
                $setClauses[] = "ending_date = :ending_date";
                $updateParams[':ending_date'] = $promo_ending_date;
            }
            if ($promotion_type_id) {
                $setClauses[] = "promo_type_id = :promo_type_id";
                $updateParams[':promo_type_id'] = $promotion_type_id;
            }
            if (isset($data['status'])) {
                $setClauses[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            // Ensure there's something to update
            if (!empty($setClauses)) {
                $setClauseString = implode(", ", $setClauses);
                $updateParams[':promo_id'] = $promoId;
                //add updated_at
                $setClauseString .= ", updated_at = NOW()";
                $updateQuery = "UPDATE promotion SET $setClauseString WHERE id = :promo_id";

                $res = $this->rawUpdateWithParams('dbBonus', $updateQuery, $updateParams);

                if (!$res) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 409,
                            'message' => "Update Failed!"], true);
                }
            }

            $updateParams = [];
            $setClauses = [];

            // Bonus Data Fields - Use proper validation to handle empty values and data types
            if (isset($data['bonus_amount']) && $data['bonus_amount'] !== '') {
                $setClauses[] = "bonus_amount = :bonus_amount";
                $updateParams[':bonus_amount'] = $bonus_amount;
            }
            if (isset($data['bonus_type']) && $data['bonus_type'] !== '') {
                $setClauses[] = "bonus_type = :bonus_type";
                $updateParams[':bonus_type'] = $bonus_type;
            }
            if (isset($data['bet_count']) && $data['bet_count'] !== '') {
                $setClauses[] = "bet_count = :bet_count";
                $updateParams[':bet_count'] = $bet_count;
            }
            if (isset($data['min_odds']) && $data['min_odds'] !== '') {
                $setClauses[] = "min_odds = :min_odds";
                $updateParams[':min_odds'] = $min_odds;
            }
            if (isset($data['min_odds_per_pick']) && $data['min_odds_per_pick'] !== '') {
                $setClauses[] = "min_odds_per_pick = :min_odds_per_pick";
                $updateParams[':min_odds_per_pick'] = $min_odds_per_pick;
            }
            if (isset($data['min_stake']) && $data['min_stake'] !== '') {
                $setClauses[] = "min_stake = :min_stake";
                $updateParams[':min_stake'] = $min_stake;
            }
            if (isset($data['max_times']) && $data['max_times'] !== '') {
                $setClauses[] = "max_times = :max_times";
                $updateParams[':max_times'] = $max_times;
            }
            if (isset($data['max_win']) && $data['max_win'] !== '') {
                $setClauses[] = "max_win = :max_win";
                $updateParams[':max_win'] = $max_win;
            }
            if (isset($data['min_selections']) && $data['min_selections'] !== '') {
                $setClauses[] = "min_selections = :min_selections";
                $updateParams[':min_selections'] = $min_selections;
            }
            if (isset($data['frequency']) && $data['frequency'] !== '') {
                $setClauses[] = "frequency = :frequency";
                $updateParams[':frequency'] = $frequency;
            }
            if (isset($data['deduct_stake']) && $data['deduct_stake'] !== '') {
                $setClauses[] = "deduct_stake = :deduct_stake";
                $updateParams[':deduct_stake'] = $deduct_stake;
            }
            if (isset($data['allow_duplicate_events']) && $data['allow_duplicate_events'] !== '') {
                $setClauses[] = "allow_duplicate_events = :allow_duplicate_events";
                $updateParams[':allow_duplicate_events'] = $allow_duplicate_events;
            }
            if (isset($data['expiry_period']) && $data['expiry_period'] !== '') {
                $setClauses[] = "expiry_period = :expiry_period";
                $updateParams[':expiry_period'] = $expiry_period;
            }
            if (isset($data['restrict_withdrawals']) && $data['restrict_withdrawals'] !== '') {
                $setClauses[] = "restrict_withdrawals = :restrict_withdrawals";
                $updateParams[':restrict_withdrawals'] = $restrict_withdrawals;
            }
//            if (isset($data['market_conditions']) && $data['market_conditions'] !== '') {
//                $setClauses[] = "market_conditions = :market_conditions";
//                $updateParams[':market_conditions'] = is_array($market_conditions) ? json_encode($market_conditions) : $market_conditions;
//            }

            // Ensure there's something to update
            if (!empty($setClauses)) {
                $setClauseString = implode(", ", $setClauses);
                $updateParams[':promo_id'] = $promoId;
                // Add updated_at timestamp
//                $setClauseString .= ", updated_at = NOW()";

                $updateQuery = "UPDATE promotion_settings SET $setClauseString WHERE promo_id = :promo_id";

                $res = $this->rawUpdateWithParams('dbBonus', $updateQuery, $updateParams);

                if (!$res) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 409,
                            'message' => "Update Failed!"], true);
                }
            }

            // refresh redis cache
//            RedisUtils::redisRawDelete('promotions');

            $logs = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => $promo_name . ' promotion (ID: ' . $promoId . ') updated successfully'], true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetPromotions
     * @return type
     */
    function GetPromotions()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Promotions";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "GetPromotions | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $status = $data['status'] ?? false;
        $setting_status = $data['setting_status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['promo_name'] = $data['promo_name'] ?? false;
        $params['promo_id'] = $data['promo_id'] ?? false;
        $params['promo_component'] = $data['promo_component'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "p." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'p.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

//            RedisUtils::redisRawDelete($key);

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";
            $key .= '$:user_id' . $authResponse['data']['user_id'];

            $searchQuery = " AND NOW() >= p.starting_date AND p.ending_date >= NOW() 
            AND p.status = 1 AND px.status = 1 AND pt.status = 1";
            $searchParams = [];

            if ($status) {
                $searchParams[':dlr_status'] = $status;
                $searchQuery .= " AND p.status REGEXP :dlr_status";

                $key .= '$dlr_status:' . $status;
            }

            if ($params['promo_name']) {
                $searchParams[':promo_name'] = $params['promo_name'];
                $searchQuery .= " AND p.promo_name REGEXP :promo_name";
                $key .= '$promo_name:' . $params['promo_name'];
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND p.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
                $key .= '$start:' . $searchParams[':start'] . '$stop' . $searchParams[':stop'];
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND p.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
                $key .= '$stop' . $searchParams[':stop'];
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND p.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
                $key .= '$start:' . $searchParams[':start'];
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $bonusSql = "SELECT (SELECT COUNT(p.id) "
                . "FROM promotion p LEFT JOIN promotion_settings px ON p.id = px.promo_id "
                . "LEFT JOIN promotion_type pt ON p.promo_type_id = pt.id $searchQuery)as trx_count, "
                . "p.id AS promo_id, p.promo_name, px.id promo_setting_id, p.status promo_status, "
                . "pt.status promo_type_status,  px.bonus_amount, px.bonus_type, px.bet_count, "
                . "px.min_odds, px.min_odds_per_pick, px.min_stake, px.min_selections, px.max_times, "
                . "px.max_win, px.frequency, px.deduct_stake, px.allow_duplicate_events, px.expiry_period, "
                . "px.restrict_withdrawals, px.market_conditions, pt.type promo_type, "
                . "px.expiry_period expiry_period_in_hours, pt.component, p.promo_url, p.promo_images, "
                . "p.promo_type_id, p.starting_date, p.ending_date, p.created_at "
                . "FROM promotion p "
                . "LEFT JOIN promotion_settings px ON p.id = px.promo_id "
                . "LEFT JOIN promotion_type pt ON p.promo_type_id = pt.id $searchQuery $sorting";

            $results = $this->rawSelect('dbBonus', $bonusSql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no promotions!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' promotions successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPromotionTypes
     * @return type
     */
    function GetPromotionTypes()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View System Promotion Types";

        $data = (array)$this->request->get();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $type = $data['type'] ?? false;
        $component = $data['component'] ?? false;
        $status = $data['status'] ?? false;
        $type_id = $data['type_id'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $export = $data['export'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "pt." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'pt.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];
            if ($type) {
                $searchParams[':type'] = $type;
                $searchQuery .= " AND pt.type = :type";
            }

            if ($component) {
                $searchParams[':component'] = $component;
                $searchQuery .= " AND pt.component REGEXP :component";
            }

            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND pt.status = :status";
            }

            if (is_numeric($type_id)) {
                $searchParams[':id'] = $type_id;
                $searchQuery .= " AND pt.id = :id";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = ' select (select count(pt.id) from promotion_type pt ' . $searchQuery . ')as trx_count'
                . ',pt.id,pt.type,pt.component,pt.status from promotion_type pt '
                . $searchQuery . ' ' . $sorting;
            $results = $this->rawSelect('dbBonus', $sql, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no promotions types!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' promotions types successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetLeaderboard
     * @return type
     */
    function GetLeaderboard()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Promotions Leaderboard";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "GetLeaderboard | Request:" . json_encode($data));

        // Header processing
        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        // Data processing
        $timestamp = $data['timestamp'] ?? false;
        $phone_number = $data['phone_number'] ?? false;
        $params['promo_id'] = $data['promo_id'] ?? false;
        $promo_name = $data['promo_name'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? 1;  // Default to 1 if not provided
        $status = $data['status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;

        // Validation
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        // Set defaults
        $params['limit'] = is_numeric($params['limit']) ? $params['limit'] : $this->settings['SelectRecordLimit'];
        $params['skipCache'] = in_array($params['skipCache'], [1, 2]) ? $params['skipCache'] : 1;

        // Sorting logic
        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "tls." . $this->cleanStrSQL($order_arr[0]);
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';
            $order = in_array(strtoupper($order), ['ASC', 'DESC']) ? $order : 'DESC';
        } else {
            $sort = 'tls.odds';
            $order = 'DESC';
        }

        try {
            // Authentication checks
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Sorting and pagination
            $sorting = ($export == 1) ? "" : $this->tableQueryBuilder($sort, $order, $page, $params['limit']);

            // Build base query with WHERE 1
            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($phone_number) {
                $searchParams[':phone_number'] = $phone_number;
                $searchQuery .= " AND pr.msisdn = :phone_number";
            }

            // Add date filters (if any)
            if ($start && $stop) {
                $searchQuery .= " AND tls.promo_date BETWEEN :start AND :stop";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop) {
                $searchQuery .= " AND tls.promo_date <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($start) {
                $searchQuery .= " AND tls.promo_date >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            // Build the SQL query
            $sql = "SELECT (SELECT COUNT(tls_inner.id)
                 FROM transaction_leaderboard_stats tls_inner
                 LEFT JOIN mossbets_profile.profile pr_inner ON tls_inner.profile_id = pr_inner.id
                 LEFT JOIN mossbets_profile.profile_attribution pa_inner ON tls_inner.profile_id = pa_inner.profile_id
                 LEFT JOIN mossbets_profile.profile_balance pb_inner ON tls_inner.profile_id = pb_inner.profile_id
                 LEFT JOIN mossbets_bonus.promotion p_inner ON tls_inner.promo_id = p_inner.id
                 LEFT JOIN transaction_leaderboard_stats_blacklist tlb_inner ON tls_inner.profile_id = tlb_inner.profile_id
                 $searchQuery) as trx_count,
                tls.id as leaderboard_id, tls.promo_id, p.promo_name, tls.profile_id,  pr.msisdn,pr.name,pr.ip_address,
                 pa.total_deposits,
                (pa.total_withdrawals- pa.total_failed_withdrawals) AS total_withdrawals,
                pa.total_failed_withdrawals,
                pb.balance,
                pb.bonus,
                ((pa.total_deposits - (pa.total_withdrawals- pa.total_failed_withdrawals))-pb.balance) AS net_revenue,
                tls.bet_count, tls.odds, tls.status, tlb.status AS blacklist_status , tls.promo_date, tls.created_at, tls.updated_at AS last_updated_at
                FROM transaction_leaderboard_stats tls
                LEFT JOIN mossbets_profile.profile pr ON tls.profile_id = pr.id
                LEFT JOIN mossbets_profile.profile_attribution pa ON tls.profile_id = pa.profile_id
                LEFT JOIN mossbets_profile.profile_balance pb ON tls.profile_id = pb.profile_id
                LEFT JOIN mossbets_bonus.promotion p ON tls.promo_id = p.id
                LEFT JOIN transaction_leaderboard_stats_blacklist tlb ON tls.profile_id = tlb.profile_id
             $searchQuery
             $sorting";


            $results = $this->rawSelect('dbTrxnRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no promotions!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' promotions successfully!',
                    'data' => [
                        'record_count' => $results[0]['trx_count'],
                        'result' => $results
                    ]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * UpdateLeaderboard
     * @return type
     */
    function UpdateLeaderboard($leaderboardId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Promotions Leaderboard";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request UpdateLeaderboard:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $status = $data['status'] ?? false;
        $blacklist_status = $data['blacklist_status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken
//                || !$timestamp || !$leaderboardId || !$status
        ) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $leaderboard = $this->rawSelectOneRecord(
                'dbTrxnRead',
                "SELECT * FROM transaction_leaderboard_stats WHERE id =:id", [':id' => $leaderboardId]
            );

            if (!$leaderboard) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Leaderboard not found!'], true);
            }

            $updateParams = [
                ':id' => $leaderboardId,
            ];
            $setClauses = [];

            if (is_numeric($status)) {
                $setClauses[] = "status = :status";
                $updateParams[':status'] = $status;
                $setClauses[] = "updated_at = NOW()";

                // Ensure there's something to update
                if (!empty($setClauses)) {
                    $setClauseString = implode(", ", $setClauses);
                    $updateQuery = "UPDATE transaction_leaderboard_stats SET $setClauseString WHERE id = :id";

                    $res = $this->rawUpdateWithParams('dbTrxn', $updateQuery, $updateParams);

                    if (!$res) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 409,
                                'message' => "Leaderboard Update Failed!"], true);
                    }
                    //else
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => "Leaderboard Updated Successfully!"]);
                }
            }

            // blacklist only
            if (is_numeric($blacklist_status)) {
                $sql = "SELECT * FROM transaction_leaderboard_stats_blacklist WHERE profile_id =:profile_id";
                $blacklist = $this->rawSelectOneRecord('dbTrxnRead', $sql, [':profile_id' => $leaderboard['profile_id']]);

                if ($blacklist) {
                    $updateParams = [
                        ':profile_id' => $leaderboard['profile_id'],
                    ];
                    $setClauses = [];
                    $setClauses[] = "status = :status";
                    $updateParams[':status'] = $blacklist_status;
                    $setClauses[] = "updated_at = NOW()";
                    $setClauseString = implode(", ", $setClauses);
                    $updateQuery = "UPDATE transaction_leaderboard_stats_blacklist SET $setClauseString WHERE profile_id = :profile_id";
                    $res = $this->rawUpdateWithParams('dbTrxn', $updateQuery, $updateParams);

                    if (!$res) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 409,
                                'message' => "Leaderboard Blacklist Failed!"], true);
                    }
                    //else
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => "Leaderboard Blacklist Successfully!"]);
                } else {
                    if ($blacklist_status == 1) {
                        $res = $this->rawInsertBulk('dbTrxn',
                            "transaction_leaderboard_stats_blacklist",
                            ['profile_id' => $leaderboard['profile_id'],
                                'status' => 1,
                                'created_at' => $this->now(),
                                'updated_at' => $this->now()]);

                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is successful',
                            ['code' => 200,
                                'message' => "Leaderboard Blacklist Successfully!"]);
                    }
                }
            }

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . " | TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . " | Exception Trace:" . $ex->getTraceAsString()
                . " | Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error . "], true);
        }
    }


    /**
     * AwardCustomerBonus
     * @return type
     */
    function AwardCustomerBonusFreebet()
    {
        $start = $this->getMicrotime();
        $permissionName = "Award Customer Bonus and Freebets";
        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request AwardCustomerBonusFreebet:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $profile_id = $data['profile_id'] ?? false;
        $promo_id = $data['promo_id'] ?? false;
        $amount = $data['amount'] ?? false;
        $type = $data['type'] ?? false;
        $expiry_period = $data['expiry_period'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken ||
            !$profile_id || !$promo_id || !$amount || !$type) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $profile = $this->rawSelectOneRecord('dbProfile',
                "SELECT * FROM profile WHERE id=:id", [':id' => $profile_id]);

            if (!$profile) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Customer does not exist."], true);
            }
            $ip_address = $profile['ip_address'];

            $promo = $this->rawSelectOneRecord('dbBonus',
                "SELECT * FROM promotion_settings WHERE id=:id", [':id' => $promo_id]);

            if (!$promo) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Promotion does not exist."], true);
            }

            $signature = md5($profile_id . $promo_id . $type . $ip_address);

            $type = strtoupper($type);
            if ($type !== 'FREEBET' && $type !== 'BONUS') {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Invalid type."], true);
            }

            $key = "duplicatebo_bonus_freebet_$profile_id$promo_id";

            $payload = [
                'unique_id' => $this->ReferenceNumber(),
                'profile_id' => $profile_id,
                'promo_id' => $promo_id,
                'amount' => $amount,
                'expiry_period' => $expiry_period,
                'ip_address' => $this->ipAddress,
                'type' => $type,
                'signature' => $signature,
                'user_id' => $authResponse['data']['user_id'],
                'date' => $this->now()
            ];

            $queue = new Queue();
            $res = $queue->ConnectAndPublishToQueue($payload,
                'BONUS_AWARDS', 'BONUS_AWARDS', 'BONUS_AWARDS'
                , null, null, null, null, "/", null);

            if (!$res) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Failed to award $type."], true);
            }

            RedisUtils::redisRawInsertData($key, 1, 600);

            $logs = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "$amount $type awarded successfully."]);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }

    }


    /**
     * GetCampaignOrigins
     * @return type
     */

    function GetCampaignOrigins()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Campaign Origins";
        $data = (array)$this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request GetCampaignOrigins:" . json_encode($data));

        // Authentication and validation
        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $table_type = $data['table_type'] ?? false;
        $origin = $data['origin'] ?? false;
        $status = $data['status'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $report = $data['report'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        // Input validation
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        // Set defaults
        if (!$page) $page = 1;
        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }
        if (!in_array($params['skipCache'], [1, 2])) $params['skipCache'] = 1;

        // Sorting
        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "co." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';
            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) $order = 'DESC';
        } else {
            $sort = 'co.id';
            $order = 'DESC';
        }

        try {
            // Authentication checks
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }
            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            $searchQuery = " WHERE 1";
            $searchParams = [];

            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND co.status = :status";
            }

            $results = [];

            // Domain extraction
            $extractDomain = function ($url) {
                $domain = preg_replace('/(https?:\/\/)?([^\/]+).*/i', '$2', $url);
                if (empty($domain)) {
                    return strtoupper($url);
                }
                $pattern = '/\.(com|org|net|co\.ke|go\.ke|ac\.ke|me|info|biz|io|tv)(\.[a-z]{2,3})?$/i';
                $result = preg_replace($pattern, '', $domain);
                return !empty($result) ? strtoupper($result) : strtoupper($domain);
            };

            // Function to build date range condition
            $buildDateCondition = function ($field) use ($start, $stop, &$searchParams) {
                $condition = "";
                if ($start && $stop) {
                    $condition .= " AND DATE($field) BETWEEN :start_date AND :stop_date";
                    $searchParams[':start_date'] = $start;
                    $searchParams[':stop_date'] = $stop;
                } elseif ($start) {
                    $condition .= " AND DATE($field) >= :start_date";
                    $searchParams[':start_date'] = $start;
                } elseif ($stop) {
                    $condition .= " AND DATE($field) <= :stop_date";
                    $searchParams[':stop_date'] = $stop;
                }
                return $condition;
            };

            switch ($table_type) {
                case 2: // Signups
                case 5: // Profile Login
                    $searchQuery .= $buildDateCondition('pl.created_at');
                    if ($origin) {
                        $searchQuery .= " AND co.utm_source LIKE CONCAT('%', :origin, '%')";
                        $searchParams[':origin'] = $origin;
                    }

                    $dateFields = $report
                        ? "MIN(DATE(pl.created_at)) AS summary_start, MAX(DATE(pl.created_at)) AS summary_end"
                        : "DATE(pl.created_at) AS summary_date";

                    $sql = "SELECT COUNT(pl.id) OVER() AS trx_count,
                        pa.origin AS campaign_origin_id, 
                        co.utm_source AS raw_campaign_name,
                        COUNT(pl.id) AS total_sign_ups,
                        SUM(pl.success_attempts) AS total_success_sign_ups,
                        SUM(pl.failed_attempts) AS total_unverified_sign_ups,
                        $dateFields
                    FROM mossbets_profile.profile_login pl
                    LEFT JOIN mossbets_profile.profile_attribution pa ON pa.profile_id = pl.profile_id
                    LEFT JOIN mossbets_profile.campaign_origins co ON co.id = pa.origin
                    $searchQuery
                    " . ($report ? "GROUP BY pa.origin" : "GROUP BY pa.origin, DATE(pl.created_at)");

                    $results = $this->rawSelect('dbProfile', $sql, $searchParams);
                    break;

                case 3: // Sports Bets
                    $searchQuery .= $buildDateCondition('sb.created_at');
                    if ($origin) {
                        $searchQuery .= " AND co.utm_source LIKE CONCAT('%', :origin, '%')";
                        $searchParams[':origin'] = $origin;
                    }

                    $dateFields = $report
                        ? "MIN(DATE(sb.created_at)) AS summary_start, MAX(DATE(sb.created_at)) AS summary_end"
                        : "DATE(sb.created_at) AS summary_date";

                    $sql = "SELECT COUNT(sb.bet_id) OVER() AS trx_count,
                        co.id AS campaign_origin_id,
                        co.utm_source AS raw_campaign_name,
                        COUNT(sb.bet_transaction_id) AS sports_bet_count,
                        SUM(sb.bet_amount) AS sports_stake_amount,
                        SUM(CASE WHEN sb.status = 1 THEN ABS(sb.witholding_tax) ELSE 0 END) AS sports_witholding_tax,
                        SUM(CASE WHEN sb.status = 1 OR sb.status = 3 THEN ABS(sb.excise_tax) ELSE 0 END) AS sports_excise_tax,
                        $dateFields
                    FROM mossbets_bets.sports_bet sb
                    LEFT JOIN mossbets_profile.campaign_origins co ON co.id = sb.bet_attribution
                    $searchQuery
                    " . ($report ? "GROUP BY co.id" : "GROUP BY co.id, DATE(sb.created_at)");

                    $results = $this->rawSelect('dbBets', $sql, $searchParams);
                    break;

                case 4: // Virtual Bets
                    $searchQuery .= $buildDateCondition('vb.created_at');
                    if ($origin) {
                        $searchQuery .= " AND co.utm_source LIKE CONCAT('%', :origin, '%')";
                        $searchParams[':origin'] = $origin;
                    }

                    $dateFields = $report
                        ? "MIN(DATE(vb.created_at)) AS summary_start, MAX(DATE(vb.created_at)) AS summary_end"
                        : "DATE(vb.created_at) AS summary_date";

                    $sql = "SELECT COUNT(vb.bet_id) OVER() AS trx_count,
                        pa.origin AS campaign_origin_id,
                        co.utm_source AS raw_campaign_name,
                        COUNT(vb.bet_transaction_id) AS virtual_bet_count,
                        SUM(ABS(vb.bet_amount)) AS virtual_stake_amount,
                        SUM(CASE WHEN vb.status = 1 THEN ABS(vb.witholding_tax) ELSE 0 END) AS virtual_witholding_tax,
                        SUM(CASE WHEN vb.status = 1 OR vb.status = 3 THEN ABS(vb.excise_tax) ELSE 0 END) AS virtual_excise_tax,
                        $dateFields
                    FROM mossbets_bets.virtuals_bet vb
                    LEFT JOIN mossbets_profile.profile_attribution pa ON vb.profile_id = pa.profile_id
                    LEFT JOIN mossbets_profile.campaign_origins co ON co.id = pa.origin
                    $searchQuery
                    " . ($report ? "GROUP BY pa.origin" : "GROUP BY pa.origin, DATE(vb.created_at)");

                    $results = $this->rawSelect('dbBets', $sql, $searchParams);
                    break;

                case 6: // Deposits
                    $searchQuery = " WHERE UPPER(TRIM(rt.identifier_name)) = 'DEPOSIT'";
                    $searchQuery .= $buildDateCondition('t.created_at');
                    if ($origin) {
                        $searchQuery .= " AND co.utm_source LIKE CONCAT('%', :origin, '%')";
                        $searchParams[':origin'] = $origin;
                    }

                    $dateFields = $report
                        ? "MIN(DATE(t.created_at)) AS summary_start, MAX(DATE(t.created_at)) AS summary_end"
                        : "DATE(t.created_at) AS summary_date";

                    $sql = "SELECT COUNT(t.id) OVER() AS trx_count,
                        pa.origin AS campaign_origin_id,
                        co.utm_source AS raw_campaign_name,
                        COUNT(t.id) as count,
                        SUM(ABS(t.amount)) AS deposit_amount,
                        $dateFields
                    FROM mossbets_transactions.transaction t
                    JOIN mossbets_transactions.reference_types rt ON t.reference_type_id = rt.id
                    JOIN mossbets_profile.profile_attribution pa ON t.profile_id = pa.profile_id
                    LEFT JOIN mossbets_profile.campaign_origins co ON co.id = pa.origin
                    $searchQuery
                    " . ($report ? "GROUP BY pa.origin" : "GROUP BY pa.origin, DATE(t.created_at)");

                    $results = $this->rawSelect('dbTrxnRead', $sql, $searchParams);
                    break;

                case 7: // Payouts
                    $searchQuery = " WHERE UPPER(TRIM(rt.identifier_name)) = 'PAYOUTS'";
                    $searchQuery .= $buildDateCondition('t.created_at');
                    if ($origin) {
                        $searchQuery .= " AND co.utm_source LIKE CONCAT('%', :origin, '%')";
                        $searchParams[':origin'] = $origin;
                    }

                    $dateFields = $report
                        ? "MIN(DATE(t.created_at)) AS summary_start, MAX(DATE(t.created_at)) AS summary_end"
                        : "DATE(t.created_at) AS summary_date";

                    $sql = "SELECT COUNT(t.id) OVER() AS trx_count,
                        pa.origin AS campaign_origin_id,
                        co.utm_source AS raw_campaign_name,
                        COUNT(t.id) as count,
                        SUM(ABS(t.amount)) AS payout_amount,
                        $dateFields
                    FROM mossbets_transactions.transaction t
                    JOIN mossbets_transactions.reference_types rt ON t.reference_type_id = rt.id
                    JOIN mossbets_profile.profile_attribution pa ON t.profile_id = pa.profile_id
                    LEFT JOIN mossbets_profile.campaign_origins co ON co.id = pa.origin
                    $searchQuery
                    " . ($report ? "GROUP BY pa.origin" : "GROUP BY pa.origin, DATE(t.created_at)");

                    $results = $this->rawSelect('dbTrxnRead', $sql, $searchParams);
                    break;

                default: // Default campaign origins list
                    if ($origin) {
                        $searchQuery .= " AND co.utm_source LIKE CONCAT('%', :origin, '%')";
                        $searchParams[':origin'] = $origin;
                    }

                    $sql = "SELECT (SELECT COUNT(id) FROM campaign_origins) AS trx_count,
                        co.id, co.utm_source AS raw_utm_source, 
                        co.utm_campaign, co.utm_medium, co.utm_content,
                        co.status, co.created_at, co.updated_at
                    FROM campaign_origins co
                    $searchQuery $sorting";

                    $results = $this->rawSelect('dbProfile', $sql, $searchParams);
            }

            // Process results to extract domains
            foreach ($results as &$row) {
                if (isset($row['raw_campaign_name'])) {
                    $campaignName = $extractDomain($row['raw_campaign_name']);
//                    unset($row['raw_campaign_name']);
                    $row = array_merge(['campaign_name' => $campaignName], $row);
                } elseif (isset($row['raw_utm_source'])) {
                    $utmSource = $extractDomain($row['raw_utm_source']);
//                    unset($row['raw_utm_source']);
                    $row = array_merge(['utm_source' => $utmSource], $row);
                }

                // Ensure campaign_name is first even if it was already set
                if (isset($row['campaign_name'])) {
                    $campaignName = $row['campaign_name'];
                    unset($row['campaign_name']);
                    $row = array_merge(['campaign_name' => $campaignName], $row);
                }
            }

            $extractDomain = function ($url) {
                $domain = parse_url($url, PHP_URL_HOST);
                if (!$domain) {
                    return $url;
                }
                $domain = explode('.', $domain);
                if (count($domain) > 2) {
                    array_shift($domain);
                }
                return implode('.', $domain);
            };

            // IF YOU FInd these names in campaign_name/utm_source that contains any of these in the array,
            // replace with the value in the array and group them together by teh name
            $campaignNameMappings = [
                'fb' => 'Facebook',
                'www.facebook' => 'Facebook',
                'lm.facebook' => 'Facebook',
                'web.facebook' => 'Facebook',
                'l.facebook' => 'Facebook',
                'SEARCH.YAHOO' => 'Yahoo',
                'UK.SEARCH.YAHOO' => 'Yahoo',
                'US.SEARCH.YAHOO' => 'Yahoo',
                'GOOGLE' => 'Google',
                'WWW.GOOGLE' => 'Google',
                'WWW.GOOGLE.CA' => 'Google',
                'WWW.GOOGLE.KG' => 'Google',
                'WWW.GOOGLEADSERVICES' => 'Google',
                'WWW.BING' => 'Bing',
                'LITE.PLAYBETMAN' => 'Playbetman',
                'Other' => 'Other',
            ];
            //ATBBXfQWyY4rPJFTwQyfQR59S6YhEA45CE6D

            // use lower case when comparing and we are looking for a similarity with campaign_name/utm_source
//            foreach ($results as &$row) {
//                foreach ($campaignNameMappings as $key => $value) {
//                    if (stripos($row['campaign_name'], $key) !== false) {
//                        $row['campaign_name'] = $value;
//                        break;
//                    }
//                }
//            }
//
//            // Group by campaign_name
//            $groupedResults = [];
//            foreach ($results as $row) {
//                $groupedResults[$row['campaign_name']][] = $row;
//            }

            // Normalize names using mappings (checking both campaign_name and utm_source)
            foreach ($results as &$row) {
                // Determine which field to use (priority to campaign_name if both exist)
                $fieldToCheck = isset($row['campaign_name']) ? 'campaign_name' :
                    (isset($row['utm_source']) ? 'utm_source' : null);

                if ($fieldToCheck) {
                    $originalValue = $row[$fieldToCheck];
                    $lowerValue = strtolower($originalValue);

                    foreach ($campaignNameMappings as $key => $value) {
                        // Match whole words only to avoid false positives
                        if (preg_match('/\b' . preg_quote(strtolower($key), '/') . '\b/', $lowerValue)) {
                            // Standardize both fields if they exist
                            if (isset($row['campaign_name'])) {
                                $row['campaign_name'] = $value;
                            }
                            if (isset($row['utm_source'])) {
                                $row['utm_source'] = $value;
                            }
                            break;
                        }
                    }

                    // If no match found, ensure consistent casing
                    if ($originalValue === $row[$fieldToCheck]) {
                        $row[$fieldToCheck] = strtoupper($originalValue);
                    }
                }
            }

// Group by primary name field (campaign_name takes precedence)
            $groupedResults = [];
            foreach ($results as $row) {
                $groupKey = $row['campaign_name'] ?? $row['utm_source'] ?? 'other';

                if (!isset($groupedResults[$groupKey])) {
                    // Initialize with the first row's structure
                    $groupedResults[$groupKey] = $row;

                    // Initialize summable fields to 0
                    $summableFields = ['count', 'deposit_amount', 'payout_amount', 'stake_amount'];
                    foreach ($summableFields as $field) {
                        if (isset($row[$field])) {
                            $groupedResults[$groupKey][$field] = 0;
                        }
                    }
                }

                // Sum numeric values
                foreach (['count', 'deposit_amount', 'payout_amount', 'stake_amount'] as $field) {
                    if (isset($row[$field])) {
                        $groupedResults[$groupKey][$field] += $row[$field];
                    }
                }

                // Handle date ranges in report mode
                if ($report) {
                    foreach (['summary_start', 'summary_end'] as $dateField) {
                        if (isset($row[$dateField])) {
                            $current = $groupedResults[$groupKey][$dateField] ?? $row[$dateField];
                            $groupedResults[$groupKey][$dateField] = $dateField === 'summary_start'
                                ? min($current, $row[$dateField])
                                : max($current, $row[$dateField]);
                        }
                    }
                }
            }

// Convert to indexed array
            $results = array_values($groupedResults);


            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . " | Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no campaign origins!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . " | Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . ($results[0]['trx_count'] ?? 0)
                        . ' campaign origins successfully!',
                    'data' => [
                        'record_count' => $results[0]['trx_count'] ?? 0,
                        'result' => $results,
                    ]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . " | TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . " | Exception Trace:" . $ex->getTraceAsString()
                . " | Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error"], true);
        }
    }


    /**
     * GetCampaignOriginTransactions
     * @return type
     */
    function GetCampaignOriginTransactions($campaignOriginId)
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Campaign Origin Transactions";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " >> GetCampaignOriginTransactions | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x - authorization'] ?? false;
        $hashKey = $headers['x - hash - key'] ?? false;
        $appKey = $headers['x - app - key'] ?? false;
        $accessToken = $headers['x - access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $table_type = $data['table_type'] ?? false;

        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;


        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "tcos . " . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'tcos.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($campaignOriginId) {
                $searchQuery .= " and tcos . campaign_origin_id = :campaign_origin_id";
                $searchParams[':campaign_origin_id'] = $campaignOriginId;
            }

            if ($start && $stop) {
                $searchQuery .= " and tcos . summary_date BETWEEN :start and :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($start && !$stop) {
                $searchQuery .= " and tcos . summary_date >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            } elseif (!$start && $stop) {
                $searchQuery .= " and tcos . summary_date <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            }


            $sql = "SELECT(SELECT COUNT(tcos . id) as trx_count
                FROM mossbets_transactions . transaction_campaign_origin_summary tcos
                JOIN mossbets_profile . campaign_origins co ON tcos . campaign_origin_id = co . id
                $searchQuery) as trx_count, "
                . "tcos . id as tcos_id, tcos . campaign_origin_id, tcos . total_sign_ups, tcos . total_success_sign_ups, tcos . total_unverified_sign_ups,"
                . " tcos . total_bet_count, tcos . stake_amount, tcos . total_deposits, "
                . "tcos . payouts_amount,tcos . net_gross_revenue, tcos . status as tcos_status, "
                . "tcos . summary_date, tcos . created_at as tcos_created_at, "
                . "co . id as campaign_id, co . utm_source, co . utm_campaign, co . utm_medium,co . utm_content,"
                . "co . status, co . created_at, co . updated_at "
                . "FROM mossbets_transactions . transaction_campaign_origin_summary tcos "
                . "LEFT JOIN mossbets_profile . campaign_origins co ON tcos . campaign_origin_id = co . id "
                . "$searchQuery $sorting";

            $results = $this->rawSelect('dbTrxnRead', $sql, $searchParams);


            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . " | Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no campaign transactions!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . " | Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' campaign transactions successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . " | TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . " | Exception Trace:" . $ex->getTraceAsString()
                . " | Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error . "], true);
        }
    }


    /**
     * GetMenuHighlights
     * @return type
     */
    function GetMenuHighlights()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Menu Highlights";


        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request GetMenuHighlights:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x - authorization'] ?? false;
        $hashKey = $headers['x - hash - key'] ?? false;
        $appKey = $headers['x - app - key'] ?? false;
        $accessToken = $headers['x - access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $status = $data['status'] ?? false;
        $priority = $data['priority'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $export = $data['export'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "mh . " . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'mh.priority';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }
            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];
            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " and mh . status = :status";
            }

            if (is_numeric($priority)) {
                $searchParams[':id'] = $priority;
                $searchQuery .= " and mh . id = :id";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = ' select (select count(mh.id) from menu_highlights mh ' . $searchQuery . ')as trx_count'
                . ',mh.id,mh.name,mh.page_desc,mh.page_url,mh.icon_url,mh.created_by,mh.updated_by,mh.status,mh.priority from menu_highlights mh '
                . $searchQuery . ' ' . $sorting;
            $results = $this->rawSelect('dbBonus', $sql, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . " | Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no menu highlights!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . " | Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' menu highlights successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . " | TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . " | Exception Trace:" . $ex->getTraceAsString()
                . " | Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error . "], true);
        }
    }

    /**
     * EditMenuHighlights
     * @return type
     */
    function EditMenuHighlights($highlightId)
    {
        $start_time = $this->getMicrotime();

        $permissionName = "Edit Menu Highlights";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x - authorization'] ?? false;
        $hashKey = $headers['x - hash - key'] ?? false;
        $appKey = $headers['x - app - key'] ?? false;
        $accessToken = $headers['x - access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;

        $name = $data['name'] ?? false;
        $page_desc = $data['page_desc'] ?? false;
        $page_url = $data['page_url'] ?? false;
        $icon_url = $data['icon_url'] ?? false;
        $status = $data['status'] ?? false;
        $priority = $data['priority'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken
            || !$timestamp
            || !$highlightId
        ) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $highlight = $this->rawSelectOneRecord(
                'dbBonus',
                "SELECT * FROM menu_highlights WHERE id =:id", [':id' => $highlightId]
            );
            if (!$highlight) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Menu highlight not found!'], true);
            }

            $updateParams = [
                ':id' => $highlightId,
            ];
            $setClauses = [];

            if ($name) {
                $setClauses[] = "name = :name";
                $updateParams[':name'] = $name;
            }

            if ($page_desc) {
                $setClauses[] = "page_desc = :page_desc";
                $updateParams[':page_desc'] = $page_desc;
            }

            if ($page_url) {
                $setClauses[] = "page_url = :page_url";
                $updateParams[':page_url'] = $page_url;
            }

            if ($icon_url) {
                $setClauses[] = "icon_url = :icon_url";
                $updateParams[':icon_url'] = $icon_url;
            }

            if (is_numeric($status)) {
                $setClauses[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            if ($priority) {
                $setClauses[] = "priority = :priority";
                $updateParams[':priority'] = $priority;
            }

            $setClauses[] = "updated_by = :updated_by";
            $updateParams[':updated_by'] = $authResponse['data']['user_id'];
            $setClauses[] = "updated_at = NOW()";

            // Ensure there's something to update
            if (!empty($setClauses)) {
                $setClauseString = implode(", ", $setClauses);
                $updateQuery = "UPDATE menu_highlights SET $setClauseString WHERE id = :id";

                $res = $this->rawUpdateWithParams('dbBonus', $updateQuery, $updateParams);

                if (!$res) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 409,
                            'message' => "Update Failed!"], true);
                }
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Menu highlight updated successfully!"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . " | TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . " | Exception Trace:" . $ex->getTraceAsString()
                . " | Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error . "], true);
        }
    }


}



