<?php

use <PERSON>al<PERSON>\Http\Request;

define('Provider', 'PRAGMATIC_PLAY');

class PragmaticController extends ControllerBase
{

    function LaunchGame()
    {
        $start_time = $this->getMicrotime();
        $data = (array)$this->request->getJsonRawBody();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "]"
            . " | Request: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $appKey = $headers['x-app-key'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $accessKey = $headers['x-access-key'] ?? false;

        $mode = (int)($data['mode'] ?? 0);
        $promo = $data['promo'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $gameId = $data['game_id'] ?? false;

        if (!$appKey || !$hashKey || !$timestamp || !$gameId) {
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"],
                true);
        }

        if (!in_array($mode, [0, 1])) {//0 - Demo; 1- Live
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422, 'message' => "Invalid Game Mode. Should be Live=0, Demo=2"],
                true);
        }


        try {
            $wUtils = new AuthUtils();
            $authInfo = $wUtils->GetApplicationKeys($appKey);
            if ($authInfo['code'] != 200) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. ' . $authInfo['message']],
                    true);
            }

            if (!$wUtils->HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'],
                    true);
            }

            $sessionToken = "";
            if ($mode == 1 || $accessKey) {
                $authResults = $wUtils->AuthenticateAccessKey($accessKey);
                if ($authResults['code'] != 200) {
                    return $this->HttpResponse(
                        __LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => $authResults['code'], 'message' => $authResults['message']],
                        true);
                }

                $sessionToken = $this->Encrypt($authResults['data']['profile_id'], true);
            }

            $returnUrl = (ENVIRONMENT == 'PROD') ? $this->settings['ServerName'] : "https://dev.v1.mossbets.bet";

            $pParams = [
                'secureLogin' => $this->settings['Pragmatic']['secureLogin'],
                'symbol' => $gameId,
                'language' => 'en',
                'currency' => 'KES',
                'country' => 'KE',
                'rcCloseUrl' => $returnUrl,
                'cashierUrl' => $returnUrl . '/my-account',
                'lobbyUrl' => $returnUrl . '/instant-games/pragmatic',
                'playMode' => ($mode == 1) ? 'REAL' : 'DEMO',
            ];

            if ($promo) {
                $pParams['promo'] = '';
            }

            if ($mode != 1) {
                $pParams['jurisdiction'] = 'KE';
            }

            if (!empty($sessionToken) && $mode == 1) {
                $pParams['externalPlayerId'] = $authResults['data']['profile_id'];
                $pParams['token'] = $sessionToken;
            }

            $pParams['hash'] = $this->generatePragmaticHash($pParams);

            $response = $this->sendRawPostData($this->settings['Pragmatic']['API'] . "http/CasinoGameAPI/game/url",
                $pParams, ['Content-Type: application/x-www-form-urlencoded',
                    'Cache-Control: no-cache']);

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . "[" . $this->ipAddress . "]"
                . " | sendRawPostData: " . json_encode($response));

            $decodedResponse = json_decode($response['response'], true);
            $statusCode = $response['statusCode'] ?? false;
            if (!in_array($statusCode, [200, 202, 202])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => $statusCode,
                        'message' => 'Launch Failed!'
                    ], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => "Game launch {PragmaticPlay} with URL",
                    'url' => $decodedResponse['gameURL']
                ], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    public function Authenticate()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request_auth: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $token = $data['token'] ?? false;
        $providerId = $data['providerId'] ?? false;
        $gameId = $data['gameId'] ?? false;
        $ipAddress = $data['ipAddress'] ?? false;
        $chosenBalance = $data['chosenBalance'] ?? false;
        $launchingType = $data['launchingType'] ?? false;

        if (!$hash || !$token || !$providerId) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {
            $profileId = false;
            try {
                $profileId = $this->Decrypt($token, true);
            } catch (Exception $ex) {
                $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . "| Exception Trace:" . $ex->getTraceAsString()
                    . "| Message:" . $ex->getMessage());
                return $this->createApiResponse(4,
                    "Player authentication failed due to invalid, not found or expired", null, $start_time);
            }

            $authResults = $this->getPlayerDataById($profileId);
            if (!$authResults) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }

            $iUtils = new IntegrationsUtils();
            $betLimits = $iUtils->GetBetLimits(1, Provider, "KES");

            $params = [
                'userId' => $authResults['profile_id'],
                'currency' => 'KES',
                'cash' => floatval($authResults['balance']),
                'bonus' => $authResults['bonus'],
                'country' => 'KES',
                'betLimits' => [
                    'defaultBet' => $betLimits['min_stake'],
                    'minBet' => $betLimits['min_stake'],
                    'minTotalBet' => $betLimits['min_stake'],
                    'maxBet' => $betLimits['max_stake'],
                    'maxTotalBet' => $betLimits['max_win'],
                ],
            ];

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . "[" . $this->ipAddress . "] | Response_Auth_params: " . json_encode($params));

            return $this->createApiResponse(0, 'Success', $params, $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error Authenticate', null, $start_time);
        }
    }

    function Balance()
    {

        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request_balance: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $token = $data['token'] ?? false;
        $userId = $data['userId'] ?? false;
        $providerId = $data['providerId'] ?? false;

        if (!$hash || !$userId || !$providerId) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {
            $profileId = false;
            if ($token) {
                try {
                    $profileId = $this->Decrypt($token, true);
                } catch (Exception $ex) {
                    $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                        . "| Exception Trace:" . $ex->getTraceAsString()
                        . "| Message:" . $ex->getMessage());
                    return $this->createApiResponse(4, "Player authentication "
                        . "failed due to invalid, not found or expired", null, $start_time);
                }
            } else {
                $profileId = $userId;
            }

            $authResults = $this->getPlayerDataById($profileId);
            if (!$authResults) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }

            return $this->createApiResponse(0, 'Success',
                ['currency' => 'KES',
                    'cash' => floatval($authResults['balance']),
                    'bonus' => floatval($authResults['bonus']),
                    'totalBalance' => floatval($authResults['balance'])], $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error balance', null, $start_time);
        }
    }

    function Bet()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request_bet: " . json_encode($data));
        $providerId = $data['providerId'] ?? false;
        $hash = $data['hash'] ?? false;
        $token = $data['token'] ?? false;
        $userId = $data['userId'] ?? false;
        $gameId = $data['gameId'] ?? false;
        $roundId = $data['roundId'] ?? false;
        $amount = $data['amount'] ?? null;
        $reference = $data['reference'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $roundDetails = $data['roundDetails'] ?? false;
        $bonusCode = $data['bonusCode'] ?? false;
        $platform = $data['platform'] ?? false;
        $language = $data['language'] ?? false;
        $jackpotContribution = $data['jackpotContribution'] ?? false;
        $jackpotDetails = $data['jackpotDetails'] ?? false;
        $jackpotId = $data['jackpotId'] ?? false;
        $ipAddress = $data['ipAddress'] ?? false;


        if (!$hash || !$userId || !$providerId || !$gameId || !$roundId || !$reference || !$timestamp || !$roundDetails || $amount == null) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }


        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {

            $betReference = $roundId . '|' . $reference;

            if ($bonusCode) {
                $betReference .= '|' . $bonusCode;
            }

            $iUtils = new IntegrationsUtils();
            $resData = $this->getPlayerDataById($userId);
            if (!$resData) {
                return $this->createApiResponse(2, "Player not found or logged out.");
            }
            $profileId = $resData['profile_id'] ?? $userId;
            $balances = $iUtils->GetPlayerBalance($profileId);
            $bet_check = $this->checkForIdempotency($profileId, $betReference);
            if (!empty($bet_check)) {
                return $this->createApiResponse(0, 'Success',
                    [
                        'transactionId' => $bet_check['bet_transaction_id'],
                        'currency' => 'KES',
                        'bonus' => floatval($balances['bonus']),
                        'cash' => floatval($balances['balance']),
                        'usedPromo' => 0], $start_time
                );
            }
            $availableCash = floatval($balances['balance']);
            $availableBonus = floatval($balances['bonus']);

            $usedCash = 0;
            $usedBonus = 0;

            if ($availableCash >= $amount) {
                $usedCash = $amount;
                $usedBonus = 0;
            } elseif (($availableCash + $availableBonus) >= $amount) {
                if ($availableCash > 0) {
                    $usedCash = $availableCash;
                    $usedBonus = $amount - $availableCash;
                } else {
                    $usedCash = 0;
                    $usedBonus = $amount;
                }
            } elseif ($availableBonus >= $amount) {
                $usedCash = 0;
                $usedBonus = $amount;
            } else {
                return $this->createApiResponse(1, "Insufficient balance.", null, $start_time);
            }

            $gameStatus = $iUtils->GetGames($gameId, Provider);
            if (!$gameStatus) {
                return $this->createApiResponse(8, "Game is not found or diabled", null, $start_time);
            }


            $eTax = $iUtils->CalculateExciseTax($amount);
            $stakeTax = $eTax['stakeTax'];

            $bet_extra_data = [
                'msisdn' => $resData['msisdn'],
                'ip' => $this->ipAddress,
                'gameName' => 'Pragmatic',
                'roundId' => $roundId,
                'amount' => $amount,
                'providerId' => $providerId,
                'timestamp' => $timestamp,
                'roundDetails' => $roundDetails,
                'bonusCode' => $bonusCode,
                'platform' => $platform,
                'jackpotContribution' => $jackpotContribution,
                'jackpotDetails' => $jackpotDetails,
                'jackpotId' => $jackpotId,
                'ipAddress' => $ipAddress,
                'bet_reference' => $reference,
                'balance' => [
                    'amount' => $availableCash,
                    'bonus' => $availableBonus
                ],
            ];

            $conBet = null;
            $conTrxn = null;
            $conProfile = null;

            $clientId = 1;


            try {
                $conBet = $this->di->getShared("dbBet");
                $conProfile = $this->di->getShared("dbProfileWrite");
                $conTrxn = $this->di->getShared("dbTransactionsWrite");

                $conBet->begin();
                $conTrxn->begin();
                $conProfile->begin();

                $conProfile->execute(
                    "UPDATE profile_balance SET balance= balance - :cash_amount"
                    . ",bonus=bonus-:bonus_amount WHERE profile_id = :profile_id "
                    . " AND balance >= :cash_amount AND bonus >= :bonus_amount ",
                    [
                        ':cash_amount' => $usedCash,
                        ':bonus_amount' => $usedBonus,
                        ':profile_id' => $profileId
                    ]
                );

                if ($conProfile->affectedRows() < 1) {
                    $conProfile->rollback();
                    $conProfile = null;
                    $conTrxn = null;
                    $conBet = null;
                    return $this->createApiResponse(1, "Insufficient balance. .....", null, $start_time);
                }


                $sql = $this->GenerateSql(
                    [
                        'profile_id' => $profileId,
                        'reference_type_id' => $this->settings['ReferenceTypes']['Bet']['Pragmatic']['Stake'],
                        'transaction_type_id' => TRANSACTION_TYPE_DEBIT_ID,
                        'reference_id' => $betReference,
                        'amount' => -$amount,
                        'currency' => 'KES',
                        'source' => Provider . ($amount > 0 ? '_CASH_BET' : '_BONUS_BET'),
                        'description' => $gameId . " Bet Debit",
                        'extra_data' => json_encode([
                            'ip_address' => $this->ipAddress,
                            'total_events' => 1,
                            'odds' => (float)1,
                            'payout' => (float)0,
                            'game_number' => $roundId
                        ]),
                        'created_at' => $this->now()],
                    'transaction');

                $conTrxn->execute($sql['sql'], $sql['sql_arr']);

                $sql = $this->GenerateSql(
                    [
                        'client_id' => $clientId,
                        'profile_id' => $profileId,
                        'bet_currency' => 'KES',
                        'bet_amount' => $amount,
                        'bet_reference' => $betReference,
                        'provider_name' => Provider,
                        'game_id' => $gameId,
                        'bet_transaction_id' => $conTrxn->lastInsertId(),
                        'bet_type' => 0,
                        'total_games' => 1,
                        'total_odd' => 1,
                        'possible_win' => 0.00,
                        'witholding_tax' => 0.00,
                        'excise_tax' => $stakeTax,
                        'extra_data' => json_encode($bet_extra_data),
                        'created_by' => $gameId,
                        'created_at' => $this->now()],
                    'virtuals_bet');

                $conBet->execute($sql['sql'], $sql['sql_arr']);
                $betId = $conTrxn->lastInsertId();
                $conProfile->commit();
                $conTrxn->commit();
                $conBet->commit();

                $conProfile = null;
                $conTrxn = null;
                $conBet = null;
            } catch (Exception $ex) {
                $conProfile->rollback();
                $conTrxn->rollback();
                $conBet->rollback();
                throw $ex;
            }

            $balances_ = $iUtils->GetPlayerBalance($profileId);

            return $this->createApiResponse(0, 'Success',
                [
                    'transactionId' => $betId,
                    'currency' => 'KES',
                    'bonus' => floatval($balances_['bonus']),
                    'cash' => floatval($balances_['balance']),
                    'usedPromo' => $usedBonus], $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error bet', null, $start_time);
        }
    }

    function Result()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request_result: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $userId = $data['userId'] ?? false;
        $gameId = $data['gameId'] ?? false;
        $roundId = $data['roundId'] ?? false;
        $amount = $data['amount'] ?? null;
        $reference = $data['reference'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $roundDetails = $data['roundDetails'] ?? false;

        $bonusCode = $data['bonusCode'] ?? false;
        $platform = $data['platform'] ?? false;
        $providerId = $data['providerId'] ?? false;
        $token = $data['token'] ?? false;
        $promoWinAmount = $data['promoWinAmount'] ?? false;
        $promoWinReference = $data['promoWinReference'] ?? false;
        $promoCampaignID = $data['promoCampaignID'] ?? false;
        $promoCampaignType = $data['promoCampaignType'] ?? false;
        $language = $data['language'] ?? false;
        $specPrizes = $data['specPrizes'] ?? [];

        if (!$hash || !$userId || !$providerId || !$gameId || !$roundId ||
            $amount == null || !$reference || !$timestamp || !$roundDetails) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        $specPrizeAmount = null;
        $specPrizecode = null;
        $specPrizeType = null;

        foreach ($specPrizes as $prize) {
            $specPrizeAmount = $prize['specPrizeAmount'] ?? false;
            $specPrizecode = $prize['specPrizeCode'] ?? false;
            $specPrizeType = $prize['specPrizeType'] ?? false;
        }

        if ($roundDetails) {
            $detailsArray = [];
            $pairs = explode(',', $roundDetails);
            foreach ($pairs as $pair) {
                $keyValue = explode(':', $pair);
                if (count($keyValue) === 2) {
                    $key = trim(str_replace('"', '', $keyValue[0]));
                    $value = trim(str_replace('"', '', $keyValue[1]));
                    $detailsArray[$key] = $value;
                }
            }
            $spin = $detailsArray['spin'] ?? null;
            $totalBet = $detailsArray['totalBet'] ?? null;
            $freeSpinCount = $detailsArray['freeSpinCount'] ?? null;
            $totalWin = $detailsArray['totalWin'] ?? null;
            $baseWin = $detailsArray['baseWin'] ?? null;
            $freeSpinWin = $detailsArray['freeSpinWin'] ?? null;
        }

        try {
            $resData = $this->getPlayerDataById($userId);
            if (!$resData) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }

            $profileId = $resData['profile_id'] ?? $userId;
            $iUtils = new IntegrationsUtils();
            $balances = $iUtils->GetPlayerBalance($profileId);
            $gameStatus = $iUtils->GetGames($gameId, Provider);

            if (!$gameStatus) {
                return $this->createApiResponse(8, "Game is not found or diabled", null, $start_time);
            }

            if ($promoWinAmount) {
                $amount = $promoWinAmount + $amount;
            }

            $eTax = $iUtils->CalculateExciseTax($amount);
            $stakeTax = $eTax['stakeTax'];

            $bet_extra_data = [
                'msisdn' => $resData['msisdn'],
                'ip' => $this->ipAddress,
                'gameName' => 'Pragmatic',
                'roundId' => $roundId,
                'amount' => $amount,
                'providerId' => $providerId,
                'timestamp' => $timestamp,
                'roundDetails' => $roundDetails,
                'bonusCode' => $bonusCode,
                'platform' => $platform,
                'promoWinAmount' => $promoWinAmount,
                'promoWinReference' => $promoWinReference,
                'promoCampaignID' => $promoCampaignID,
                'promoCampaignType' => $promoCampaignType,
                'specPrizeAmount' => $specPrizeAmount,
                'specPrizecode' => $specPrizecode,
                'specPrizeType' => $specPrizeType,
                'result_reference' => $reference
            ];

            $betCheck = $this->getVirtualBet($userId, $gameId, $roundId);


            if (!$betCheck) {
                return $this->createApiResponse(120, "Transaction not found", null, $start_time);
            }

            $firstBet = null;
            foreach ($betCheck as $bet) {
                if ($bet['status'] == 0) {
                    $firstBet = $bet;
                    break;
                }
            }
            if (!$firstBet) {
                $fallback = $betCheck[0];
                return $this->createApiResponse(0, 'Success', [
                    'transactionId' => $fallback['bet_credit_transaction_id'],
                    'currency' => 'KES',
                    'bonus' => floatval($balances['bonus']),
                    'cash' => floatval($balances['balance']),
                ], $start_time);
            }

            $WinTrans = $this->UpdateWins($firstBet, $gameId, $roundId, $amount,
                $freeSpinCount ?? 1, $bet_extra_data, $timestamp);

            try {
                if (count($betCheck) > 1 && $WinTrans) {
                    foreach ($betCheck as $x) {
                        if ($x['bet_id'] == $firstBet['bet_id']) {
                            continue;
                        }
                        $params = $WinTrans['update_params'];
                        $params[':bet_id'] = $x['bet_id'];
                        $params[':extra_data'] = json_encode(
                            array_merge(json_decode($x['extra_data'], true) ?? [], $bet_extra_data)
                        );
                        $this->di->getShared("dbBet")->execute($WinTrans['update_sql'], $params);
                    }

                    $allBetIds = implode('|', array_column($betCheck, 'bet_transaction_id'));
                    $sqlx = "UPDATE transaction SET reference_id = :reference_id WHERE id = :id";
                    $this->di->getShared("dbTransactionsWrite")->execute($sqlx, [
                        ':reference_id' => $allBetIds,
                        ':id' => $WinTrans['transaction_id']
                    ]);
                }
            } catch (Exception $ex) {
                throw $ex;
            }


            if (!$WinTrans) {
                return $this->createApiResponse(120, 'Unable to update bet - '
                    . 'bet may not exist or was already processed. result', null, $start_time);
            }

            $balances_ = $iUtils->GetPlayerBalance($userId);

            return $this->createApiResponse(0, 'Success',
                ['transactionId' => $WinTrans['transaction_id'],
                    'currency' => 'KES',
                    'cash' => floatval($balances_['balance']),
                    'bonus' => floatval($balances_['bonus'])], $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error result', null, $start_time);
        }
    }

    function BonusWin()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request: " . json_encode($data));
        $hash = $data['hash'] ?? false;
        $userId = $data['userId'] ?? false;
        $amount = $data['amount'] ?? null;
        $reference = $data['reference'] ?? false;
        $providerId = $data['providerId'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $bonusCode = $data['bonusCode'] ?? false;
        $roundId = $data['roundId'] ?? false;
        $gameId = $data['gameId'] ?? false;
        $token = $data['token'] ?? false;
        $requestId = $data['requestId'] ?? false;
        $remainAmount = $data['remainAmount'] ?? false;
        $specPrizes = $data['specPrizes'] ?? [];


        if (!$hash || !$userId || !$providerId || $amount == null || !$reference ||
            !$timestamp || !$bonusCode) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        $allSpecPrizes = [];
        foreach ($specPrizes as $prize) {
            $allSpecPrizes[] = [
                'specPrizeAmount' => $prize['specPrizeAmount'] ?? null,
                'specPrizecode' => $prize['specPrizeCode'] ?? null,
                'specPrizeType' => $prize['specPrizeType'] ?? null
            ];
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {
            $resData = $this->getPlayerDataById($userId);
            if (!$resData) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }

            $profileId = $resData['profile_id'] ?? $userId;
            $balance = (float)($resData['balance'] ?? 0.00);
            if ($amount > $balance) {
                return $this->createApiResponse(1, "Insufficient balance.", null, $start_time);
            }

            $iUtils = new IntegrationsUtils();
            $balances = $iUtils->GetPlayerBalance($profileId);
            $gameStatus = $iUtils->GetGames($gameId, Provider);

            if (!$gameStatus) {
                return $this->createApiResponse(8, "Game is not found or diabled", null, $start_time);
            }

            if ($balances['balance'] < $amount) {
                return $this->createApiResponse(1, "Insufficient balance.", null, $start_time);
            }

            $eTax = $iUtils->CalculateExciseTax($amount);
            $stakeTax = $eTax['stakeTax'];

            $bet_extra_data = [
                'msisdn' => $resData['msisdn'],
                'ip' => $this->ipAddress,
                'gameName' => 'Pragmatic',
                'roundId' => $roundId,
                'amount' => $amount,
                'providerId' => $providerId,
                'timestamp' => $timestamp,
                'bonusCode' => $bonusCode,
                'requestId' => $requestId,
                'remainAmount' => $remainAmount];


            if ($allSpecPrizes) {
                $bet_extra_data['specPrizes'] = $allSpecPrizes;
            }

            $betCheck = $this->getVirtualBet($userId, $gameId, $roundId, $reference, $bonusCode);

            if (!$betCheck) {
                return $this->createApiResponse(120, "Transaction not found", null, $start_time);
            }

            $firstBet = $betCheck[0];
            $WinTrans = $this->UpdateWins($firstBet, $gameId, $reference, $amount,
                1, $bet_extra_data, $timestamp, 'BONUS');
            if (!$WinTrans) {
                return $this->createApiResponse(120, 'Unable to update bet - '
                    . 'bet may not exist or was already processed.', null, $start_time);
            }
            if ($WinTrans && $WinTrans['duplicate']) {
                $balances_ = $iUtils->GetPlayerBalance($userId);
                return $this->createApiResponse(0, 'Success',
                    ['transactionId' => $WinTrans['transaction_id'],
                        'currency' => 'KES',
                        'cash' => floatval($balances_['balance']),
                        'bonus' => floatval($balances_['bonus'])], $start_time);
            }
            try {
                if (count($betCheck) > 1 && $WinTrans) {
                    foreach ($betCheck as $x) {
                        if ($x['bet_id'] == $firstBet['bet_id']) {
                            continue;
                        }
                        $params = $WinTrans['update_params'];
                        $params[':bet_id'] = $x['bet_id'];
                        $params[':extra_data'] = json_encode(
                            array_merge(json_decode($x['extra_data'], true) ?? [], $bet_extra_data)
                        );
                        $this->di->getShared("dbBet")->execute($WinTrans['update_sql'], $params);
                    }

                    $allBetIds = implode('|', array_column($betCheck, 'bet_transaction_id'));
                    $sqlx = "UPDATE transaction SET reference_id = :reference_id WHERE id = :id";
                    $this->di->getShared("dbTransactionsWrite")->execute($sqlx, [
                        ':reference_id' => $allBetIds,
                        ':id' => $WinTrans['transaction_id']
                    ]);
                }
            } catch (Exception $ex) {
                throw $ex;
            }


            $balances_ = $iUtils->GetPlayerBalance($userId);

            return $this->createApiResponse(0, 'Success',
                ['transactionId' => $WinTrans['transaction_id'],
                    'currency' => 'KES',
                    'cash' => floatval($balances_['balance']),
                    'bonus' => floatval($balances_['bonus'])], $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error BonusWin', null, $start_time);
        }
    }

    function JackpotWin()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $providerId = $data['providerId'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $userId = $data['userId'] ?? false;
        $gameId = $data['gameId'] ?? false;
        $roundId = $data['roundId'] ?? false;
        $jackpotId = $data['jackpotId'] ?? false;
        $jackpotDetails = $data['jackpotDetails'] ?? [];
        $amount = $data['amount'] ?? null;
        $reference = $data['reference'] ?? false;
        $platform = $data['platform'] ?? false;
        $token = $data['token'] ?? false;
        $balanceBeforeWin = $data['balanceBeforeWin'] ?? false;
        $balanceAfterWin = $data['balanceAfterWin'] ?? false;
        $instanceId = $data['instanceId'] ?? false;
        $specPrizes = $data['specPrizes'] ?? [];
        $specPrizeType = false;
        foreach ($specPrizes as $prize) {
            $specPrizeType = $prize['specPrizeType'] ?? false;
        }

        if (!$hash || !$userId || !$providerId || $amount == null || !$reference || !$timestamp) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }
        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }


        try {
            $resData = $this->getPlayerDataById($userId);
            if (!$resData) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }
            $profileId = $resData['profile_id'] ?? $userId;
            $iUtils = new IntegrationsUtils();
            $balances = $iUtils->GetPlayerBalance($profileId);
            $gameStatus = $iUtils->GetGames($gameId, Provider);

            if (!$gameStatus) {
                return $this->createApiResponse(8, "Game is not found or diabled", null, $start_time);
            }


            $eTax = $iUtils->CalculateExciseTax($amount);
            $stakeTax = $eTax['stakeTax'];

            $bet_extra_data = [
                'msisdn' => $resData['msisdn'],
                'ip' => $this->ipAddress,
                'gameName' => 'Pragmatic',
                'roundId' => $roundId,
                'amount' => $amount,
                'providerId' => $providerId,
                'timestamp' => $timestamp,
                'jackpotId' => $jackpotId,
                'jackpotDetails' => $jackpotDetails,
                'platform' => $platform,
                'specPrizeType' => $specPrizeType,
                'balanceBeforeWin' => $balanceBeforeWin,
                'balanceAfterWin' => $balanceAfterWin,
                'instanceId' => $instanceId,
                'result_reference' => $reference
            ];

            $betCheck = $this->getVirtualBet($userId, $gameId, $roundId);
            if (!$betCheck) {
                return $this->createApiResponse(120, "Transaction not found", null, $start_time);
            }
            $firstBet = $betCheck[0];


            // if($jackpotDetails){
            //     $this->insertAward($firstBet['bet_id'], $gameId, 'JACKPOT', $amount, $specPrizeType['specPrizeType'], $bet_extra_data, $jackpotId);
            // }


            $WinTrans = $this->UpdateWins($firstBet, $gameId, $reference, $amount,
                1, $bet_extra_data, $timestamp, 'JACKPOT');

            if (!$WinTrans) {
                return $this->createApiResponse(120, 'Unable to update bet - '
                    . 'bet may not exist or was already processed.', null, $start_time);
            }

            if ($WinTrans && $WinTrans['duplicate']) {
                $balances_ = $iUtils->GetPlayerBalance($userId);
                return $this->createApiResponse(0, 'Success',
                    ['transactionId' => $WinTrans['transaction_id'],
                        'currency' => 'KES',
                        'cash' => floatval($balances_['balance']),
                        'bonus' => floatval($balances_['bonus'])], $start_time);
            }

            try {
                if (count($betCheck) > 1 && $WinTrans) {
                    foreach ($betCheck as $x) {
                        if ($x['bet_id'] == $firstBet['bet_id']) {
                            continue;
                        }
                        $params = $WinTrans['update_params'];
                        $params[':bet_id'] = $x['bet_id'];
                        $params[':extra_data'] = json_encode(
                            array_merge(json_decode($x['extra_data'], true) ?? [], $bet_extra_data)
                        );
                        $this->di->getShared("dbBet")->execute($WinTrans['update_sql'], $params);
                    }

                    $allBetIds = implode('|', array_column($betCheck, 'bet_transaction_id'));
                    $sqlx = "UPDATE transaction SET reference_id = :reference_id WHERE id = :id";
                    $this->di->getShared("dbTransactionsWrite")->execute($sqlx, [
                        ':reference_id' => $allBetIds,
                        ':id' => $WinTrans['transaction_id']
                    ]);
                }
            } catch (Exception $ex) {
                throw $ex;
            }


            $balances_ = $iUtils->GetPlayerBalance($userId);

            return $this->createApiResponse(0, 'Success',
                ['transactionId' => $WinTrans['transaction_id'],
                    'currency' => 'KES',
                    'cash' => floatval($balances_['balance']),
                    'bonus' => floatval($balances_['bonus'])], $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error JackpotWin', null, $start_time);
        }
    }

    function PromoWin()
    {

        $start_time = $this->getMicrotime();

        $data = $this->request->getPost();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $providerId = $data['providerId'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $userId = $data['userId'] ?? false;
        $campaignId = $data['campaignId'] ?? false;
        $campaignType = $data['campaignType'] ?? false;
        $amount = $data['amount'] ?? null;
        $currency = $data['currency'] ?? false;
        $reference = $data['reference'] ?? false;
        $roundId = $data['roundId'] ?? false;
        $gameId = $data['gameId'] ?? false;
        $dataType = $data['dataType'] ?? false;

        if (!$hash || !$userId || !$providerId || !$campaignId || !$campaignType ||
            $amount == null || !$reference || !$timestamp || !$currency) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {
            $resData = $this->getPlayerDataById($userId);
            if (!$resData) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }

            $profileId = $resData['profile_id'] ?? $userId;


            $iUtils = new IntegrationsUtils();
            $balances = $iUtils->GetPlayerBalance($profileId);
            $gameStatus = $iUtils->GetGames($gameId, Provider);

            if (!$gameStatus) {
                return $this->createApiResponse(8, "Game is not found or diabled", null, $start_time);
            }


            $eTax = $iUtils->CalculateExciseTax($amount);
            $stakeTax = $eTax['stakeTax'];

            $bet_extra_data = [
                'msisdn' => $resData['msisdn'],
                'ip' => $this->ipAddress,
                'gameName' => 'Pragmatic',
                'roundId' => $roundId,
                'amount' => $amount,
                'providerId' => $providerId,
                'timestamp' => $timestamp,
                'campaignId' => $campaignId,
                'campaignType' => $campaignType,
                'dataType' => $dataType
            ];

            $betCheck = $this->getVirtualBet($userId, $gameId, $roundId);
            if (!$betCheck) {
                return $this->createApiResponse(120, "Transaction not found", null, $start_time);
            }
            $firstBet = $betCheck[0];
            $WinTrans = $this->UpdateWins($firstBet, $gameId, $reference, $amount,
                0, $bet_extra_data, $timestamp, 'PROMO_WIN');
            if (!$WinTrans) {
                return $this->createApiResponse(120, 'Unable to update bet - '
                    . 'bet may not exist or was already processed.', null, $start_time);
            }
            if ($WinTrans && $WinTrans['duplicate']) {
                $balances_ = $iUtils->GetPlayerBalance($userId);
                return $this->createApiResponse(0, 'Success',
                    ['transactionId' => $WinTrans['transaction_id'],
                        'currency' => 'KES',
                        'cash' => floatval($balances_['balance']),
                        'bonus' => floatval($balances_['bonus'])], $start_time);
            }

            try {
                if (count($betCheck) > 1 && $WinTrans) {
                    foreach ($betCheck as $x) {
                        if ($x['bet_id'] == $firstBet['bet_id']) {
                            continue;
                        }
                        $params = $WinTrans['update_params'];
                        $params[':bet_id'] = $x['bet_id'];
                        $params[':extra_data'] = json_encode(
                            array_merge(json_decode($x['extra_data'], true) ?? [], $bet_extra_data)
                        );
                        $this->di->getShared("dbBet")->execute($WinTrans['update_sql'], $params);
                    }

                    $allBetIds = implode('|', array_column($betCheck, 'bet_transaction_id'));
                    $sqlx = "UPDATE transaction SET reference_id = :reference_id WHERE id = :id";
                    $this->di->getShared("dbTransactionsWrite")->execute($sqlx, [
                        ':reference_id' => $allBetIds,
                        ':id' => $WinTrans['transaction_id']
                    ]);
                }
            } catch (Exception $ex) {
                throw $ex;
            }

            $balances_ = $iUtils->GetPlayerBalance($userId);

            return $this->createApiResponse(0, 'Success',
                ['transactionId' => $WinTrans['transaction_id'],
                    'currency' => 'KES',
                    'cash' => floatval($balances_['balance']),
                    'bonus' => floatval($balances_['bonus'])], $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error PromoWin', null, $start_time);
        }
    }

    function EndRound()
    {

        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $userId = $data['userId'] ?? false;
        $gameId = $data['gameId'] ?? false;
        $roundId = $data['roundId'] ?? false;
        $providerId = $data['providerId'] ?? false;

        if (!$hash || !$userId || !$gameId || !$providerId || !$roundId) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {
            $betCheck = $this->rawSelectOneRecord(
                'dbBet',
                "SELECT bet_id,profile_id,bet_transaction_id,bet_credit_transaction_id"
                . ",extra_data,status,bet_amount "
                . "FROM virtuals_bet WHERE profile_id = :profile_id AND game_id = :game_id "
                . "AND JSON_UNQUOTE(JSON_EXTRACT(extra_data, '$.roundId')) = :round_id ",
                // . "AND status = 0",
                [
                    ':profile_id' => $userId,
                    ':game_id' => $gameId,
                    ':round_id' => $roundId
                ]
            );

            if ($betCheck) {
                if ($betCheck['status'] == 0) {
                    try {
                        $conBet = $this->di->getShared("dbBet");
                        $conBet->begin();
                        $conBet->execute("UPDATE virtuals_bet SET status = :status "
                            . "WHERE bet_id = :bet_id LIMIT 1",
                            [':status' => $this->settings['BetStatus']['LOST'],
                                ':bet_id' => $betCheck['bet_id']]);

                        if ($conBet->affectedRows() < 1) {
                            throw new PDOException("Error<>Updating cancelled bet status failed!");
                        }

                        $conBet->commit();
                    } catch (Exception $ex) {
                        throw $ex;
                    }
                }
            } else {
                return $this->createApiResponse(100, 'Internal server error betcheck EndRound', null, $start_time);
            }

            $iUtils = new IntegrationsUtils();
            $balances_ = $iUtils->GetPlayerBalance($userId);

            return $this->createApiResponse(0, 'Success', [
                'cash' => floatval($balances_['balance']),
                'bonus' => floatval($balances_['bonus'])], $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error EndRound', null, $start_time);
        }
    }

    function SessionExpired()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $providerId = $data['providerId'] ?? false;
        $sessionId = $data['sessionId'] ?? false;
        $playerId = $data['playerId'] ?? false;
        $token = $data['token'] ?? false;

        if (!$hash || !$sessionId || !$providerId || !$playerId) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {
            $wUtils = new AuthUtils();
            $authResults = $wUtils->AuthenticateAccessKey($token);
            if ($authResults['code'] != 200) {
                return $this->createApiResponse(4, 'Player authentication failed '
                    . 'due to invalid, not found or expired token', null, $start_time);
            }

            $profileId = false;
            try {
                $profileId = $this->Decrypt($token, true);
            } catch (Exception $ex) {
                $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . "| Exception Trace:" . $ex->getTraceAsString()
                    . "| Message:" . $ex->getMessage());

                return $this->createApiResponse(4, "Player authentication failed "
                    . "due to invalid, not found or expired", null, $start_time);
            }

            $authResults = $this->getPlayerDataById($profileId);
            if (!$authResults) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }

            return $this->createApiResponse(0, 'Success', null, $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(100, 'Internal server error SessionExpired', null, $start_time);
        }
    }

    function Refund()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $userId = $data['userId'] ?? false;
        $reference = $data['reference'] ?? false;
        $providerId = $data['providerId'] ?? false;
        $platform = $data['platform'] ?? false;
        $amount = $data['amount'] ?? null;
        $gameId = $data['gameId'] ?? false;
        $roundId = $data['roundId'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $roundDetails = $data['roundDetails'] ?? false;
        $bonusCode = $data['bonusCode'] ?? false;
        $token = $data['token'] ?? false;

        if (!$hash || !$userId || !$providerId || !$reference) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {
            $resData = $this->getPlayerDataById($userId);
            if (!$resData) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }

            $profileId = $resData['profile_id'] ?? $userId;
            $iUtils = new IntegrationsUtils();
            $balances = $iUtils->GetPlayerBalance($profileId);
            $eTax = $iUtils->CalculateExciseTax($amount);
            $stakeTax = $eTax['stakeTax'];

            $bet_extra_data = [
                'msisdn' => $resData['msisdn'],
                'ip' => $this->ipAddress,
                'gameName' => 'Pragmatic',
                'roundId' => $roundId,
                'amount' => $amount,
                'providerId' => $providerId,
                'timestamp' => $timestamp,
                'bonusCode' => $bonusCode,
                'roundDetails' => $roundDetails,
                'platform' => $platform,
                'refund_reference' => $reference
            ];

            $betChecks = $this->getVirtualBet($userId, $gameId, $roundId, $reference);

            if (!$betChecks) {
                return $this->createApiResponse(120, "Transaction not found", null, $start_time);
            }

            $lastSuccessfulTransaction = null;
            foreach ($betChecks as $betCheck) {
                $Transaction = $this->checkTransaction($betCheck['bet_transaction_id']);
                if (!$Transaction) {
                    continue;
                }
                if ($betCheck['status'] != 0) {
                    $lastSuccessfulTransaction = ['transactionId' => $betCheck['bet_credit_transaction_id']];
                    continue;
                }
                $amount = abs($Transaction['amount']);
                $winResult = $this->UpdateWins(
                    $betCheck,
                    $betCheck['game_id'],
                    $betCheck['bet_transaction_id'],
                    $amount,
                    1,
                    $bet_extra_data,
                    $timestamp,
                    'REFUND'
                );
                $lastSuccessfulTransaction = ['transactionId' => $winResult['transaction_id']];
            }

            return $this->createApiResponse(0, 'Success', $lastSuccessfulTransaction, null, $start_time);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());
            return $this->createApiResponse(120, 'Internal server error Refund', null, $start_time);
        }
    }

    function Adjustment()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->getPost();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request: " . json_encode($data));

        $hash = $data['hash'] ?? false;
        $userId = $data['userId'] ?? false;
        $reference = $data['reference'] ?? false;
        $providerId = $data['providerId'] ?? false;
        $platform = $data['platform'] ?? false;
        $amount = $data['amount'] ?? null;
        $gameId = $data['gameId'] ?? false;
        $roundId = $data['roundId'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $roundDetails = $data['roundDetails'] ?? false;
        $bonusCode = $data['bonusCode'] ?? false;
        $token = $data['token'] ?? false;

        if (!$hash || !$userId || !$providerId || $amount == null || !$reference) {
            return $this->createApiResponse(7, "Bad parameters in the request, please check post parameters", null, $start_time);
        }

        if (!$this->calculatePragmaticPlayHash($data, $hash)) {
            return $this->createApiResponse(5, "Invalid hash code.", null, $start_time);
        }

        try {
            $resData = $this->getPlayerDataById($userId);
            if (!$resData) {
                return $this->createApiResponse(2, "Player not found or logged out.", null, $start_time);
            }

            $profileId = $resData['profile_id'] ?? $userId;


            $iUtils = new IntegrationsUtils();
            $balances = $iUtils->GetPlayerBalance($profileId);
            $gameStatus = $iUtils->GetGames($gameId, Provider);

            if (!$gameStatus) {
                return $this->createApiResponse(8, "Game is not found or diabled", null, $start_time);
            }


            $eTax = $iUtils->CalculateExciseTax($amount);
            $stakeTax = $eTax['stakeTax'];

            $bet_extra_data = [
                'msisdn' => $resData['msisdn'],
                'ip' => $this->ipAddress,
                'gameName' => 'Pragmatic',
                'roundId' => $roundId,
                'amount' => $amount,
                'providerId' => $providerId,
                'timestamp' => $timestamp,
                'bonusCode' => $bonusCode,
                'roundDetails' => $roundDetails,
                'platform' => $platform
            ];

            $betCheck = $this->getVirtualBet($userId, $gameId, $roundId);
            if (!$betCheck) {
                return $this->createApiResponse(120, "Transaction not found", null, $start_time);
            }
            $firstBet = $betCheck[0];
            $WinTrans = $this->UpdateWins($firstBet, $gameId, $reference, $amount,
                1, $bet_extra_data, $timestamp, 'ADJUSTMENTS');
            if (!$WinTrans) {
                return $this->createApiResponse(120, 'Unable to update bet - '
                    . 'bet may not exist or was already processed.', null, $start_time);
            }
            if ($WinTrans && $WinTrans['duplicate']) {
                $balances_ = $iUtils->GetPlayerBalance($userId);
                return $this->createApiResponse(0, 'Success',
                    ['transactionId' => $WinTrans['transaction_id'],
                        'currency' => 'KES',
                        'cash' => floatval($balances_['balance']),
                        'bonus' => floatval($balances_['bonus'])], $start_time);
            }
            try {
                if (count($betCheck) > 1 && $WinTrans) {
                    foreach ($betCheck as $x) {
                        if ($x['bet_id'] == $firstBet['bet_id']) {
                            continue;
                        }
                        $params = $WinTrans['update_params'];
                        $params[':bet_id'] = $x['bet_id'];
                        $params[':extra_data'] = json_encode(
                            array_merge(json_decode($x['extra_data'], true) ?? [], $bet_extra_data)
                        );
                        $this->di->getShared("dbBet")->execute($WinTrans['update_sql'], $params);
                    }

                    $allBetIds = implode('|', array_column($betCheck, 'bet_transaction_id'));
                    $sqlx = "UPDATE transaction SET reference_id = :reference_id WHERE id = :id";
                    $this->di->getShared("dbTransactionsWrite")->execute($sqlx, [
                        ':reference_id' => $allBetIds,
                        ':id' => $WinTrans['transaction_id']
                    ]);
                }
            } catch (Exception $ex) {
                throw $ex;
            }

            $balances_ = $iUtils->GetPlayerBalance($userId);

            return $this->createApiResponse(0, 'Success',
                ['transactionId' => $WinTrans['transaction_id'],
                    'currency' => 'KES',
                    'cash' => floatval($balances_['balance']),
                    'bonus' => floatval($balances_['bonus'])], $start_time);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->createApiResponse(120, 'Internal server error Adjustment', null, $start_time);
        }
    }


    public function getCasinoGames()
    {
        $start_time = $this->getMicrotime();

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $appKey = $headers['x-app-key'] ?? false;
        $accessKey = $headers['x-access-key'] ?? false;

        $data = (array)$this->request->getJsonRawBody();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "]"
            . " | Request: " . json_encode($data));

        // Pagination
        $page = $data["page"] ?? 1;
        $gameName = $data["game_name"] ?? null;
        $id = $data["id"] ?? null;
        $pageSize = $data["pageSize"] ?? 12;
        $providerGameId = $data["provider_game_id"] ?? null;
        $GameProvider = $data["game_provider"] ?? null;

        $page = (int)$page;
        $pageSize = (int)$pageSize;

        $offset = ($page - 1) * $pageSize;

        if (!$appKey) {
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"],
                true);
        }

        try {
            $wUtils = new AuthUtils();
            $authInfo = $wUtils->GetApplicationKeys($appKey);
            if ($authInfo['code'] != 200) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. ' . $authInfo['message']],
                    true);
            }

            if ($accessKey) {
                $authResults = $wUtils->AuthenticateAccessKey($accessKey);
                if ($authResults['code'] != 200) {
                    return $this->HttpResponse(
                        __LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => $authResults['code'], 'message' => $authResults['message']],
                        true);
                }
            }

            $params = [];
            $conditions = [];

            if ($gameName !== null) {
                $conditions[] = 'game_name REGEXP :game_name';
                $params[':game_name'] = $gameName;
            }

            if ($GameProvider !== null) {
                $conditions[] = 'UPPER(provider) REGEXP :provider';
                $params[':provider'] = $GameProvider;
            }

            if ($id !== null) {
                $conditions[] = 'id = :id';
                $params[':id'] = $id;
            }

            if ($providerGameId === null && isset($data['provider_game_id'])) {
                $conditions[] = 'provider_game_id IS NULL';
            } elseif ($providerGameId !== null) {
                $conditions[] = 'provider_game_id = :provider_game_id';
                $params[':provider_game_id'] = $providerGameId;
            }

            $whereClause = $conditions ? 'WHERE ' . implode(' AND ', $conditions) : '';
            $limit = (int)$pageSize;
            $offsetVal = (int)$offset;

            $countSql = "SELECT COUNT(id) AS total FROM games $whereClause";
            $totalResult = $this->rawSelect("dbReadBet", $countSql, $params);
            $totalCount = $totalResult[0]['total'] ?? 0;
            $totalPages = ceil($totalCount / $pageSize);

            $sql = "SELECT id, game_name, provider, ip_address, provider_game_id"
                . ", status FROM games $whereClause ORDER BY id DESC "
                . "LIMIT $limit OFFSET $offsetVal";

            $results = $this->rawSelect("dbReadBet", $sql, $params);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['page' => $page,
                    'pageSize' => $pageSize,
                    'results' => $results,
                    'total' => $totalCount,
                    'totalPages' => $totalPages],
                false,
                true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    function getCasinoGamesPp()
    {
        $start_time = $this->getMicrotime();
        $data = (array)$this->request->getJsonRawBody();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "]"
            . " | Request: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $appKey = $headers['x-app-key'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $options = $data['options'] ?? [];

        if (!$appKey || !$hashKey) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"],
                true);
        }

        try {
            $wUtils = new AuthUtils();
            $authInfo = $wUtils->GetApplicationKeys($appKey);
            if ($authInfo['code'] != 200) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. ' . $authInfo['message']],
                    true);
            }

            $requestParams = [
                'secureLogin' => $this->settings['Pragmatic']['secureLogin'],
            ];

            if (!empty($options)) {
                $requestParams['options'] = implode(',', (array)$options);
            }

            $requestParams['hash'] = $this->generatePragmaticHash($requestParams);
            $apiUrl = $this->settings['Pragmatic']['API'] . 'http/CasinoGameAPI/getCasinoGames/';
            $headers_x = [
                'Content-Type: application/x-www-form-urlencoded',
                'Cache-Control: no-cache',
            ];

            $response = $this->sendRawPostData($apiUrl, $requestParams, $headers_x);
            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . "[" . $this->ipAddress . "]"
                . "| sendRawPostData()"
                . "|URL:$apiUrl"
                . " | Request: " . json_encode($requestParams)
                . " | Response: " . json_encode($response));

            $parsedResponse = json_decode($response['response'], true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Failed to decode API response',
                    ['code' => 500,
                        'message' => 'Invalid JSON response from Pragmatic Play API'],
                    true
                );
            }
            if (!isset($parsedResponse['error']) || $parsedResponse['error'] !== '0') {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => $parsedResponse['description'] ?? 'Error from Pragmatic Play API'],
                    true
                );
            }

            $gameList = $parsedResponse['gameList'] ?? [];
            $conGames = $this->di->getShared("dbBet");
            try {
                $conGames->begin();
                foreach ($gameList as $game) {
                    if (empty($game['gameID']) || empty($game['gameName'])) {
                        continue;
                    }

                    $gameName = strtoupper($game['gameName']);
                    $providerGameId = $game['gameID'];
                    $provider = Provider;
                    $description = $game['typeDescription'] ?? null;

                    $existingGame = $this->rawSelectOneRecord(
                        'dbBet',
                        "SELECT game_name FROM games WHERE game_name = :game_name",
                        [':game_name' => $gameName]);

                    $this->infologger->info(__LINE__ . ":" . __CLASS__
                        . "[" . $this->ipAddress . "]"
                        . " | Request_existingGame: " . json_encode($existingGame));

                    if ($existingGame) {
                        continue;
                    }

                    $insertParams = [
                        'game_name' => $gameName,
                        'provider' => $provider,
                        'provider_game_id' => $providerGameId,
                        'description' => $description,
                        'ip_address' => $this->ipAddress,
                        'status' => 1,
                        'created_at' => $this->now(),
                        'updated_at' => $this->now(),
                        'extra_data' => json_encode([
                            'gameID' => $game['gameID'],
                            'gameTypeID' => $game['gameTypeID'],
                            'technology' => $game['technology'],
                            'platform' => $game['platform'],
                            'demoGameAvailable' => $game['demoGameAvailable'],
                            'aspectRatio' => $game['aspectRatio'],
                            'technologyID' => $game['technologyID'],
                            'jurisdictions' => $game['jurisdictions'],
                        ]),
                    ];

                    $sql = $this->GenerateSql($insertParams, 'games');
                    $conGames->execute($sql['sql'], $sql['sql_arr']);
                }
                $conGames->commit();
            } catch (Exception $e) {
                $conGames->rollback();
                throw $e;
            }

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200, 'message' => 'Games imported or updated successfully'],
                false,
                true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."],
                true);
        }
    }

    private function generatePragmaticHash($data)
    {
        unset($data['hash']);
        ksort($data);
        $paramString = '';
        foreach ($data as $key => $value) {
            if (!empty($paramString)) {
                $paramString .= '&';
            }
            $paramString .= $key . '=' . $value;
        }
        $secret = $this->settings['Pragmatic']['SecretKey'];
        return md5($paramString . $secret);
    }

    function calculatePragmaticPlayHash(array $params, string $req_hash): bool
    {
        unset($params['hash']);
        $hash = $this->generatePragmaticHash($params);
        return hash_equals($hash, $req_hash);
    }

    private function UpdateWins($betCheck, $gameId, $reference, $amount, $obbs = 0.0,
                                $bet_extra_data, $timestamp, $type = null)
    {


        $conBet = $this->di->getShared("dbBet");
        $conTrxn = $this->di->getShared("dbTransactionsWrite");
        $conProfile = $this->di->getShared("dbProfileWrite");
        $clientId = 1;
        $duplicate = false;

        $isNegative = floatval($amount) < 0;
        try {
            $conProfile->begin();
            $conBet->begin();
            $conTrxn->begin();
            $transactionId = null;

            $existingExtraData = json_decode($betCheck['extra_data'], true) ?? [];
            $mergedExtraData = array_merge($existingExtraData, $bet_extra_data);

            $status = ($type == 'REFUND')
                ? $this->settings['BetStatus']['VOIDED']
                : (($amount > 0) ? $this->settings['BetStatus']['WON'] : $this->settings['BetStatus']['LOST']);

            $updateBetParams = [
                ':bet_id' => $betCheck['bet_id'],
                ':total_odd' => $obbs,
                ':status' => $status
            ];

            $updateBetSql = "UPDATE virtuals_bet SET 
                                    status=:status, 
                                    extra_data=:extra_data, 
                                    total_odd=:total_odd";


            if ($amount > 0) {
                $limits = $this->rawSelectOneRecord('dbReadBet',
                    'SELECT max_win FROM bet_limits WHERE bet_name=:bet_name AND client_id=:client_id',
                    [':bet_name' => Provider, ':client_id' => $clientId]
                );

                if ($limits && $amount >= $limits['max_win']) {
                    $amount = $limits['max_win'];
                }

                $transactionTypeId = ($amount > 0) ? TRANSACTION_TYPE_CREDIT_ID : TRANSACTION_TYPE_DEBIT_ID;
                $source = Provider . (($amount > 0) ? '_PAYOUT' : '_ADJUSTMENT');

                $referenceTypeId = $this->settings['ReferenceTypes']['Bet']['Pragmatic']['Winning'];
                $description = "$gameId Bet " . (($amount > 0) ? 'Credit' : 'Debit');

                if ($type == 'REFUND') {
                    $transactionTypeId = TRANSACTION_TYPE_REFUND;
                    $source = Provider . '_REFUND';
                    $referenceTypeId = $this->settings['ReferenceTypes']['Bet']['Pragmatic']['refund'];
                } elseif ($type == 'JACKPOT') {
                    $source = Provider . '_JACKPOT';
                } elseif ($type == 'BONUS') {
                    $source = Provider . '_BONUS';
                } elseif ($type == 'PROMO_WIN') {
                    $source = Provider . '_PROMO_WIN';
                } elseif ($type == 'ADJUSTMENT') {
                    $referenceTypeId = $this->settings['ReferenceTypes']['Bet']['Pragmatic']['adjustment'];
                }


                $extraData = [
                    'ip' => $this->ipAddress,
                    'total_events' => 1,
                    'bet_id' => $betCheck['bet_id'],
                    'trxn_date' => $timestamp
                ];


                $sql = $this->GenerateSql([
                    'profile_id' => $betCheck['profile_id'],
                    'reference_type_id' => $referenceTypeId,
                    'transaction_type_id' => $transactionTypeId,
                    'reference_id' => $reference,
                    'amount' => $amount,
                    'currency' => 'KES',
                    'source' => $source,
                    'description' => $description,
                    'extra_data' => json_encode($extraData),
                    'created_at' => $this->now()], 'transaction');

                // $conTrxn->execute($sql['sql'], $sql['sql_arr']);
                try {
                    $conTrxn->execute($sql['sql'], $sql['sql_arr']);
                    $transactionId = $conTrxn->lastInsertId();
                } catch (\PDOException $e) {
                    if ($e->getCode() == 23000) {
                        try {
                            $data = $this->request->getPost();
                            $query = " SELECT id
                                        FROM transaction
                                        WHERE reference_id = :reference
                                        AND profile_id = :profile_id
                                        LIMIT 1";
                            $params = [
                                ':reference' => $reference,
                                ':profile_id' => $betCheck['profile_id']
                            ];
                            $resData = $this->rawSelectOneRecord("dbTransactionsRead", $query, $params);
                            $duplicate = true;
                            $transactionId = $resData['id'] ?? null;
                        } catch (Exception $ex) {
                            throw $ex;
                        }
                    } else {
                        throw $e;
                    }
                }


                if (!$duplicate) {
                    $updateBetParams[':bet_credit_transaction_id'] = $transactionId;
                    $updateBetSql .= ", bet_credit_transaction_id=:bet_credit_transaction_id";

                    //                    if ($amount >= 1000) {//Approval amounts greater needs approval
                    //                        $sql = $this->GenerateSql(
                    //                                ['profile_id' => $betCheck['profile_id'],
                    //                                    'transaction_id' => $updateBetParams[':bet_credit_transaction_id'],
                    //                                    'amount' => $amount,
                    //                                    'currency' => 'KES',
                    //                                    'source' => Provider . '_PAYOUT',
                    //                                    'description' => "BetId:" . $betCheck['bet_id']
                    //                                    . " on Pragmatic($productId) Bet Credit(s) Above KES.1000",
                    //                                    'created_at' => $this->now()],
                    //                                'transaction_pending_approval'
                    //                        );
                    //
                    //                        $conTrxn->execute($sql['sql'], $sql['sql_arr']);
                    //                        $mergedExtraData['approval_id'] = $conTrxn->lastInsertId();
                    //
                    //                        $amount = 0; //uncomment on live
                    //                    }

                    $prof_balance = $conProfile->fetchOne(
                        "SELECT IFNULL(balance,0) as balance, IFNULL(bonus,0) as bonus "
                        . "FROM profile_balance WHERE profile_id = :profile_id LIMIT 1",
                        (PHALCON_VERSION == 3 ? \Phalcon\Db::FETCH_ASSOC : \Phalcon\Db\Enum::FETCH_ASSOC),
                        [':profile_id' => $betCheck['profile_id']]
                    );

                    if (!$prof_balance) {
                        $sql = $this->GenerateSql([
                            'profile_id' => $betCheck['profile_id'],
                            'currency' => 'KES',
                            'balance' => $amount,
                            'bonus' => 0,
                            'created_at' => $this->now()
                        ], 'profile_balance');

                        $conProfile->execute($sql['sql'], $sql['sql_arr']);
                    } else {
                        $updateAmount = floatval($amount);
                        if ($type == 'ADJUSTMENT') {
                            $updateAmount = $isNegative ? -$amount : $amount;
                        }
                        $conProfile->execute("UPDATE profile_balance
                                    SET balance = balance + :cash_amount, updated_at = NOW()
                                    WHERE profile_id = :profile_id LIMIT 1", [
                            ':cash_amount' => $updateAmount,
                            ':profile_id' => $betCheck['profile_id']
                        ]);
                        if ($conProfile->affectedRows() < 1) {
                            throw new Exception("Error<>Updating Player balance. KES.$amount failed!");
                        }
                    }

                    $updateBetParams[':extra_data'] = json_encode($mergedExtraData);
                    $updateBetSql .= " WHERE bet_id=:bet_id LIMIT 1";


                    if (!in_array($type, ['ADJUSTMENT', 'JACKPOT', 'BONUS', 'PROMO_WIN'])) {
                        $conBet->execute($updateBetSql, $updateBetParams);
                        if ($conBet->affectedRows() < 1) {
                            throw new Exception("Error<>Updating Virtual bet final "
                                . "status(" . $updateBetParams['status'] . ") failed!");
                        }
                    }
                }
            } else {
                if (!in_array($type, ['ADJUSTMENT', 'JACKPOT', 'BONUS', 'PROMO_WIN'])) {
                    $updateBetParams[':witholding_tax'] = 0;
                    $updateBetParams[':bet_credit_transaction_id'] = null;
                    $mergedExtraData['note'] = "No payout due - amount is zero";
                    $updateBetParams[':extra_data'] = json_encode($mergedExtraData);

                    $updateBetSql .= ", witholding_tax=:witholding_tax,bet_credit_transaction_id=:bet_credit_transaction_id";
                    $updateBetSql .= " WHERE bet_id=:bet_id LIMIT 1";

                    $conBet->execute($updateBetSql, $updateBetParams);

                    if ($conBet->affectedRows() < 1) {
                        throw new Exception("Error<>Updating bet with zero payout failed!");
                    }
                }
                $transactionId = null;
            }

            $conProfile->commit();
            $conTrxn->commit();
            $conBet->commit();


            // return $transactionId;
            return [
                'transaction_id' => $transactionId,
                'update_sql' => $updateBetSql,
                'update_params' => $updateBetParams,
                'duplicate' => $duplicate
            ];

        } catch (Exception $ex) {
            $conProfile->rollback();
            $conTrxn->rollback();
            $conBet->rollback();
            throw $ex;
        } finally {
            $conProfile = null;
            $conTrxn = null;
            $conBet = null;
        }
    }

    public function getPlayerDataById($userId)
    {
        $statusActive = $this->settings['Account']['ProfileStatus']['Active'];

        $query = "SELECT pl.profile_id,p.msisdn,p.name,p.acc_number,p.hash,pl.access_token"
            . ", IFNULL(pb.balance, 0.00) AS balance, IFNULL(pb.bonus, 0.00) AS bonus "
            . "FROM profile_login pl JOIN profile p ON pl.profile_id = p.id "
            . "LEFT JOIN profile_balance pb ON p.id = pb.profile_id "
            . "WHERE pl.profile_id = :profile_id AND pl.token_expiry_date >= NOW() "
            . "AND pl.status = :status";

        $params = [
            ':profile_id' => $userId,
            ':status' => $statusActive
        ];

        $resData = $this->rawSelectOneRecord("dbProfileWrite", $query, $params);
        return $resData;
    }

    public function createApiResponse($code, $description, $data = null, $starttime)
    {
        $response = [
            'error' => (string)$code,
            'description' => $description,
        ];

        if (is_array($data) && !empty($data)) {
            $response = array_merge($response, $data);
        }

        $json = json_encode($response, JSON_UNESCAPED_UNICODE);
        if ($json == false) {
            throw new \RuntimeException('JSON encoding error: ' . json_last_error_msg());
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "]"
            . " | Took " . $this->CalculateTAT($starttime) . " Sec"
            . " | Response: " . $json);

        return $json;
    }

    protected function getVirtualBet(string $userId, string $gameId = null, string $roundId, string $reference = null, string $bonusCode = null)
    {

        $query = "
            SELECT 
                bet_id,
                profile_id,
                bet_transaction_id,
                bet_credit_transaction_id,
                extra_data,
                game_id,
                status,
                (bet_amount - excise_tax) AS stake_after_tax
            FROM virtuals_bet
            WHERE profile_id = :profile_id
        ";

        $params = [':profile_id' => $userId];
        if ($bonusCode != null) {
            $query .= " AND bet_reference REGEXP :bonus_code";
            $params[':bonus_code'] = $bonusCode;
        } else if ($reference != null) {
            $query .= " AND bet_reference REGEXP  :reference";
            $params[':reference'] = $reference;
        } else {
            $query .= " AND bet_reference REGEXP :bet_round_id";
            $params[':bet_round_id'] = $roundId;
            if ($gameId != null) {
                $query .= " AND game_id = :game_id";
                $params[':game_id'] = $gameId;
            }
        }
        $data = $this->rawSelect('dbBet', $query, $params);
        return $data;
    }

    private function checkForIdempotency($profileId, $betReference)
    {
        $query = "SELECT bet_id,bet_reference,bet_transaction_id FROM virtuals_bet WHERE profile_id = :profile_id "
            . "AND bet_reference = :bet_reference";
        $resData = $this->rawSelectOneRecord("dbBet", $query,
            ['profile_id' => $profileId,
                'bet_reference' => $betReference
            ]);

        return $resData;
    }


    private function checkTransaction($reference)
    {

        $query = "SELECT * FROM transaction WHERE  id = :id";

        $resData = $this->rawSelectOneRecord("dbTransactionsRead", $query,
            ['id' => $reference]);
        return $resData;
    }

    private function insertAward($bet_id, $game_id, $award_type, $value, $prize_type, $extradata, $prize_code)
    {
        try {
            $params = [
                'bet_id' => $bet_id,
                'award_type' => $award_type,
                'game_id' => $game_id,
                'value' => $value,
                'prize_type' => $prize_type,
                'extradata' => json_encode($extradata),
                'prize_code' => $prize_code
            ];

            $sqlData = $this->GenerateSql($params, 'bet_awards');
            $con = $this->di->getShared("dbBet");
            $con->execute($sqlData['sql'], $sqlData['sql_arr']);
            return null;
        } catch (Exception $e) {
            $this->infologger->error("Error inserting award: " . $e->getMessage());
            throw $e;
        }
    }

    function CheckJackpots()
    {
        $start_time = $this->getMicrotime();
        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "]"
            . " | Request: " . json_encode($data));
        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $appKey = $headers['x-app-key'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $type = $data['type'] ? $data['type'] : 'active';
        $jack_id = $data['mainJackpotID'] ?? false;
        $startime = $data['start'] ?? false;
        $endtime = $data['end'] ?? false;


        if ($startime && !is_numeric($startime)) {
            $startime = strtotime($startime) * 1000;
        }
        if ($endtime && !is_numeric($endtime)) {
            $endtime = strtotime($endtime) * 1000;
        }

        if (!$appKey || !$hashKey || !$type) {
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"],
                true);
        }


        try {
            $wUtils = new AuthUtils();
            $authInfo = $wUtils->GetApplicationKeys($appKey);
            if ($authInfo['code'] != 200) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. ' . $authInfo['message']],
                    true);
            }

            if (!$wUtils->HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'],
                    true);
            }

            $Params = ['secureLogin' => $this->settings['Pragmatic']['secureLogin']];
            $Params['hash'] = $this->generatePragmaticHash($Params);
            $responsex = $this->sendHttpGetGames($this->settings['Pragmatic']['API'] . "http/SystemAPI/environments", $Params, ['Content-Type: application/x-www-form-urlencoded',
                'Cache-Control: no-cache']);
            $envData = json_decode($responsex['response'], true);
            if ($envData['error'] !== "0") {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Environment config failed',
                    ['code' => 400, 'message' => $envData['description'], 'url' => $this->settings['Pragmatic']['API'] . "http/SystemAPI/environments" . http_build_query($Params)],
                    true
                );
            }
            $apiDomain = $envData['environments'][0]['apiDomain'];


            $params = ['login' => $this->settings['Pragmatic']['secureLogin']];

            $X_url = "https://$apiDomain/IntegrationService/v3/JackpotFeeds/extended/";


            switch ($type) {
                case 'active':
                    $params['currency'] = 'KES';
                    $url_ = $X_url . "jackpots/";
                    break;
                case 'winners':
                    $params['startTimepoint'] = $startime;
                    $params['endTimepoint'] = $endtime;
                    $url_ = $X_url . "winners/";
                    break;
                case 'winnings':
                    $params['currency'] = 'KES';
                    $params['mainJackpotID'] = $jack_id;
                    $url_ = $X_url . "winnings/";
                    break;

                default:
                    return $this->HttpResponse(
                        __LINE__ . ":" . __CLASS__,
                        200,
                        'Invalid type',
                        ['code' => 422, 'message' => "Invalid type specified"],
                        true
                    );
            }

            $params['hash'] = $this->generatePragmaticHash($params);

            $response = $this->sendHttpGetGames($url_, $params, ['Content-Type: application/x-www-form-urlencoded',
                'Cache-Control: no-cache']);

            $responseData = json_decode($response['response'], true);
            $statusCode = $response['statusCode'] ?? false;


            if (!in_array($statusCode, [200, 202, 202])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    $statusCode,
                    'Request is not successful',
                    [
                        'url' => $url_ . http_build_query($params),
                        'Envs' => $envData['environments'],
                        'message' => $responseData['description'] ?? 'Unknown error',
                        'response' => $responseData
                    ], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                [$responseData],
                false, true);


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    function CheckFBS()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Check FBS";
        $data = (array)$this->request->getJsonRawBody();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "]"
            . " | Request CheckFBS: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $type = $data['type'] ? $data['type'] : 'getplayerfrb';

        if (!$Authorization || !$hashKey || !$appKey || !$accessToken || !$type) {
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"],
                true);
        }


        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $params = ['secureLogin' => $this->settings['Pragmatic']['secureLogin']];

            $X_url = $this->settings['Pragmatic']['API'] . "http/FreeRoundsBonusAPI/";


            $body = [];
            switch ($type) {
                case 'create':

                    $params['bonusCode'] = $data['bonusCode'] ?? null;
                    $gameList = $data['gameList'] ?? [];
                    $expirationDate = $data['expirationDate'] ?? null;
                    $validityDate = $data['validityDate'] ?? null;
                    $expirationPeriod = $data['expirationPeriod'] ?? null;
                    $validityPeriod = $data['validityPeriod'] ?? null;
                    $rounds = $data['rounds'] ?? null;
                    $periodOfTime = $data['periodOfTime'] ?? null;
                    $frType = $data['frType'] ?? null;

                    if (isset($data['startDate'])) {
                        $params['startDate'] = (int)strtotime($data['startDate']);
                    }

                    if ($expirationDate !== null) {
                        $params['expirationDate'] = (int)strtotime($expirationDate);
                    }
                    if ($validityDate !== null) {
                        $params['validityDate'] = (int)strtotime($validityDate);
                    }
                    if ($expirationPeriod !== null) {
                        $params['expirationPeriod'] = (int)$expirationPeriod; // minutes
                    }
                    if ($validityPeriod !== null) {
                        $params['validityPeriod'] = (int)$validityPeriod; // minutes
                    }
                    if ($periodOfTime !== null) {
                        $params['periodOfTime'] = (int)$periodOfTime; // seconds
                    }

                    if ($rounds !== null) {
                        $params['rounds'] = $rounds;
                    }

                    if ($frType !== null) {
                        $params['frType'] = $frType;
                    }

                    $body['gameList'] = $gameList;

                    $url_ = $X_url . "v2/bonus/create";
                    break;
                case 'cancel':
                    $params['bonusCode'] = $data['bonusCode'] ?? null;
                    $url_ = $X_url . "v2/bonus/cancel";
                    break;

                case 'getplayerfrb':
                    $params['playerId'] = $data['playerId'] ?? null;
                    $url_ = $X_url . "getPlayersFRB/";
                    break;
                case 'addplayers':
                    $params['bonusCode'] = $data['bonusCode'] ?? null;
                    if (!empty($data['requestId'])) {
                        $params['requestId'] = $data['requestId'];
                    }

                    $body['playerList'] = $data['playerList'] ?? []; //max 5000;
                    $url_ = $X_url . "v2/players/add/";
                    break;

                case 'addplayer':
                    $params['bonusCode'] = $data['bonusCode'] ?? null;
                    $params['playerId'] = $data['playerId'] ?? null;

                    if (!empty($data['requestId'])) {
                        $params['requestId'] = $data['requestId'];
                    }

                    $body = ['config' => []];
                    if (isset($data['rounds'])) {
                        $body['config']['rounds'] = (int)$data['rounds'];
                    }
                    if (isset($data['expirationDate'])) {
                        $body['config']['expirationDate'] = strtotime($data['expirationDate']);
                    }
                    if (isset($data['validityDate'])) {
                        $body['config']['validityDate'] = strtotime($data['validityDate']);
                    }
                    if (isset($data['periodOfTime'])) {
                        $body['config']['periodOfTime'] = (int)$data['periodOfTime'];
                    }
                    $url_ = $X_url . "v2/bonus/player/add";
                    break;

                case 'removeplayers':
                    $params['bonusCode'] = $data['bonusCode'] ?? null;
                    if (!empty($data['requestId'])) {
                        $params['requestId'] = $data['requestId'];
                    }
                    $body['playerList'] = $data['playerList'] ?? [];
                    $url_ = $X_url . "v2/players/remove/";
                    break;

                case 'creatplayerspin':
                    $gameList = $data['gameList'] ?? [];
                    $expirationDate = $data['expirationDate'] ?? null;
                    $validityDate = $data['validityDate'] ?? null;
                    $expirationPeriod = $data['expirationPeriod'] ?? null;
                    $validityPeriod = $data['validityPeriod'] ?? null;
                    $currency = $data['currency'] ?? 'KES';
                    $playerId = $data['playerId'] ?? null;
                    $bonusCode = $data['bonusCode'] ?? null;
                    $startDate = $data['startDate'] ?? null;
                    $rounds = $data['rounds'] ?? null;
                    $periodOfTime = $data['periodOfTime'] ?? null;
                    $frType = $data['frType'] ?? null;


                    $params['bonusCode'] = $bonusCode;
                    $params['playerId'] = $playerId;
                    $params['currency'] = $currency;


                    if (!empty($data['requestId'])) {
                        $params['requestId'] = $data['requestId'];
                    }
                    if (isset($data['startDate'])) {
                        $params['startDate'] = (int)strtotime($data['startDate']);
                    }
                    if ($expirationDate !== null) {
                        $params['expirationDate'] = (int)strtotime($expirationDate);
                    }
                    if ($validityDate !== null) {
                        $params['validityDate'] = (int)strtotime($validityDate);
                    }
                    if ($expirationPeriod !== null) {
                        $params['expirationPeriod'] = (int)$expirationPeriod; // minutes
                    }
                    if ($validityPeriod !== null) {
                        $params['validityPeriod'] = (int)$validityPeriod; // minutes
                    }
                    if ($periodOfTime !== null) {
                        $params['periodOfTime'] = (int)$periodOfTime; // seconds
                    }

                    if ($rounds !== null) {
                        $params['rounds'] = $rounds;
                    }

                    if ($frType !== null) {
                        $params['frType'] = $frType;
                    }
                    $body['gameList'] = $gameList;

                    $url_ = $X_url . "v2/bonus/player/create/";
                    break;

                case 'getbetscales':
                    $body['gameIds'] = $data['gameIds'] ?? [];
                    $body['currencies'] = $data['currencies'] ?? ['KES'];
                    $url_ = $this->settings['Pragmatic']['API'] . "http/CasinoGameAPI/getBetScales";
                    break;

                default:
                    return $this->HttpResponse(
                        __LINE__ . ":" . __CLASS__,
                        200,
                        'Invalid type',
                        ['code' => 422, 'message' => "Invalid type specified"],
                        true
                    );
            }

            $params['hash'] = $this->generatePragmaticHash($params);
            $X_url = $url_ . "?" . http_build_query($params);

            $headers = [
                'Content-Type: application/json',
                'Accept: application/json'
            ];
            $response = $this->sendRawPostData($X_url, $body, $headers);

            $responseData = json_decode($response['response'], true);
            $statusCode = $response['statusCode'] ?? 500;


            if (!in_array($statusCode, [200, 202, 202])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    $statusCode,
                    'Request is not successful',
                    [
                        'url' => $url_ . "?" . http_build_query($params),
                        'message' => $responseData['description'] ?? 'Unknown error',
                        'response' => $responseData
                    ], true);
            }


            if (in_array($type, ['create', 'creatplayerspin', 'cancel']) &&
                isset($data['bonusCode']) &&
                trim($data['bonusCode']) !== ''
            ) {
                $rows = $this->rawSelect(
                    'dbBonusWrite',
                    "SELECT id FROM promotion WHERE promo_name = :bonusCode",
                    ['bonusCode' => $data['bonusCode']]
                );

                $bonusExists = !empty($rows);
                if ($type == 'cancel') {
                    if (!$bonusExists) {
                        return $this->HttpResponse(
                            __LINE__ . ":" . __CLASS__,
                            200,
                            'Promo ID not found',
                            ['code' => 404],
                            true
                        );
                    }
                    $promoId = $rows[0]['id'];
                    $normalizedBonusCode = trim(strtoupper($data['bonusCode']));
                    $updateParams1 = [
                        ':original_promo_name' => $normalizedBonusCode,
                        ':new_promo_name' => $normalizedBonusCode . 'CANCELLED' . time(),
                        ':status' => 0
                    ];
                    $updateSql1 = "
                            UPDATE promotion
                            SET status = :status, promo_name = :new_promo_name
                            WHERE TRIM(UPPER(promo_name)) = :original_promo_name
                            LIMIT 1
                        ";
                    $this->rawUpdateWithParams('dbBonusWrite', $updateSql1, $updateParams1);

                    // $updateParams2 = [
                    //     ':promo_id' => $promoId,
                    //     ':status' => 0
                    // ];
                    // $updateSql2 = "UPDATE promotion_settings SET status = :status WHERE promo_id = :promo_id";
                    // $this->rawUpdateWithParams('dbBonusWrite', $updateSql2, $updateParams2);

                    return $this->HttpResponse(
                        __LINE__ . ":" . __CLASS__,
                        200,
                        'Request is successful Promo Canceled',
                        [$responseData],
                        false,
                        true
                    );
                } elseif ($bonusExists) {
                    return $this->HttpResponse(
                        __LINE__ . ":" . __CLASS__,
                        200,
                        'Request is successful_but bonus already exists in Liden db (try again with another bonus id)',
                        [$responseData],
                        false,
                        true
                    );
                } else {
                    $startDate = $data['startDate'] ?? '';
                    $ending_date = $data['expirationDate'] ?? '';
                    if (empty($ending_date) && !empty($data['expirationPeriod'])) {
                        $dt = new DateTime($startDate);
                        $dt->add(new DateInterval("PT{$data['expirationPeriod']}M"));
                        $ending_date = $dt->format("Y-m-d H:i:s");
                    }
                    if (empty($ending_date)) {
                        $dt = new DateTime($startDate);
                        $dt->add(new DateInterval("P30D"));
                        $ending_date = $dt->format("Y-m-d H:i:s");
                    }

                    $promo_details = [
                        'rounds' => $data['rounds'] ?? null,
                        'gameList' => $body['gameList'],
                        'currency' => $data['currency'] ?? 'KES'
                    ];
                    if ($type == 'creatplayerspin') {
                        $promo_details['player_id'] = $data['playerId'] ?? null;
                    }
                    $promo_id = $this->rawInsertBulk('dbBonusWrite', 'promotion', [
                        'promo_type_id' => $this->settings['Pragmatic']['Bonus_type'],
                        'promo_name' => $data['bonusCode'] ?? null,
                        'promo_url' => 'https://mossbets.com/',
                        'promo_details' => json_encode($promo_details),
                        'promo_images' => 'https://www.pragmaticplay.com/wp-content/themes/gp-theme-basic/libs/dist/images/PP-white-logo.svg',
                        'status' => 1,
                        'starting_date' => $startDate,
                        'ending_date' => $ending_date,
                        'created_at' => $this->now(),
                        'updated_at' => $this->now()
                    ]);

                    // $body = json_decode($this->request->getRawBody(), true);
                    // foreach ($body['gameList'] as $game) {
                    //     if (empty($game['betValues'])) {
                    //         continue;
                    //     }
                    //     foreach ($game['betValues'] as $betValue) {
                    //         if (!isset($betValue['betPerLine'])) {
                    //             continue;
                    //         }
                    //         $this->rawInsertBulk('dbBonusWrite', 'promotion_settings', [
                    //             'promo_id' => $promo_id,
                    //             'bonus_amount' => $betValue['betPerLine'],
                    //             'bet_count' => $data['betCount'] ?? 1,
                    //             'min_odds' => $data['minOdds'] ?? 0.00,
                    //             'min_odds_per_pick' => $data['minOddsPerPick'] ?? 0.00,
                    //             'min_stake' => $data['minStake'] ?? 0.00,
                    //             'min_selections' => $data['minSelections'] ?? 0,
                    //             'max_times' => $data['maxTimes'] ?? 1,
                    //             'max_win' => $data['maxWin'] ?? 0.00,
                    //             'frequency' => $data['frequency'] ?? 'once',
                    //             'cumulative_stage_wager' => $data['cumulativeStageWager'] ?? 'NO',
                    //             'market_conditions' => $data['marketConditions'] ?? null,
                    //             'sport_id' => $data['sportId'] ?? null,
                    //             'deduct_stake' => $data['deductStake'] ?? 1,
                    //             'restrict_withdrawals' => $data['restrictWithdrawals'] ?? 0,
                    //             'allow_duplicate_events' => $data['allowDuplicateEvents'] ?? 1,
                    //             'expiry_period' => $data['expiryPeriod'] ?? 48,
                    //             'status' => 1,
                    //             'created_at' => $this->now(),
                    //             'update_at' => $this->now()
                    //         ]);
                    //     }
                    // }

                }
            }


            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                [$responseData],
                false, true);

//            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                200,
//                'Request is successful',
//                ['code' => 200,
//                    'message' => [$responseData]],
//                false, true);


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }



//    function CheckFBS() {
//        $start_time = $this->getMicrotime();
//        $permissionName = "Check FBS";
//        $data = (array) $this->request->getJsonRawBody();
//
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "]"
//            . " | Request CheckFBS: " . json_encode($data));
//
//        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
//        $Authorization = $headers['x-authorization'] ?? false;
//        $hashKey = $headers['x-hash-key'] ?? false;
//        $appKey = $headers['x-app-key'] ?? false;
//        $accessToken = $headers['x-access'] ?? false;
//
//
//        $type = $data['type'] ?? 'getplayerfrb';
//
//        if (!$Authorization || !$hashKey || !$appKey || !$accessToken || !$type) {
//            return $this->HttpResponse(
//                __LINE__ . ":" . __CLASS__,
//                200,
//                'Request is not successful',
//                ['code' => 422, 'message' => "Mandatory field(s) required!"],
//                true);
//        }
//
//
//        try {
//            $channel = UserUtils::GetApplicationKeys($appKey);
//            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    200,
//                    'Request is not successful',
//                    ['code' => 401,
//                        'message' => 'Authorization Error. Invalid Key!'], true);
//            }
//
//            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    200,
//                    'Request is not successful',
//                    ['code' => 401,
//                        'message' => 'Security Error. Wrong authentictated request!'], true);
//            }
//
//            $authResponse = UserUtils::QuickAuthenticate($accessToken);
//            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    200,
//                    'Request Requires Authentication',
//                    ['code' => $authResponse['code'],
//                        'message' => $authResponse['message']], true);
//            }
//
//            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    200,
//                    'Request is not successful',
//                    ['code' => 403,
//                        'message' => "Action require priviledged access!"], true);
//            }
//
//
//            $params = ['secureLogin' => $this->settings['Pragmatic']['secureLogin']];
//
//            $X_url=$this->settings['Pragmatic']['API']."http/FreeRoundsBonusAPI/v2/";
//
//
//            $body =[];
//            switch ($type) {
//                case 'create':
//
//                    $params['bonusCode']=$data['bonusCode']?? null;
//                    $gameList =$data['gameList']?? [];
//                    $expirationDate= $data['expirationDate']?? null;
//                    $validityDate=$data['validityDate'] ?? null;
//                    $expirationPeriod = $data['expirationPeriod'] ?? null;
//                    $validityPeriod= $data['validityPeriod']?? null;
//                    $rounds= $data['rounds']?? null;
//                    $periodOfTime= $data['periodOfTime']?? null;
//                    $frType= $data['frType']?? null;
//
//                    if (isset($data['startDate'])) {
//                        $params['startDate'] = (int)strtotime($data['startDate']);
//                    }
//
//                    if ($expirationDate !== null) {
//                        $params['expirationDate'] = (int)strtotime($expirationDate);
//                    }
//                    if ($validityDate !== null) {
//                        $params['validityDate'] = (int)strtotime($validityDate);
//                    }
//                    if ($expirationPeriod !== null) {
//                        $params['expirationPeriod'] = (int)$expirationPeriod; // minutes
//                    }
//                    if ($validityPeriod !== null) {
//                        $params['validityPeriod'] = (int)$validityPeriod; // minutes
//                    }
//                    if ($periodOfTime !== null) {
//                        $params['periodOfTime'] = (int)$periodOfTime; // seconds
//                    }
//
//                    if ($rounds !== null) {
//                        $params['rounds'] = $rounds;
//                    }
//
//                    if ($frType !== null) {
//                        $params['frType'] = $frType;
//                    }
//
//                    $body['gameList'] =   $gameList;
//
//                    $url_ = $X_url."bonus/create";
//                    break;
//                case 'cancel':
//                    $params['bonusCode'] = $data['bonusCode']?? null;
//                    $url_ = $X_url."bonus/cancel";
//                    break;
//
//                case 'getplayerfrb':
//                    $params['playerId'] =$data['playerId']?? null;
//                    $url_ = $X_url."getPlayersFRB/";
//                    break;
//                case 'addplayers':
//                    $params['bonusCode'] = $data['bonusCode']?? null;
//                    $requestId = $data['requestId']?? null;
//                    if (!empty($data['requestId'])) {
//                        $params['requestId'] = $data['requestId'];
//                    }
//
//                    $body['playerList'] =  $data['playerList']?? []; //max 5000;
//                    $url_ = $X_url."players/add/";
//                    break;
//
//                case 'addplayer':
//                    $params['bonusCode'] = $data['bonusCode'] ?? null;
//                    $params['playerId']  = $data['playerId'] ?? null;
//
//                    if (!empty($data['requestId'])) {
//                        $params['requestId'] = $data['requestId'];
//                    }
//
//                    $body = ['config' => []];
//                    if (isset($data['rounds'])) {
//                        $body['config']['rounds'] = (int)$data['rounds'];
//                    }
//                    if (isset($data['expirationDate'])) {
//                        $body['config']['expirationDate'] = strtotime($data['expirationDate']);
//                    }
//                    if (isset($data['validityDate'])) {
//                        $body['config']['validityDate'] =  strtotime($data['validityDate']);
//                    }
//                    if (isset($data['periodOfTime'])) {
//                        $body['config']['periodOfTime'] = (int)$data['periodOfTime'];
//                    }
//                    $url_ = $X_url . "bonus/players/add/?".http_build_query($params);
//                    break;
//
//                case 'removeplayers':
//                    $params['bonusCode'] = $data['bonusCode'] ?? null;
//                    if (!empty($data['requestId'])) {
//                        $params['requestId'] = $data['requestId'];
//                    }
//                    $body['playerList'] = $data['playerList'] ?? [];
//                    $url_ = $X_url."players/remove/";
//                    break;
//
//                case 'creatplayerspin':
//                    $gameList =$data['gameList']?? [];
//                    $expirationDate= $data['expirationDate']?? null;
//                    $validityDate=$data['validityDate'] ?? null;
//                    $expirationPeriod = $data['expirationPeriod'] ?? null;
//                    $validityPeriod= $data['validityPeriod']?? null;
//                    $currency= $data['currency']?? 'KES';
//                    $playerId= $data['playerId']?? null;
//                    $bonusCode= $data['bonusCode']?? null;
//                    $startDate= $data['startDate']?? null;
//                    $rounds= $data['rounds']?? null;
//                    $periodOfTime= $data['periodOfTime']?? null;
//                    $frType= $data['frType']?? null;
//                    $requestId= $data['requestId']?? null;
//                    $params = [
//                        'secureLogin' => $this->settings['Pragmatic']['secureLogin'],
//                        'bonusCode' => $bonusCode,
//                        'playerId' => $playerId,
//                        'currency' => $currency,
//                        'startDate' => $startDate,
//                        'expirationDate' => $expirationDate
//                    ];
//
//                    if ($validityDate) {
//                        $params['validityDate'] = $validityDate;
//                    }
//                    if ($rounds) {
//                        $params['rounds'] = $rounds;
//                    }
//                    if ($periodOfTime) {
//                        $params['periodOfTime'] = $periodOfTime;
//                    }
//                    if ($frType) {
//                        $params['frType'] = $frType;
//                    }
//                    if ($requestId) {
//                        $params['requestId'] = $requestId;
//                    }
//
//                    $body['gameList'] =   $gameList;
//
//                    $url_ = $X_url."bonus/player/create/";
//                    break;
//
//                case 'getbetscales':
//                    $body['gameIds'] = $data['gameIds']?? [];
//                    $body['currencies'] = $data['currencies']?? [];
//                    $url_ = $X_url."getBetScales/";
//                    break;
//
//                default:
//                    return $this->HttpResponse(
//                        __LINE__ . ":" . __CLASS__,
//                        200,
//                        'Invalid type',
//                        ['code' => 422, 'message' => "Invalid type specified"],
//                        true
//                    );
//            }
//
//
//
//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . "[" . $this->ipAddress . "]"
//                . " | Request_body: " . json_encode($body));
//
//            $params['hash'] = $this->generatePragmaticHash($params);
//
//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . "[" . $this->ipAddress . "]"
//                . " | Request_params: " . json_encode($params));
//
//            $X_url = $url_."?".http_build_query($params);
//
//            $headers = [
//                'Content-Type: application/json',
//                'Accept: application/json'
//            ];
//            $response = $this->sendRawPostData( $X_url,$body, $headers);
//
//            $responseData = json_decode($response['response'], true);
//            $statusCode = $response['statusCode'] ?? false;
//
//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . "[" . $this->ipAddress . "]"
//                . " | responseData: " . json_encode($responseData));
//
//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . "[" . $this->ipAddress . "]"
//                . " | URL: " . json_encode( $url_."?".http_build_query($params)));
//
//            if (!in_array($statusCode, [200, 202, 202])) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    $statusCode,
//                    'Request is not successful',
//                    [
//                        'url' => $url_."?".http_build_query($params),
//                        'message' => $responseData['description'] ?? 'Unknown error',
//                        'response' => $responseData
//                    ], true);
//            }
//
//
//            if (in_array($type, ['create', 'creatplayerspin'])) {
//                $startDate = $data['startDate'] ?? '';
//                $ending_date = $data['expirationDate'] ?? '';
//                if (empty($ending_date) && !empty($data['expirationPeriod'])) {
//                    $dt = new DateTime($startDate);
//                    $dt->add(new DateInterval("PT{$data['expirationPeriod']}M"));
//                    $ending_date = $dt->format("Y-m-d H:i:s");
//                }
//                if (empty($ending_date)) {
//                    $dt = new DateTime($startDate);
//                    $dt->add(new DateInterval("P30D"));
//                    $ending_date = $dt->format("Y-m-d H:i:s");
//                }
//
//                $promo_details = [
//                    'rounds' => $data['rounds'] ?? null,
//                    'gameList' => $body['gameList'],
//                    'currency' => $data['currency'] ?? 'KES'
//                ];
//                if ($type == 'creatplayerspin') {
//                    $promo_details['player_id'] = $data['playerId'] ?? null;
//                }
//
//                $rows = $this->rawSelect('dbBonusWrite',
//                    "SELECT id FROM promotion WHERE promo_name = :bonusCode",
//                    ['bonusCode' => $data['bonusCode']]
//                );
//                $exists = !empty($rows);
//                if ($exists) {
//                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                        200,
//                        'Request is successful_but bonus already exists in our db (try again with another bonus id)',
//                        [$responseData],
//                        false, true
//                    );
//                }
//
//                $promo_id = $this->rawInsertBulk('dbBonusWrite', 'promotion', [
//                    'promo_type_id' => $this->settings['Pragmatic']['Bonus_type'],
//                    'promo_name' => $data['bonusCode'] ?? null,
//                    'promo_url' => 'https://mossbets.com/',
//                    'promo_details' => json_encode($promo_details),
//                    'promo_images' => 'https://www.pragmaticplay.com/wp-content/themes/gp-theme-basic/libs/dist/images/PP-white-logo.svg',
//                    'status' => 0,
//                    'starting_date' => $startDate,
//                    'ending_date' => $ending_date,
//                    'created_at' => $this->now(),
//                    'updated_at' => $this->now()
//                ]);
//
//                foreach ($body['gameList'] as $game) {
//                    if (!isset($game['betValues'][0]['betPerLine'])) {
//                        continue;
//                    }
//
//                    $this->rawInsertBulk('dbBonusWrite', 'promotion_settings', [
//                        'promo_id' => $promo_id,
//                        'bonus_amount' => $game['betValues'][0]['betPerLine'] ?? 0.00,
//                        'bet_count' => $data['betCount'] ?? 1,
//                        'min_odds' => $data['minOdds'] ?? 0.00,
//                        'min_odds_per_pick' => $data['minOddsPerPick'] ?? 0.00,
//                        'min_stake' => $data['minStake'] ?? 0.00,
//                        'min_selections' => $data['minSelections'] ?? 0,
//                        'max_times' => $data['maxTimes'] ?? 1,
//                        'max_win' => $data['maxWin'] ?? 0.00,
//                        'frequency' => $data['frequency'] ?? 'once',
//                        'cumulative_stage_wager' => $data['cumulativeStageWager'] ?? 'NO',
//                        'market_conditions' => $data['marketConditions'] ?? null,
//                        'sport_id' => $data['sportId'] ?? null,
//                        'deduct_stake' => $data['deductStake'] ?? 1,
//                        'restrict_withdrawals' => $data['restrictWithdrawals'] ?? 0,
//                        'allow_duplicate_events' => $data['allowDuplicateEvents'] ?? 1,
//                        'expiry_period' => $data['expiryPeriod'] ?? 48,
//                        'status' => 1,
//                        'created_at' => $this->now(),
//                        'update_at' => $this->now()
//                    ]);
//                }
//
//            }
//
//
//            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                200,
//                'Request is successful',
//                [$responseData],
//                false, true);
//
//
//        } catch (Exception $ex) {
//            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
//                . "| Exception Trace:" . $ex->getTraceAsString()
//                . "| Message:" . $ex->getMessage());
//
//            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
//                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
//                200,
//                'Request is not successful',
//                ['code' => 500,
//                    'message' => "Internal Server Error."], true);
//        }
//    }
}
