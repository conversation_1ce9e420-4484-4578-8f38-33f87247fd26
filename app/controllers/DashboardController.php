<?php

/**
 * Description of DashboardController
 *
 * <AUTHOR>
 */
class DashboardController extends \ControllerBase
{


    /**
     * ViewSummaryReports
     * @return type
     */
    function ViewSummaryReports()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Summary Reports";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;

        $start = $data['start'] ?? false;
        $end = $data['end'] ?? false;
        $summary_start = $data['summary_start'] ?? false;
        $summary_end = $data['summary_end'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQueryP = " WHERE 1";
            $searchQuery = " WHERE 1";
            $searchQueryAvg = " WHERE 1";
            $searchParams = [];
            $searchParamsAvg = [];

            // Created/Updated Date Filters
            if ($start && $end) {
                $searchQueryP .= " AND p.created_at BETWEEN :start AND :stop";
                $searchQuery .= " AND transaction_summary.summary_date BETWEEN :start AND :stop";
                $searchQueryAvg .= " AND transaction_summary_averages.summary_date BETWEEN :start AND :stop";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$end 23:59:59";
                $searchParamsAvg[':start'] = "$start 00:00:00";
                $searchParamsAvg[':stop'] = "$end 23:59:59";
            } elseif ($end && !$start) {
                $searchQueryP .= " AND p.created_at <= :stop";
                $searchQuery .= " AND transaction_summary.summary_date <= :stop";
                $searchQueryAvg .= " AND transaction_summary_averages.summary_date <= :stop";
                $searchParams[':stop'] = "$end 23:59:59";
                $searchParamsAvg[':stop'] = "$end 23:59:59";
            } elseif (!$end && $start) {
                $searchQueryP .= " AND p.created_at >= :start";
                $searchQuery .= " AND transaction_summary.summary_date >= :start";
                $searchQueryAvg .= " AND transaction_summary_averages.summary_date >= :start";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParamsAvg[':start'] = "$start 00:00:00";
            }

            // Profiles
            $sql_profile_new = "SELECT COUNT(*) AS total_profiles "
                . "FROM (SELECT p.id FROM profile p "
                . $searchQueryP
                . " GROUP BY p.id) AS grouped_profiles";

            $sql_profile_total = "SELECT COUNT(*) AS total_profiles "
                . "FROM (SELECT p.id FROM profile p"
                . " GROUP BY p.id) AS grouped_profiles";

            $sql_profile_balances = "SELECT "
                . "SUM(pb.balance) AS total_balance, "
                . "SUM(pb.bonus) AS total_bonus, "
                . "COUNT(DISTINCT CASE WHEN pb.balance > 0 THEN p.id END) AS profiles_with_balance, "
                . "COUNT(DISTINCT CASE WHEN pb.bonus > 0 THEN p.id END) AS profiles_with_bonus "
                . "FROM profile p "
                . "JOIN profile_balance pb ON p.id = pb.profile_id "
                . "WHERE p.status = 1 AND pb.status = 1 ";

            // Sum-total profile balances and bonuses
            $sql_profile_liabilities = "SELECT "
                . "SUM(pb.balance) AS total_balance, "
                . "SUM(pb.bonus) AS total_bonus "
                . "FROM profile p "
                . "JOIN profile_balance pb ON p.id = pb.profile_id "
                . "WHERE p.status = 1 AND pb.status = 1 ";

            // Build summary query
//            $sql_summary = "SELECT identifier_name, summary_date, SUM(amount) AS total_amount, "
//                . "SUM(total_players) AS total_players, SUM(unique_players) AS total_unique_players "
//                . "FROM transaction_summary "
//                . "$searchQuery "
//                . "GROUP BY identifier_name";
            $sql_summary = "WITH base_summary AS (   SELECT      identifier_name,     summary_date,     SUM(amount) AS total_amount,  
               SUM(total_players) AS total_players,     SUM(unique_players) AS total_unique_players   
               FROM mossbets_transactions.transaction_summary   $searchQuery  
                GROUP BY identifier_name), withdraw_charges AS (   SELECT SUM(amount) AS charges  
                 FROM mossbets_transactions.transaction_summary  
                  $searchQuery AND identifier_name = 'WITHDRAW_CHARGES' ), 
                  reversals AS (   SELECT SUM(amount) AS reversal   FROM mossbets_transactions.transaction_summary  
                   $searchQuery AND identifier_name = 'REVERSAL') SELECT    bs.identifier_name, 
                     bs.summary_date,   CASE      WHEN bs.identifier_name = 'WITHDRAW' THEN      
                       bs.total_amount + COALESCE(wc.charges, 0) - COALESCE(rv.reversal, 0)   
                         ELSE bs.total_amount   END AS total_amount,   bs.total_players,
                            bs.total_unique_players FROM base_summary bs 
                            LEFT JOIN withdraw_charges wc ON 1=1 LEFT JOIN reversals rv ON 1=1";

            // Build averages query
            $sql_averages = "SELECT summary_date, source, service_id, identifier_name, average_stake, "
                . "average_minimum_stake, average_maximum_stake, avg_bet_count_per_player, "
                . "avg_payout_per_player, updated_at "
                . "FROM transaction_summary_averages "
                . "$searchQueryAvg";

//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . ": SQL Averages: $sql_averages "
//                . "| Params: " . json_encode($searchParamsAvg));

            // Execute
            $summary_profile_new = $this->rawSelect('dbProfile', $sql_profile_new, $searchParams);
            $summary_profile_total = $this->rawSelect('dbProfile', $sql_profile_total, null);
            $summary_profile_balances = $this->rawSelect('dbProfile', $sql_profile_balances, null);
            $summary_profile_liabilities = $this->rawSelect('dbProfile', $sql_profile_liabilities, null);

            $summary_results = $this->rawSelect('dbTrxnRead', $sql_summary, $searchParams);
            $summary_averages = $this->rawSelect('dbTrxnRead', $sql_averages, $searchParamsAvg);

            $summary_profiles = [
                'total_profiles' => $summary_profile_total[0]['total_profiles'],
                'new_profiles' => $summary_profile_new[0]['total_profiles'],
                // Add other profile stats here
                'profiles_with_balance' => $summary_profile_balances[0]['profiles_with_balance'],
                'profiles_with_bonus' => $summary_profile_balances[0]['profiles_with_bonus'],
                'liabilities' => $summary_profile_liabilities,
            ];

//            // LogUserActivity
//            $log = UserUtils::LogUserActivity([
//                'user_id' => $authResponse['data']['user_id'],
//                'activity' => strtoupper(__CLASS__) . " => ".strtoupper(__FUNCTION__),
//                'request' => $data,
//                'created_at' => $this->now(),
//            ]);
//
//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . ": LogUserActivity: " . json_encode($log)
//            );


            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Request for Summary reports was successful!',
                    'data' => [
                        'record_count' => count($summary_results),
                        'trxn_summary' => $summary_results,
                        'trxn_summary_averages' => $summary_averages,
                        'summary_profiles' => $summary_profiles
                    ]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());


            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * ViewSportsBetsSummaryReports
     */
    function ViewSportsBetsSummaryReports()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Sports Bets Summary Reports";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request ViewGameSummaryReports :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $game = $data['game'] ?? false;
        $start = $data['start'] ?? false;
        $end = $data['end'] ?? false;


        if (!$Authorization || !$hashKey || !$appKey || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            if (!$start) {
                $start = $this->now('Y-m-d');
            }

            if (!$end) {
                $end = $this->now('Y-m-d');
            }

            $searchQuery = " WHERE 1=1";
            $searchParams = [];

            if ($game) {
                $searchQuery .= " AND game_name = :game";
                $searchParams['game'] = $game;
            }
            if ($start) {
                $searchQuery .= " AND summary_date >= :start";
                $searchParams['start'] = $start;
            }
            if ($end) {
                $searchQuery .= " AND summary_date <= :end";
                $searchParams['end'] = $end;
            }


            // join mossbets_bets.sports_bet by bet_transaction_id and/or bet_credit_transaction_id
            $joins = " JOIN mossbets_bets.sports_bet sb ON sb.bet_transaction_id = t.bet_transaction_id "
                . "OR sb.bet_credit_transaction_id = t.bet_credit_transaction_id ";

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * ViewSummaryAverageReports
     * @throws Exception
     */
    function ViewSummaryAverageReports()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Summary Average Reports";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request ViewSummaryAverageReports :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $start = $data['start'] ?? false;
        $end = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

//        if (!$end) {
//            $end = $this->now('Y-m-d');
//        }
//
//        if (!$start) {
//            $start = $this->now('Y-m-d');
//        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $params = [];
            $searchQuery = " WHERE 1";

            if ($start && $end) {
                $searchQuery .= " AND transaction_summary_averages.updated_at BETWEEN :start AND :end";
                $params[':start'] = "$start 00:00:00";
                $params[':end'] = "$end 23:59:59";
            } elseif ($start && !$end) {
                $searchQuery .= " AND transaction_summary_averages.updated_at >= :start";
                $params[':start'] = "$start 00:00:00";
            } elseif (!$start && $end) {
                $searchQuery .= " AND transaction_summary_averages.updated_at <= :end";
                $params[':end'] = "$end 23:59:59";
            }

            $sql = "SELECT * FROM transaction_summary_averages $searchQuery";

            $stats = $this->rawSelect('dbTrxnRead', $sql, $params);

//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . ": Summary Average SQL: $sql"
//                . "| params:" . json_encode($params)
//                . "| stats:" . json_encode($stats)
//            );

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully queried stats data.",
                    'data' => $stats
                ], false, true);


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true
            );

        }
    }

    /**
     * ViewDashboardStats
     * @throws Exception
     */
    function ViewDashboardStats()
    {

        $start_time = $this->getMicrotime();

        $permissionName = "View Dashboard Reports";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request ViewDashboardStats :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $start = $data['start'] ?? false;
        $end = $data['end'] ?? false;
        $graph = $data['graph'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$end) {
            $end = $this->now('Y-m-d');
        }

        if (!$start) {
            $start = $this->now('Y-m-d');
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $params = [];

//            $sql_profile_active = "SELECT count(DISTINCT m.id) as total FROM profile m WHERE m.status=1";
//
//            $sql_profile_balances = "SELECT "
//                . "SUM(pb.balance) AS total_balance, "
//                . "SUM(pb.bonus) AS total_bonus, "
//                . "COUNT(DISTINCT CASE WHEN pb.balance > 0 THEN p.id END) AS profiles_with_balance, "
//                . "COUNT(DISTINCT CASE WHEN pb.bonus > 0 THEN p.id END) AS profiles_with_bonus "
//                . "FROM profile p "
//                . "JOIN profile_balance pb ON p.id = pb.profile_id "
//                . "WHERE p.status = 1 AND pb.status = 1 ";


            // new profile is of today's date
            $sql_profile_new = "SELECT COUNT(DISTINCT m.id) AS total FROM profile m WHERE DATE(m.created_at) = CURDATE()";

//            $sql_profile_inactive = "SELECT count(DISTINCT m.id) as total FROM profile m WHERE m.status=3";
//
//            $sql_profile_dormant = "SELECT count(DISTINCT m.id) as total FROM profile m WHERE m.status=7";
//
//            $sql_profile_suspended = "SELECT count(DISTINCT m.id) as total FROM profile m WHERE m.status=8";
//
//            $sql_profile_unverified = "SELECT count(DISTINCT m.id) as total FROM profile m WHERE m.status=9";
//
//            $sql_deposits = "SELECT IFNULL(SUM(m.amount), 0) as total FROM transaction_summary m "
////                . "JOIN transaction_reference tr ON m.transaction_type_id=tr.id "
//                . "JOIN reference_types rt ON m.reference_type_id=rt.id "
//                . "WHERE rt.identifier_name='DEPOSIT'";
//
//            $sql_stake = "SELECT IFNULL(SUM(m.amount), 0)  total FROM transaction_summary m JOIN reference_types rt ON m.reference_type_id = rt.id WHERE rt.identifier_name = 'STAKE'";
//
//            $sql_payouts = "SELECT IFNULL(SUM(m.amount), 0)  total FROM transaction_summary m JOIN reference_types rt ON m.reference_type_id = rt.id WHERE rt.identifier_name = 'PAYOUTS'";
//
//            $sql_reversals = "SELECT IFNULL(SUM(m.amount), 0)  total FROM transaction_summary m JOIN reference_types rt ON m.reference_type_id = rt.id WHERE rt.identifier_name = 'REVERSAL'";
//
//            $sql_withdrawals = "SELECT IFNULL(SUM(m.amount), 0)  total FROM transaction_summary m JOIN reference_types rt ON m.reference_type_id = rt.id WHERE rt.identifier_name = 'WITHDRAW'";
//
//            $sql_sms = "SELECT count(DISTINCT m.id) as total FROM outbox m ";
//
//            $sql_trans = "SELECT sum(t.amount)  total FROM transaction t";

            $where = " WHERE";
            $and = " AND";
            $and1 = ", AND";

            if ($start && $end) {
                $date = " DATE(m.created_at) BETWEEN :start AND :stop";
                $date1 = " DATE(m.created) BETWEEN :start AND :stop";
                $date2 = " DATE(m.updated) BETWEEN :start AND :stop";

                $sql_profile_new .= $and . $date;

//                $sql_deposits .= $and . $date;
//                $sql_stake .= $and . $date;
//                $sql_payouts .= $and . $date;
//                $sql_reversals .= $and . $date;
//                $sql_withdrawals .= $and . $date;
//                $sql_sms .= $and . $date;

                $params[':start'] = "$start 00:00:00";
                $params[':stop'] = "$end 23:59:59";
            }

            // grouped data analytics
            if ($graph) {
                // transactions | graph
                $label = [];
                $total = [];
                $amount = [];
                $color = [];
                // DAILY
                if ($graph == 'daily') {
                    $d = 0;
                    for ($i = 6; $i >= $d; $i--) {

                        $today = new DateTime();
                        $begin = $today->modify("-$i day")->format('Y-m-d');
                        $end = $begin;

                        $sql = "SELECT count(id) total, sum(ABS(amount)) amount FROM transaction "
                            . "WHERE DATE(created_at) BETWEEN '$begin' AND '$end'";

                        $results = $this->rawSelectOneRecord($sql);

                        $label[] = date('D', strtotime($begin));
                        $total[] = intval($results['total']);
                        $amount[] = floatval($results['amount']);
                        $color[] = $this->colors();
                    }
                } elseif ($graph == 'weekly') {
                    // WEEKLY
                    $w = 0;
                    for ($i = 7; $i >= $w; $i--) {

                        $weekNo = date('W');
                        $year = date('Y');
                        $today = new DateTime();
                        $today->setISODate($year, $weekNo);
                        $begin = $today->modify("-$i week")->format('Y-m-d');

                        $weekNo = date('W', strtotime($begin));
                        $year = date('Y', strtotime($begin));
                        $today = new DateTime();
                        $today->setISODate($year, $weekNo);
                        $end = $today->modify("+6 days")->format('Y-m-d');


                        $sql = "SELECT count(id) total, sum(ABS(amount)) amount FROM transaction "
                            . "WHERE DATE(created_at) BETWEEN '$begin' AND '$end'";

                        $results = $this->rawSelectOneRecord($sql);

                        $label[] = "W " . date('W', strtotime($begin));
                        $total[] = intval($results['total']);
                        $amount[] = floatval($results['amount']);
                        $color[] = $this->colors();
                    }
                } else {
                    // MONTHLY
                    $m = 0;
                    for ($i = 11; $i >= $m; $i--) {

                        $today = new DateTime();
                        $begin = $today->modify("-$i month")->format('Y-m-01');
                        $today = new DateTime();
                        $end = $today->modify("-$i month")->format('Y-m-t');

                        $sql = "SELECT count(id) total, sum(ABS(amount)) amount FROM transaction "
                            . "WHERE DATE(created_at) BETWEEN '$begin' AND '$end'";

                        $results = $this->rawSelectOneRecord($sql);

                        $label[] = date('M', strtotime($begin));
                        $total[] = intval($results['total']);
                        $amount[] = floatval($results['amount']);
                        $color[] = $this->colors();
                    }
                }
                $res = ['label' => $label, 'total' => $total, 'amount' => $amount, 'color' => $color];
                $stats['graph'] = $res;
            }

//            $profiles = $this->rawSelectOneRecord("dbProfile", $sql_profile_active, $params);
//            $profile_balances = $this->rawSelectOneRecord("dbProfile", $sql_profile_balances, $params);
            $profiles_new = $this->rawSelectOneRecord("dbProfile", $sql_profile_new, $params);
//            $profiles_unverified = $this->rawSelectOneRecord("dbProfile", $sql_profile_unverified, $params);
//            $profiles_dormant = $this->rawSelectOneRecord("dbProfile", $sql_profile_dormant, $params);
//            $profiles_suspended = $this->rawSelectOneRecord("dbProfile", $sql_profile_suspended, $params);
//            $profiles_inactive = $this->rawSelectOneRecord("dbProfile", $sql_profile_inactive, $params);

//            $total_deposits = $this->rawSelectOneRecord("dbTrxn", $sql_deposits, $params);
//            $total_stake = $this->rawSelectOneRecord("dbTrxn", $sql_stake, $params);
//            $total_payouts = $this->rawSelectOneRecord("dbTrxn", $sql_payouts, $params);
//            $total_reversals = $this->rawSelectOneRecord("dbTrxn", $sql_reversals, $params);
//            $total_withdrawals = $this->rawSelectOneRecord("dbTrxn", $sql_withdrawals, $params);
//            $total_transactions = $this->rawSelectOneRecord("dbTrxn", $sql_trans, $params);
//            $total_sms = $this->rawSelectOneRecord("dbSms",$sql_sms, $params);

            // Build response
            $stats['profiles'] = [
//                'active' => $profiles['total'],
                'new' => $profiles_new['total'],
//                'unverified' => $profiles_unverified['total'],
//                'dormant' => $profiles_dormant['total'],
//                'suspended' => $profiles_suspended['total'],
//                'inactive' => $profiles_inactive['total'],
            ];
//            $stats['total_balances'] = [
//                'wallet_balance' => [
//                    'total' => $profile_balances['total_balance'],
//                    'count' => $profile_balances['profiles_with_balance'],
//                ],
//                'bonus_balance' => [
//                    'total' => $profile_balances['total_bonus'],
//                    'count' => $profile_balances['profiles_with_bonus'],
//                ],
//            ];
//            $stats['transactions'] = [
//                'deposits' => $total_deposits['total'],
//                'stake' => $total_stake['total'],
//                'payouts' => $total_payouts['total'],
//                'withdrawals' => $total_withdrawals['total'],
//                'reversals' => $total_reversals['total'],
//                'all_transactions' => $total_transactions['total'],
////                'sms' => $total_sms,
//            ];

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully queried stats data.",
                    'data' => $stats
                ], false, true);


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true
            );

        }
    }


    /**
     * GameSummary
     */
    function GameSummary()
    {
        $start_time = microtime(true);

        $permissionName = "View Game Summary";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request ViewSummaryAverageReports :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $created_by = $data['created_by'] ?? false;
        $provider_name = $data['provider_name'] ?? false;
        $page = $data['page'] ?? false;
        $limit = $data['limit'] ?? false;
        $start = $data['start'] ?? false;
        $end = $data['end'] ?? false;
        $skipCache = $data['skipCache'] ?? false;
        $export = $data['export'] ?? false;
        $sort = $data['sort'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            if (!$page) {
                $page = 1;
            }

            if (!$limit || !is_numeric($limit)) {
                $limit = $this->settings['SelectRecordLimit'];
            }

            $order_arr = explode("|", $this->cleanStrSQL($sort));
            if (count($order_arr) > 1) {
                $sort = "game_summary." . $this->cleanStrSQL($order_arr[0]) . "";
                $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

                if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                    $order = 'DESC';
                }
            } else {
                $sort = 'game_summary.summary_date';
                $order = 'DESC';
            }

            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $params = [];
            $searchQuery = " WHERE 1";

            if ($created_by) {
                // is like
                $searchQuery .= " AND created_by LIKE :created_by";
                $params[':created_by'] = "%$created_by%";
            }

            if ($provider_name) {
                // is like
                $searchQuery .= " AND provider_name LIKE :provider_name";
                $params[':provider_name'] = "%$provider_name%";
            }


            if ($start && $end) {
                $searchQuery .= " AND game_summary.summary_date BETWEEN :start AND :end";
                $params[':start'] = "$start 00:00:00";
                $params[':end'] = "$end 23:59:59";
            } elseif ($start && !$end) {
                $searchQuery .= " AND game_summary.summary_date >= :start";
                $params[':start'] = "$start 00:00:00";
            } elseif (!$start && $end) {
                $searchQuery .= " AND game_summary.summary_date <= :end";
                $params[':end'] = "$end 23:59:59";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(game_summary.id) FROM game_summary  $searchQuery) as trx_count,"
                . "game_summary.id, game_summary.created_by, game_summary.provider_name, game_summary.player_count, "
                . "game_summary.total_bets, game_summary.pending_bets, game_summary.lost_bets, game_summary.total_stake, "
                . "game_summary.total_wins, game_summary.bonus_win, game_summary.total_refund_void, game_summary.maziwa, "
                . "game_summary.total_wins_count, game_summary.summary_date, game_summary.created_at, game_summary.updated_at "
                . "FROM game_summary $sql";

            $stats = $this->rawSelect('dbTrxnRead', $query, $params);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully queried game summary data.",
                    'data' => $stats
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true
            );
        }

    }


    /**
     * GetGames
     * @return type
     */
    function GetGreenerMonday()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Greener Monday";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | GetGreenerMonday Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $phone_number = $data['phone_number'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "sb." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'sb.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }
            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

//            $whereClauses = [];
//            $searchParams = [];
//
//            $whereClauses[] = "sp.bet_type = 0";
//            $whereClauses[] = "sp.bet_amount > 49";
//            $whereClauses[] = "tt.reference_type_id IN (1, 2)";
//
//            if ($phone_number) {
//                $searchParams[':msisdn'] = $phone_number;
//                $whereClauses[] = "p.msisdn = :msisdn";
//            }
//
//            if ($start && $stop) {
//                // Validate date order
//                if (strtotime($start) > strtotime($stop)) {
//                    $temp = $start;
//                    $start = $stop;
//                    $stop = $temp;
//                }
//                $searchParams[':start_date'] = $start;
//                $searchParams[':end_date'] = $stop;
//                $whereClauses[] = "DATE(sp.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)";
//            } elseif ($stop) {
//                $searchParams[':end_date'] = $stop;
//                $whereClauses[] = "DATE(sp.created_at) <= DATE(:end_date)";
//            } elseif ($start) {
//                $searchParams[':start_date'] = $start;
//                $whereClauses[] = "DATE(sp.created_at) >= DATE(:start_date)";
//            } else {
//                $whereClauses[] = "DATE(sp.created_at) > CURRENT_DATE - INTERVAL 7 DAY";
//            }
//
//            $sql = "SELECT
//        DATE(sp.created_at) AS bet_date,
//        p.msisdn AS Phone_Number,
//        COUNT(sp.bet_id) AS Total_bets,
//        SUM(sp.total_odd) AS Total_odds,
//        SUM(CASE WHEN sp.bet_type = 0 THEN sp.bet_amount ELSE 0 END) AS Stake,
//        SUM(CASE WHEN sp.bet_type = 0 AND sp.status = 1 THEN sp.possible_win ELSE 0 END) AS Win_amount,
//        COUNT(CASE WHEN sp.bet_type = 0 AND sp.status = 1 THEN sp.bet_id ELSE NULL END) AS Total_Wins,
//        SUM(CASE WHEN tt.reference_type_id = 2 THEN tt.amount ELSE 0 END) AS deposits,
//        SUM(CASE WHEN tt.reference_type_id = 1 THEN -tt.amount ELSE 0 END) AS withdrawals,
//        SUM(CASE WHEN sp.bet_type = 0 THEN sp.bet_amount ELSE 0 END) -
//        SUM(CASE WHEN sp.bet_type = 0 AND sp.status = 1 THEN sp.possible_win ELSE 0 END) AS NGR
//    FROM mossbets_bets.sports_bet AS sp
//    Left JOIN mossbets_profile.profile p ON sp.profile_id = p.id
//    LEFT JOIN mossbets_transactions.transaction AS tt ON p.id = tt.profile_id
//    WHERE " . implode(" AND ", $whereClauses) . "
//    GROUP BY p.msisdn";
////    GROUP BY DATE(sp.created_at), p.msisdn";

$whereClauses = [];
$searchParams = [];

// Core betting conditions
$whereClauses[] = "sp.bet_type = 0";
$whereClauses[] = "sp.bet_amount > 49";

if ($phone_number) {
    $searchParams[':msisdn'] = $phone_number;
    $whereClauses[] = "p.msisdn = :msisdn";
}

// Date filtering for bets (primary date range)
if ($start && $stop) {
    // Validate date order
    if (strtotime($start) > strtotime($stop)) {
        $temp = $start;
        $start = $stop;
        $stop = $temp;
    }
    $searchParams[':start_date'] = $start;
    $searchParams[':end_date'] = $stop;
    $whereClauses[] = "DATE(sp.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)";
} elseif ($stop) {
    $searchParams[':end_date'] = $stop;
    $whereClauses[] = "DATE(sp.created_at) <= DATE(:end_date)";
} elseif ($start) {
    $searchParams[':start_date'] = $start;
    $whereClauses[] = "DATE(sp.created_at) >= DATE(:start_date)";
} else {
    $whereClauses[] = "DATE(sp.created_at) > CURRENT_DATE - INTERVAL 7 DAY";
}

$sql = "SELECT 
    p.msisdn AS Phone_Number, 
    COUNT(DISTINCT sp.bet_id) AS Total_bets, 
    SUM(sp.total_odd) AS Total_odds, 
    SUM(sp.bet_amount) AS Total_Stake, 
    SUM(CASE WHEN sp.status = 1 THEN sp.possible_win ELSE 0 END) AS Total_Win_amount, 
    COUNT(CASE WHEN sp.status = 1 THEN sp.bet_id ELSE NULL END) AS Total_Wins, 
    
    (SELECT COALESCE(SUM(tt_dep.amount), 0) 
     FROM mossbets_transactions.transaction AS tt_dep 
     WHERE tt_dep.profile_id = p.id 
     AND tt_dep.reference_type_id = 2 
     " . ($start && $stop ? "AND DATE(tt_dep.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)" :
        ($stop ? "AND DATE(tt_dep.created_at) <= DATE(:end_date)" :
            ($start ? "AND DATE(tt_dep.created_at) >= DATE(:start_date)" :
                "AND DATE(tt_dep.created_at) > CURRENT_DATE - INTERVAL 7 DAY"))) . "
    ) AS Total_Deposits, 
    
    (SELECT COALESCE(SUM(tt_with.amount), 0) 
     FROM mossbets_transactions.transaction AS tt_with 
     WHERE tt_with.profile_id = p.id 
     AND tt_with.reference_type_id = 1 
     " . ($start && $stop ? "AND DATE(tt_with.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)" :
        ($stop ? "AND DATE(tt_with.created_at) <= DATE(:end_date)" :
            ($start ? "AND DATE(tt_with.created_at) >= DATE(:start_date)" :
                "AND DATE(tt_with.created_at) > CURRENT_DATE - INTERVAL 7 DAY"))) . "
    ) AS Total_Withdrawals,
    
    SUM(sp.bet_amount) - SUM(CASE WHEN sp.status = 1 THEN sp.possible_win ELSE 0 END) AS NGR,
    
    MIN(DATE(sp.created_at)) AS First_Bet_Date,
    MAX(DATE(sp.created_at)) AS Last_Bet_Date
    
FROM mossbets_bets.sports_bet AS sp 
INNER JOIN mossbets_profile.profile p ON sp.profile_id = p.id 
WHERE " . implode(" AND ", $whereClauses) . " 
GROUP BY p.id, p.msisdn 
ORDER BY Total_Stake DESC";


            $results = $this->rawSelect('dbBetsRead', $sql, $searchParams);

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . ": SQL: $sql"
                . "| all_markets params:" . json_encode($searchParams)
                // count of results
                . "| results:" . count($results)
            );

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Green Monday found!'], true);
            }

            $responseData = [
                'record_count' => count($results),
                'result' => $results
            ];

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Green Monday fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    function GetGreenerMondayOld()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Greener Monday";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | GetGreenerMonday Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $phone_number = $data['phone_number'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        // log headers
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetGreenerMonday Headers:" . json_encode($headers));

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "sb." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'sb.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }
            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($phone_number) {
                $searchParams[':msisdn'] = $phone_number;
                $searchQuery .= " AND p.msisdn = :msisdn";
            }

            if ($start && $stop) {
                $searchQuery .= " AND sp.created_at BETWEEN :start AND :stop";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop) {
                $searchQuery .= " AND sp.created_at <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($start) {
                $searchQuery .= " AND sp.created_at >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sql = "SELECT p.msisdn AS Phone_Number, COUNT(sp.bet_id) AS Number_of_bets, SUM(sp.total_odd) AS Total_odds, "
                . "SUM(CASE WHEN sp.bet_type = 0 THEN sp.bet_amount ELSE 0 END) AS Stake, "
                . "SUM(CASE WHEN sp.bet_type = 0 AND sp.status = 1 THEN sp.possible_win ELSE 0 END) AS Win_amount, "
                . "COUNT(CASE WHEN sp.bet_type = 0 AND sp.status = 1 THEN sp.bet_id ELSE NULL END) AS Wins, "
                . "SUM(CASE WHEN tt.reference_type_id = 2 THEN tt.amount ELSE 0 END) AS deposits, "
                . "SUM(CASE WHEN tt.reference_type_id = 1 THEN -tt.amount ELSE 0 END) AS withdrawals "
                . "FROM mossbets_bets.sports_bet AS sp INNER JOIN  mossbets_profile.profile p ON sp.profile_id = p.id "
                . "INNER JOIN mossbets_transactions.transaction AS tt ON p.id = tt.profile_id "
                . "WHERE DATE(sp.created_at) > CURRENT_DATE - INTERVAL 8 DAY AND sp.bet_type = 0 "
                . "AND sp.bet_amount > 49 AND tt.reference_type_id IN (1, 2) "
                . $searchQuery
                . "GROUP BY p.msisdn";

            $results = $this->rawSelect('dbBetsRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Green Monday found!'], true);
            }

            $responseData = [
                'record_count' => count($results),
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Green Monday fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetSportsBetsSummary
     */
    public function GetSportsBetsSummary()
    {
        $start_time = microtime(true);
        $permissionName = "Get SportsBooks Summary";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }
            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($start && $stop) {
                $searchQuery .= " AND created_at BETWEEN :start AND :stop";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop) {
                $searchQuery .= " AND created_at <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($start) {
                $searchQuery .= " AND created_at >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            //
            $sql = "SELECT DATE_FORMAT(created_at, '%Y-%m') AS bet_month, SUM(bet_amount) AS Total_stake, "
                . "COUNT(DISTINCT profile_id) AS monthly_active_players, "
                . "COUNT(CASE WHEN bet_type = 0 THEN bet_id END) AS Cashbet_stake, "
                . "COUNT(CASE WHEN bet_type = 1 THEN bet_id END) AS Bonus_bets, "
                . "COUNT(CASE WHEN bet_type = 2 THEN bet_id END) AS Free_bets, "
                . "SUM(CASE WHEN bet_type = 0 THEN bet_amount ELSE 0 END) AS Cash_bet_amount, "
                . "SUM(CASE WHEN bet_type = 0 AND status = 1 THEN possible_win ELSE 0 END) AS Cash_winnings, "
                . "SUM(CASE WHEN bet_type = 1 AND status = 1 THEN possible_win ELSE 0 END) AS Bonus_winnings, "
                . "SUM(CASE WHEN bet_type = 2 AND status = 1 THEN possible_win ELSE 0 END) AS Free_winnings, "
                . "COUNT(bet_id) AS Total_bets, "
                . "SUM(CASE WHEN status = 1 THEN possible_win ELSE 0 END) AS Total_winnings, "
                . "SUM(CASE WHEN bet_type = 0 THEN bet_amount ELSE 0 END) - SUM(CASE WHEN status = 1 THEN possible_win ELSE 0 END) AS GGR "
                . "FROM mossbets_bets.sports_bet GROUP BY DATE_FORMAT(created_at, '%Y-%m') ORDER BY bet_month DESC";

            $results = $this->rawSelect('dbBetsRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No SportsBooks Summary found!'], true);
            }

            $responseData = [
                'record_count' => count($results),
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'SportsBooks Summary fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetVirtualBetsSummary
     */
    public function GetVirtualBetsSummary()
    {
        $start_time = microtime(true);
        $permissionName = "Get VirtualBets Summary";
        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $provider_name = $data['provider_name'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }
            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($start && $stop) {
                $searchQuery .= " AND created_at BETWEEN :start AND :stop";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop) {
                $searchQuery .= " AND created_at <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($start) {
                $searchQuery .= " AND created_at >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            // fetch distinct provider_name
            $sql = "SELECT DISTINCT provider_name FROM virtuals_bet $searchQuery";
            $providers = $this->rawSelect('dbBetsRead', $sql, $searchParams);
            if (!$providers) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No VirtualBets Summary found!'], true);
            }

            if ($provider_name) {
                $searchQuery .= " AND provider_name = :provider_name";
                $searchParams[':provider_name'] = $provider_name;
            }

            $sql = "SELECT DATE_FORMAT(created_at, '%Y-%m') AS bet_month, provider_name, "
                . "COUNT(DISTINCT profile_id) AS monthly_active_players,"
                . "COUNT(CASE WHEN bet_type = 0 THEN bet_id END) AS cash_bet, COUNT(CASE WHEN bet_type = 1 THEN bet_id END) AS bonus_bet,"
                . "COUNT(CASE WHEN bet_type = 2 THEN bet_id END) AS free_bet, "
                . "SUM(CASE WHEN bet_type = 0 THEN bet_amount ELSE 0 END) AS Cashbet_stake, "
                . "SUM(CASE WHEN bet_type = 1 AND status = 1 THEN bet_amount ELSE 0 END) AS Bonus_stake, "
                . "SUM(CASE WHEN bet_type = 2 AND status = 1 THEN bet_amount ELSE 0 END) AS Free_stake, "
                . "COUNT(bet_id) AS Total_bets, SUM(bet_amount) AS Total_stake, "
                . "SUM(CASE WHEN bet_type = 0 AND status = 1 THEN possible_win ELSE 0 END) AS cashbet_wins, "
                . "SUM(CASE WHEN bet_type = 2 AND status = 1 THEN possible_win ELSE 0 END) AS free_bet_wins, "
                . "SUM(CASE WHEN status = 1 THEN possible_win ELSE 0 END) AS Total_winnings, "
                . "SUM(CASE WHEN bet_type = 0 THEN bet_amount ELSE 0 END) - SUM(CASE WHEN status = 1 THEN possible_win ELSE 0 END) AS GGR "
                . "FROM mossbets_bets.virtuals_bet "
                . $searchQuery . " GROUP BY DATE_FORMAT(created_at, '%Y-%m'), provider_name ORDER BY bet_month DESC";

            $results = $this->rawSelect('dbBetsRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No VirtualBets Summary found!'], true);
            }

            $responseData = [
                'record_count' => count($results),
                'result' => $results,
                'providers' => $providers
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'VirtualBets Summary fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetSoftGamingSummary
     */
    public
    function GetSoftGamingSummary()
    {
        $start_time = microtime(true);
        $permissionName = "Get SoftGaming Summary";
        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $created_by = $data['created_by'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }
            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($start && $stop) {
                $searchQuery .= " AND created_at BETWEEN :start AND :stop";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop) {
                $searchQuery .= " AND created_at <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($start) {
                $searchQuery .= " AND created_at >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            // get distinct created_by and split by : or " " get distinct
            $sql = "SELECT DISTINCT created_by FROM softgaming_bets ";
            $games = $this->rawSelect('dbBetsRead', $sql, $searchParams);
            $creatorDetails = [];
            foreach ($games as $game) {
                if (!empty($game['created_by'])) {
                    $parts = preg_split('/[: ]/', $game['created_by'], 2, PREG_SPLIT_NO_EMPTY);
                    $type = $parts[0] ?? null;
                    $name = $parts[1] ?? $parts[0];

                    $creatorDetails[] = [
                        'key' => $game['created_by'],
                        'name' => $name
                    ];
                }
            }

            if (!$creatorDetails) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No SoftGaming Summary found!'], true);
            }

            if ($created_by) {
                $searchQuery .= " AND created_by = :created_by";
                $searchParams[':created_by'] = $created_by;
            }

            $sql = "SELECT DATE_FORMAT(created_at, '%Y-%m') AS bet_month, created_by AS game, "
                . "COUNT(DISTINCT profile_id) AS monthly_active_players,"
                . "COUNT(CASE WHEN bet_type = 0 THEN bet_id END) AS cash_bet, "
                . "COUNT(CASE WHEN bet_type = 1 THEN bet_id END) AS bonus_bet, "
                . "SUM(CASE WHEN bet_type = 0 THEN bet_amount ELSE 0 END) AS Cashbet_stake, "
                . "SUM(CASE WHEN bet_type = 1 AND status = 1 THEN bet_amount ELSE 0 END) AS Bonus_stake, "
                . "SUM(CASE WHEN bet_type = 2 AND status = 1 THEN bet_amount ELSE 0 END) AS Free_stake, "
                . "COUNT(bet_id) AS Total_bets, SUM(bet_amount) AS Total_stake, "
                . "SUM(CASE WHEN bet_type = 0 AND status = 1 THEN possible_win ELSE 0 END) AS cashbet_wins, "
                . "SUM(CASE WHEN bet_type = 2 AND status = 1 THEN possible_win ELSE 0 END) AS free_bet_wins, "
                . "SUM(CASE WHEN status = 1 THEN possible_win ELSE 0 END) AS Total_winnings, "
                . "SUM(CASE WHEN bet_type = 0 THEN bet_amount ELSE 0 END) - SUM(CASE WHEN status = 1 THEN possible_win ELSE 0 END) AS GGR "
                . "FROM softgaming_bets "
                . $searchQuery . " GROUP BY DATE_FORMAT(created_at, '%Y-%m'), created_by ORDER BY bet_month DESC";

            $results = $this->rawSelect('dbBetsRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No SoftGaming Summary found!'], true);
            }

            $responseData = [
                'record_count' => count($results),
                'result' => $results,
                'games' => $creatorDetails
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'SoftGaming Summary fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }
}
