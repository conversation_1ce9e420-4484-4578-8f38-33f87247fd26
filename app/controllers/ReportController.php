<?php

class ReportController extends \ControllerBase
{

    /**
     * GetKRATaxPaymentSummary
     * @return type
     */
    function GetKRATaxPaymentSummary()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View KRA Tax Payment Summary";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetKRATaxPaymentSummary:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $export_paid = $data['export_paid'] ?? false;
        $export_unpaid = $data['export_unpaid'] ?? false;
        $export_failed = $data['export_failed'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "compliance_prn_payments_dlr." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'compliance_prn_payments_dlr.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#page$page";

            $searchQuery = ' WHERE 1';
            $searchParams = [];

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND compliance_prn_payments_dlr.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND compliance_prn_payments_dlr.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND compliance_prn_payments_dlr.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                if (is_numeric($export_paid) == 1) {
                    $searchQuery .= " AND compliance_prn_payments_dlr.status = 200";
                } elseif (is_numeric($export_unpaid) == 1) {
                    $searchQuery .= " AND compliance_prn_payments_dlr.status = 400";
                } elseif (is_numeric($export_failed) == 1) {
                    $searchQuery .= " AND compliance_prn_payments_dlr.status = 301";
                }

                $sorting = "";
            }

            // copy above below
            $sql = "SELECT (SELECT COUNT(compliance_prn_payments_dlr.id) FROM compliance_prn_payments_dlr "
                . "join compliance_prn_generation on "
                . "compliance_prn_payments_dlr.prn_number=compliance_prn_generation.prn_number $searchQuery)trx_count"
                . ",compliance_prn_payments_dlr.id,compliance_prn_payments_dlr.prn_number"
                . ",compliance_prn_generation.prn_amount,compliance_prn_generation.prn_reg_date"
                . ",compliance_prn_generation.tax_type,compliance_prn_payments_dlr.conversation_id"
                . ",compliance_prn_payments_dlr.original_conversation_id,compliance_prn_payments_dlr.response_code"
                . ",compliance_prn_payments_dlr.response_description,compliance_prn_payments_dlr.reciept_number"
                . ",compliance_prn_payments_dlr.callback_extra_data,compliance_prn_payments_dlr.status"
                . ",compliance_prn_payments_dlr.created_at FROM compliance_prn_payments_dlr "
                . "join compliance_prn_generation on "
                . "compliance_prn_payments_dlr.prn_number=compliance_prn_generation.prn_number $searchQuery $sorting";

            $results = $this->rawSelect('dbTrxn', $sql, $searchParams);

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . ": SQL: " . $sql
                . "| KRA Tax Pays params:" . json_encode($searchParams)
//                . "| results:" . json_encode($results)
            );

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Taxes summary records!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Taxes summary  records successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetKRATaxSummary
     * @return type
     */
    function GetKRATaxSummary()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View KRA Tax Summary";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request GetKRATaxSummary:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['summary_date'] = $data['summary_date'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "compliance_transaction_summary." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'compliance_transaction_summary.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#page$page";

            $searchQuery = ' WHERE 1';
            $searchParams = [];

            if ($params['summary_date']) {
                $searchParams[':summary_date'] = $params['summary_date'];
                $searchQuery .= " AND compliance_transaction_summary.summary_date=:summary_date";

                $key .= '$summary_date:' . $params['summary_date'];
            }

            if ($stop && $start) {
                $searchQuery .= " AND compliance_transaction_summary.summary_date BETWEEN :start AND :stop";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop && !$start) {
                $searchQuery .= " AND compliance_transaction_summary.summary_date <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (!$stop && $start) {
                $searchQuery .= " AND compliance_transaction_summary.summary_date >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            //
            $sql = "SELECT (SELECT COUNT(id) FROM compliance_transaction_summary  $searchQuery) as trx_count, "
                . "id as summary_id, bets as total_bets, deposit_count as total_deposits, withdrawal_count as total_withdrawals, "
                . "stake as total_stke, payout as total_payout, "
                . "gross_gaming_revenue as GGR, excirce_tax,witholding_tax, betting_tax, tax_factor, "
                . "summary_date, created_at, updated_at "
                . "FROM compliance_transaction_summary $searchQuery $sorting";

            $results = $this->rawSelect('dbTrxn', $sql, $searchParams);

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . " | GetKRATaxSummary Request:" . json_encode($data)
                . " | SQL: " . $sql
                . " | Params:" . json_encode($searchParams)
            );

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Taxes summary records!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Taxes summary  records successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetKRATaxes
     * @return type
     */
    function GetKRATaxes()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View KRA Taxes records";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['bet_id'] = $data['bet_id'] ?? false;
        $params['mobile_number'] = $data['mobile_number'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "compliance_data_transmission." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'compliance_data_transmission.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#page$page";

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($params['mobile_number']) {
                $searchParams[':msisdn'] = $params['mobile_number'];
                $searchQuery .= " AND compliance_data_transmission.mobile_number=:msisdn";

                $key .= '$msisdn:' . $params['mobile_number'];
            }

            if ($params['bet_id']) {
                $searchParams[':bet_id'] = $params['bet_id'];
                $searchQuery .= " AND compliance_data_transmission.bet_id=:bet_id";

                $key .= '$bet_id:' . $params['bet_id'];
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND compliance_data_transmission.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND compliance_data_transmission.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND compliance_data_transmission.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbTrxn',
                "select (select count(id) from compliance_data_transmission $searchQuery)trx_count"
                . ",id,bet_id,bet_type,bet_amount,bet_odds,account_number,mobile_number"
                . ",post_desc,stake_post_date,extra_data,status,created_at "
                . "from compliance_data_transmission $searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Taxes records!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Taxes records successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetAuthChannels
     * @return type
     */
    function GetAuthChannels()
    {
        $start = $this->getMicrotime();

        $permissionName = "View Authentication Channels";

        $data = (array)$this->request->get();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $channel_name = $data['channel_name'] ?? false;
        $status = $data['status'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelect('dbProfile',
                "select (select count(id) from auth_channels)trx_count,id channel_id"
                . ",name channel_name,status from auth_channels", []);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Auth Channels doesn't exists!"], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Auth Channels successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPaybills
     * @return type
     */
    function GetPaybills()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Mpesa Paybills";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $paybill_number = $data['paybill_number'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $paybill_type = $data['paybill_type'] ?? false;
        $paybill_status = $data['paybill_status'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "paybills." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'paybills.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";
            $key .= '$:user_id' . $authResponse['data']['user_id'];

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($paybill_status) {
                $searchParams[':paybill_status'] = $paybill_status;
                $searchQuery .= " AND paybills.status = :paybill_status";

                $key .= '$paybill_status:' . $paybill_status;
            }


            if ($paybill_number) {
                $searchParams[':paybill_number'] = $paybill_number;
                $searchQuery .= " AND paybills.paybill_number REGEXP :paybill_number";

                $key .= '$paybill_number:' . $paybill_number;
            }

            if ($paybill_type) {
                $searchParams[':paybill_type'] = $paybill_type;
                $searchQuery .= " AND paybills.paybill_type = :paybill_type";

                $key .= '$paybill_type:' . $paybill_type;
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND paybills.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND paybills.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND paybills.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            if ($params['skipCache'] == 1) {
                $cacheData = RedisUtils::redisRawSelectData($key);
                if ($cacheData) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => 'Queried Cache ' . $cacheData->record_count
                                . ' MPESA Paybills successfully!',
                            'data' => [
                                'record_count' => $cacheData->record_count,
                                'result' => $cacheData->result]], false, true);
                }
            }

            RedisUtils::redisRawDelete($key);

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbTrxnRead',
                "select (select count(paybills.id) from paybills left join "
                . "paybill_settings on paybills.id=paybill_settings.paybill_id $searchQuery)trx_count"
                . ",paybills.id,paybills.paybill_number,paybills.paybill_type"
                . ",paybill_settings.initiator_name,paybills.reference_id"
                . ",paybill_settings.paybill_balance,paybill_settings.action_type"
                . ",paybill_settings.allowed_ip ip_whitelist,paybills.status,paybills.created_at "
                . "from paybills left join paybill_settings on paybills.id=paybill_settings.paybill_id "
                . "$searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no MPESA Paybills!'], true);
            }

            RedisUtils::redisRawInsertData($key,
                ['record_count' => $results[0]['trx_count'],
                    'result' => $results], 600);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' MPESA Paybills successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * Referrals
     * @return type
     */
    function Referrals()
    {
        $start_time = $this->getMicrotime();
        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $mobile_number = $data['mobile_number'] ?? false;
        $status = $data['status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "referrals." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'referrals.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }


            if (!in_array($authResponse['data']['role_id'], [1, 2])) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                                200,
//                                'Request is not successful',
//                                ['code' => 403,
//                                    'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($mobile_number) {
                $searchParams[':mobile_number'] = $mobile_number;
                $searchQuery .= " AND referrals.msisdn REGEXP :mobile_number";

                $key .= '$$mobile_number:' . $mobile_number;
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND referrals.status =:status";

                $key .= '$status:' . $status;
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND referrals.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND referrals.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND referrals.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            if ($params['skipCache'] == 1) {
                $cacheData = RedisUtils::redisRawSelectData($key);
                if ($cacheData) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => 'Queried Cache ' . $cacheData->record_count
                                . ' Referrals successfully!',
                            'data' => [
                                'record_count' => $cacheData->record_count,
                                'result' => $cacheData->result]], false, true);
                }
            }

            RedisUtils::redisRawDelete($key);

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbRead',
                "select (select count(referrals.id) from referrals "
                . "JOIN customer ON referrals.cust_id=customer.id $searchQuery)trx_count"
                . ",referrals.id,referrals.cust_id,referrals.msisdn,referrals.status"
                . ",customer.msisdn referred_by,referrals.completed_on,referrals.created_at from referrals "
                . "JOIN customer ON referrals.cust_id=customer.id $searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Referrals!'], true);
            }

            RedisUtils::redisRawInsertData($key,
                ['record_count' => $results[0]['trx_count'],
                    'result' => $results], 600);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Referrals successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());


            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetSmsOutbox
     * @return type
     */
    function GetSmsOutbox()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Outbox Messages";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $dlr_status = $data['dlr_status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['mobile_number'] = $data['mobile_number'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "profile_outbox." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'profile_outbox.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";
            $key .= '$:user_id' . $authResponse['data']['user_id'];

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($dlr_status) {
                $searchParams[':dlr_status'] = $dlr_status;
                $searchQuery .= " AND profile_outbox.status REGEXP :dlr_status";

                $key .= '$dlr_status:' . $dlr_status;
            }

            if ($params['mobile_number']) {
                $searchParams[':msisdn'] = $params['mobile_number'];
                $searchQuery .= " AND profile.msisdn REGEXP :msisdn";

                $key .= '$ob_msisdn:' . $params['mobile_number'];
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND profile_outbox.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND profile_outbox.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND profile_outbox.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

//            if ($params['skipCache'] == 1) {
//                $cacheData = RedisUtils::redisRawSelectData($key);
//                if ($cacheData) {
//                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
//                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
//                        200,
//                        'Request is successful',
//                        ['code' => 200,
//                            'message' => 'Queried Cache ' . $cacheData->record_count
//                                . ' sms outbox request successfully!',
//                            'data' => [
//                                'record_count' => $cacheData->record_count,
//                                'result' => $cacheData->result]], false, true);
//                }
//            }

//            RedisUtils::redisRawDelete($key);

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbProfile',
                "select (select count(profile_outbox.id) from profile_outbox "
                . "join profile on profile_outbox.profile_id=profile.id $searchQuery)trx_count"
                . ",profile_outbox.id,profile.msisdn,profile_outbox.message"
                . ",profile_outbox.message_type,profile_outbox.sender_id"
                . ",profile_outbox.status dlr_state,profile_outbox.created_at "
                . "from profile_outbox join profile on profile_outbox.profile_id=profile.id "
                . "$searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no sms outbox!'], true);
            }

//            RedisUtils::redisRawInsertData($key,
//                    ['record_count' => $results[0]['trx_count'],
//                        'result' => $results], 600);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' sms outbox successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * BlastSMSFilters
     * @return type
     */
    function BlastSMSFilters()
    {
        $start = $this->getMicrotime();

        $permissionName = "View Bulk SMS Filters";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Queried SMS Blast filters Successfully!",
                    'data' => [
                        'sender_ids' => $this->settings['mnoApps']['DefaultSenders'],
                        'filters' => [
                            'all',
                            'test',
                            'all_depositors',
                            'non_depositors',
                            'new_profiles']]]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetSpecialReports
     * @return
     */
    function GetSpecialReports()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Special Reports";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request Special Reports:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $report_type = $data['report_type'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require privileged access!"], true);
            }

            $join = "";

            $results = [];

            if ($report_type == '1' || $report_type == 'registration') {

                $searchQuery = " WHERE 1";
                $searchParams = [];
                $sorting = "";
                if ($start && $stop) {
                    $searchQuery .= " AND p.created_at BETWEEN :start AND :stop ";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                } elseif ($start && !$stop) {
                    $searchQuery .= " AND p.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$start 23:59:59";
                } elseif (!$start && $stop) {
                    $searchQuery .= " AND p.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$stop 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                }
                // Registrations
                $join .= " JOIN profile p ON pl.profile_id=p.id ";
                $sql = "SELECT DATE(pl.created_at) as date, COUNT(pl.id) as number_of_registrations, "
                    . "SUM(CASE WHEN pl.status = 6 THEN 1 ELSE 0 END) as verified, "
                    . "SUM(CASE WHEN pl.status = 2 THEN 1 ELSE 0 END) as unverified "
                    . "FROM profile_login pl "
                    . "$join $searchQuery "
                    . "GROUP BY DATE(pl.created_at) "
                    . "ORDER BY DATE(pl.created_at) DESC";

                $results = $this->rawSelect('dbProfile', $sql, $searchParams);

                if (!$results) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is not successful',
                        ['code' => 404,
                            'message' => 'Request returned no registrations!'], true);
                }

            } else if ($report_type == '2' || $report_type == 'deposits') {
                // Deposits - Use transaction table with reference_types join
                $searchQuery = " WHERE 1";
                $searchParams = [];
                $sorting = "";
                if ($start && $stop) {
                    $searchQuery .= " AND t.created_at BETWEEN :start AND :stop ";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                } elseif ($start && !$stop) {
                    $searchQuery .= " AND t.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$start 23:59:59";
                } elseif (!$start && $stop) {
                    $searchQuery .= " AND t.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$stop 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                }

                $join = " JOIN reference_types rt ON t.reference_type_id = rt.id JOIN mossbets_profile.profile p ON t.profile_id = p.id ";
                $searchQuery .= " AND rt.identifier_name = 'DEPOSIT' ";

                $sql = "SELECT DATE(t.created_at) as date,
                    SUM(t.amount) as total_deposits,
                    COUNT(DISTINCT t.profile_id) as unique_depositors,
                    AVG(t.amount) as average_deposits,
                    COUNT(t.id) as number_of_transactions
                    FROM transaction t $join $searchQuery
                    GROUP BY DATE(t.created_at)
                    ORDER BY DATE(t.created_at) DESC";

                $results = $this->rawSelect('dbTrxn', $sql, $searchParams);

                if (!$results) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is not successful',
                        ['code' => 404,
                            'message' => 'Request returned no deposits!'], true);
                }
            } else if ($report_type == '3' || $report_type == 'withdrawals') {
                // Withdrawals - Use transaction table with reference_types join
                // Formula: (total withdrawals + charges) - (total withdrawal reversals + charge reversals)
                $searchQuery = " WHERE 1";
                $searchParams = [];
                $sorting = "";
                if ($start && $stop) {
                    $searchQuery .= " AND t.created_at BETWEEN :start AND :stop ";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                } elseif ($start && !$stop) {
                    $searchQuery .= " AND t.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$start 23:59:59";
                } elseif (!$start && $stop) {
                    $searchQuery .= " AND t.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$stop 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                }

                $join = " JOIN reference_types rt ON t.reference_type_id = rt.id JOIN mossbets_profile.profile p ON t.profile_id = p.id ";
                $searchQuery .= " AND rt.identifier_name IN ('WITHDRAW', 'WITHDRAWAL_MPESA_CHARGES', 'REVERSAL', 'WITHDRAWAL_MPESA_CHARGES_REVERSAL') ";

                // Enhanced withdrawal query with detailed breakdown using identifier_name
                $sql = "SELECT DATE(t.created_at) as date,
                    -- Withdrawal amounts and counts
                    SUM(CASE WHEN rt.identifier_name = 'WITHDRAW' THEN t.amount ELSE 0 END) as total_amount_withdrawn,
                    SUM(CASE WHEN rt.identifier_name = 'WITHDRAWAL_MPESA_CHARGES' THEN t.amount ELSE 0 END) as total_charges,
                    SUM(CASE WHEN rt.identifier_name = 'REVERSAL' THEN t.amount ELSE 0 END) as total_reversals,
                    SUM(CASE WHEN rt.identifier_name = 'WITHDRAWAL_MPESA_CHARGES_REVERSAL' THEN t.amount ELSE 0 END) as total_reversal_charges,

                    -- Net withdrawal calculation: (withdrawals + charges) - (reversals + charge reversals)
                    (SUM(CASE WHEN rt.identifier_name = 'WITHDRAW' THEN t.amount ELSE 0 END) +
                     SUM(CASE WHEN rt.identifier_name = 'WITHDRAWAL_MPESA_CHARGES' THEN t.amount ELSE 0 END)) -
                    (SUM(CASE WHEN rt.identifier_name = 'REVERSAL' THEN t.amount ELSE 0 END) +
                     SUM(CASE WHEN rt.identifier_name = 'WITHDRAWAL_MPESA_CHARGES_REVERSAL' THEN t.amount ELSE 0 END)) as total_net_withdrawals,

                    -- Withdrawal counts and unique withdrawers
                    SUM(CASE WHEN rt.identifier_name = 'WITHDRAW' THEN 1 ELSE 0 END) as total_transactions ,
                    COUNT(DISTINCT CASE WHEN rt.identifier_name = 'WITHDRAW' THEN t.profile_id ELSE NULL END) as unique_withdrawers

                    FROM transaction t $join $searchQuery
                    GROUP BY DATE(t.created_at)
                    ORDER BY DATE(t.created_at) DESC";

                $results = $this->rawSelect('dbTrxnRead', $sql, $searchParams);

                if (!$results) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is not successful',
                        ['code' => 404,
                            'message' => 'Request returned no withdrawals!'], true);
                }
            } else if ($report_type == '4' || $report_type == 'sportsbook') {
                // Sports bets
                $searchQuery = " WHERE 1";
                $searchParams = [];

                if ($start && $stop) {
                    $searchQuery .= " AND sb.created_at BETWEEN :start AND :stop ";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                } elseif ($start && !$stop) {
                    $searchQuery .= " AND sb.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$start 23:59:59";
                } elseif (!$start && $stop) {
                    $searchQuery .= " AND sb.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$stop 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                }

                $join .= " JOIN mossbets_profile.profile p ON sb.profile_id=p.id ";
                $searchQuery .= " AND sb.bet_type = 0 ";
                // return the date, number of bets, total stake amount,average stake amount, total wins(status = 1),
                // total losses(status = 3) total pending(status = 0),total bettors, unique bettors.
                // all cash bet meaning where (bet_type = 0)
                $sql = "SELECT DATE(sb.created_at) as date,"
                    . "SUM(sb.bet_amount) as total_stake, "
                    . "AVG(sb.bet_amount) as average_stake, "
                    . "COUNT(sb.bet_id) as total_bets, "
                    . "SUM(CASE WHEN sb.status = 1 THEN 1 ELSE 0 END) as bets_won, "
                    . "SUM(CASE WHEN sb.status = 3 THEN 1 ELSE 0 END) as bets_lost, "
                    . "SUM(CASE WHEN sb.status = 0 THEN 1 ELSE 0 END) as bets_pending, "
                    . "COUNT(DISTINCT sb.profile_id) as total_players "
                    . "FROM sports_bet sb "
                    . "$join $searchQuery "
                    . "GROUP BY DATE(sb.created_at) "
                    . "ORDER BY DATE(sb.created_at) DESC";

                $results = $this->rawSelect('dbBetsRead', $sql, $searchParams);

                if (!$results) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is not successful',
                        ['code' => 404,
                            'message' => 'Request returned no sports bets!'], true);
                }
            } else if ($report_type == '5' || $report_type == 'freebets') {
                // Freebets
                $searchQuery = " WHERE 1";
                $searchParams = [];
                if ($start && $stop) {
                    $searchQuery .= " AND sb.created_at BETWEEN :start AND :stop ";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                } elseif ($start && !$stop) {
                    $searchQuery .= " AND sb.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$start 23:59:59";
                } elseif (!$start && $stop) {
                    $searchQuery .= " AND sb.created_at BETWEEN :start AND :stop";
                    $searchParams[':start'] = "$stop 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                }

                $join .= " JOIN mossbets_profile.profile p ON sb.profile_id=p.id ";
                $searchQuery .= " AND sb.bet_type = 2 ";
                // return the date, number of freebets, total stake amount,average stake amount, total wins(status = 1),
                // total losses(status = 3) total pending(status = 0),total bettors, unique bettors.
                // all freebets meaning where (bet_type = 2)
                $sql = "SELECT DATE(sb.created_at) as date, "
                    . "SUM(sb.bet_amount) as total_stake, "
                    . "AVG(sb.bet_amount) as average_stake, "
                    . "COUNT(sb.bet_id) as total_freebets, "
                    . "SUM(CASE WHEN sb.status = 1 THEN 1 ELSE 0 END) as bets_won, "
                    . "SUM(CASE WHEN sb.status = 3 THEN 1 ELSE 0 END) as bets_lost, "
                    . "SUM(CASE WHEN sb.status = 0 THEN 1 ELSE 0 END) as bets_pending, "
                    . "COUNT(DISTINCT sb.profile_id) as total_players "
                    . "FROM sports_bet sb $join $searchQuery "
                    . "GROUP BY DATE(sb.created_at) "
                    . "ORDER BY DATE(sb.created_at) DESC";

                $results = $this->rawSelect('dbBetsRead', $sql, $searchParams);

                if (!$results) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is not successful',
                        ['code' => 404,
                            'message' => 'Request returned no freebets!'], true);
                }
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' //. $results[0]['trx_count']
                        . ' Special Report successfully!',
//                    'data' => ['record_count' => $results[0]['trx_count']?? 0,
                    'data' => ['record_count' => 0,
                        'result' => $results]], false, true);


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


}
