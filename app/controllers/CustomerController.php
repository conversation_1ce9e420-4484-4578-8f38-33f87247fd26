<?php

class CustomerController extends \ControllerBase
{


    /**
     * GetProfiles
     * @return type
     */
    function GetProfiles()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Profile";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request GetProfiles :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;

        $acc_number = $data['acc_number'] ?? false;
        $name = $data['customer_name'] ?? false;
        $msisdn = $data['mobile_number'] ?? false;
        $network = $data['network'] ?? false;

        $min_bonus = $data['min_bonus'] ?? false;
        $max_bonus = $data['max_bonus'] ?? false;
        $min_balance = $data['min_balance'] ?? false;
        $max_balance = $data['max_balance'] ?? false;

        $status = $data['status'] ?? false;

        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "p." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'p.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];
            if ($msisdn) {
                $searchParams[':msisdn'] = $this->formatMobileNumber($msisdn);
                $searchQuery .= " AND p.msisdn = :msisdn";
            }

            if ($acc_number) {
                $searchParams[':acc_number'] = $acc_number;
                $searchQuery .= " AND p.acc_number = :acc_number";
            }

            if ($name) {
                $searchParams[':name'] = $name;
                $searchQuery .= " AND p.name REGEXP :name";
            }

            if (is_numeric($min_bonus) && is_numeric($max_bonus)) {
                $searchParams[':min_bonus'] = $min_bonus;
                $searchParams[':max_bonus'] = $max_bonus;
                $searchQuery .= " AND pb.bonus BETWEEN :min_bonus AND :max_bonus";
            }

            if (is_numeric($min_balance) && is_numeric($max_balance)) {
                $searchParams[':min_balance'] = $min_balance;
                $searchParams[':max_balance'] = $max_balance;
                $searchQuery .= " AND pb.balance BETWEEN :min_balance AND :max_balance";
            }

            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND p.status = :status";
            }

            if ($network) {
                $searchParams[':network'] = $network;
                $searchQuery .= " AND p.network = :network";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND p.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND p.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND p.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $orderBy = $sort ? "ORDER BY $sort $order" : "";
                $exportLimit = 50000;
                $sorting = "$orderBy LIMIT $exportLimit";
            }

            $sql = 'select (select count(p.id) '
                . 'from profile p '
                . 'left join profile_login pa on p.id=pa.profile_id '
                . 'left join profile_attribution pl ON p.id=pl.profile_id '
                . 'left join profile_balance pb on p.id=pb.profile_id '
                . 'left join profile_blacklist pbk on p.id=pbk.profile_id '
                . $searchQuery . ') as trx_count'
                . ',p.id,p.msisdn,p.name,p.acc_number,p.ip_address,p.network,p.status profile_status,pa.status login_status'
                . ',pb.currency, ifnull(pb.balance,0) balance, ifnull(pb.bonus,0) as bonus, ifnull(pb.status,1) balance_status'
                . ',pl.origin,pl.country,pl.can_market,pa.exclusion_period_date'
                . ',pl.deposit_count,pl.withdrawal_count,pl.total_referrals'
                . ',pl.total_freebets,pl.total_deposits'
                . ',(pl.total_withdrawals - pl.total_failed_withdrawals) AS total_withdrawals,pl.total_bet_stake,pl.total_winnings'
                . ',pl.total_bonus,pl.last_winning_date,pl.first_deposit_date'
                . ',pl.last_deposit_date,pa.success_attempts,pa.failed_attempts'
                . ',pa.successful_resets,pa.failed_verify_attempts'
                . ',pa.frequency_of_use,pa.verify_blocked_timeline,pa.last_login_date'
                . ',pa.last_reset_date,pa.last_failed_reset_date,pa.last_verified_date'
                . ',ifnull(pbk.status,1) blacklist_status,pbk.reason blacklist_reason,pbk.blacklisted_on,pbk.unblacklisted_on'
                . ',pa.blocked_timeline,pa.last_use_date,p.created_at '
                . 'from profile p '
                . 'left join profile_login pa on p.id=pa.profile_id '
                . 'left join profile_attribution pl ON p.id=pl.profile_id '
                . 'left join profile_balance pb on p.id=pb.profile_id '
                . 'left join profile_blacklist pbk on p.id=pbk.profile_id '
                . $searchQuery . ' ' . $sorting;

            $results = $this->rawSelect('dbProfile', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Customer Profiles!'], true);
            }

            // Check Total Bets
            // if $msisdn then query bets table using profile_id from $results
            if ($msisdn) {

                // Check if a profile is found
                if (!empty($results)) {
                    // Extract the profile_id
                    $profile_id = $results[0]['id'];

                    // Initialize search parameters and query
                    $searchParams = [':profile_id' => $profile_id];
                    $searchQuery = " AND sports_bet.profile_id = :profile_id";

                    // Create the final query
                    $query = "SELECT COUNT(sports_bet.profile_id) as trx_count FROM sports_bet WHERE sports_bet.profile_id = :profile_id";

                    // Fetch results
                    $bets = $this->rawSelect('dbBetsRead', $query, $searchParams);

                    //in results add a field 'total_bets' and equate to trx_count
                    foreach ($results as $key => $result) {
                        $results[$key]['total_bets'] = $bets[0]['trx_count'] ?? null;
                    }

                } else {
                    // Handle the case where no profile is found for the given MSISDN
                    throw new Exception("No profile found for MSISDN:");
                }
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Customer Profiles successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetSelfExclusionCustomers
     */
    function GetSelfExclusionCustomers()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Profile Self Exclusion";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request GetSelfExclusionCustomers :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;
        $timestamp = $data['timestamp'] ?? false;

        $msisdn = $data['mobile_number'] ?? false;
        $status = $data['status'] ?? false;

        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "p." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'p.id';
            $order = 'DESC';
        }

        try {
            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($msisdn) {
                $searchParams[':msisdn'] = $this->formatMobileNumber($msisdn);
                $searchQuery .= " AND p.msisdn = :msisdn";
            }

            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND p.status = :status";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND p.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND p.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND p.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);

            $sql_query = "SELECT *, COUNT(*) OVER() AS trx_count
            FROM profile_self_exlusion p
            $searchQuery
            $sorting";

            $results = $this->rawSelect('dbProfile', $sql_query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Self Exclusion Customer Profiles!'], true);
            }


            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Self Exclusion Customer Profiles successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * AddSelfExclusionCustomer
     * @return type
     */

    function AddSelfExclusionCustomer()
    {
        $start = $this->getMicrotime();

        $permissionName = "Add Self Exclusion Customer";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request AddSelfExclusionCustomer :" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $msisdn = $data['msisdn'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$msisdn) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $msisdn = $this->formatMobileNumber($msisdn);
            // check if msisdn exists
            $profile = $this->rawSelectOneRecord('dbProfile',
                "SELECT * FROM profile_self_exlusion WHERE msisdn=:msisdn", [':msisdn' => $msisdn]);

            if ($profile) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Customer already in Self Exclusion list."], true);
            }

            // insert into profile_self_exlusion id, msisdn, status, created_at, updated_at
            $result = $this->rawInsertBulk('dbProfile',
                "profile_self_exlusion",
                ['msisdn' => $msisdn,
                    'status' => 1,
                    'created_at' => $this->now(),
                ]);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Failed to add customer to Self Exclusion list."], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Customer added to Self Exclusion list successfully!"], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdateSelfExclusionCustomer
     */
    function UpdateSelfExclusionCustomer($id)
    {
        $start = $this->getMicrotime();

        $permissionName = "Update Self Exclusion Customer";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request UpdateSelfExclusionCustomer :" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $msisdn = $data['msisdn'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$id) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            // check if msisdn exists
            $profile = $this->rawSelectOneRecord('dbProfile',
                "SELECT * FROM profile_self_exlusion WHERE id=:id", [':id' => $id]);

            if (!$profile) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Customer does not exist in Self Exclusion list."], true);
            }

            $fields = [];
            $params = [':id' => $id];

            if ($msisdn) {
                $msisdn = $this->formatMobileNumber($msisdn);
                $fields[] = "msisdn = :msisdn";
                $params[":msisdn"] = $msisdn;
            }

            if (is_numeric($status)) {
                $fields[] = "status = :status";
                $params[":status"] = $status;
            }

            $fields[] = "updated_at = NOW()";

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE profile_self_exlusion SET " . implode(', ', $fields) . " WHERE id = :id Limit 1";

            if (!$this->rawUpdateWithParams('dbProfile', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful', [
                        'code' => 400,
                        'message' => "Sorry, Update failed for Self Exclusion ID: $id"
                    ], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Customer status updated successfully."]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * BlacklistUser
     * @return type
     */
    function BlacklistCustomer($profileId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Blacklist Customer";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request BlacklistCustomer :" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $status = $data['status'] ?? false;
        $reason = $data['reason'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$profileId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!in_array($status, [1, 3, 5])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 421,
                    'message' => "Invalid Status!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $check = $this->rawSelectOneRecord('dbProfile',
                "SELECT * FROM profile WHERE id=:id", [':id' => $profileId]);

            if (!$check) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "The System user does not exists!"], true);
            }


            $blacklist = $this->rawSelectOneRecord('dbProfile',
                "SELECT * FROM profile_blacklist WHERE profile_id=:profile_id", [':profile_id' => $profileId]);

            if (!$blacklist) {
                if ($status == 3) {
//                    $this->infologger->info(__LINE__ . ":" . __CLASS__
//                        . " | blacklist :" . json_encode($blacklist));

                    $this->rawInsertBulk('dbProfile',
                        "profile_blacklist",
                        ['profile_id' => $profileId,
                            'reason' => $reason,
                            'status' => 3,
                            'blacklisted_on' => $this->now(),
                            'created_at' => $this->now()]);

                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => "Blacklisted User Successfully!"], false);
                }

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "User is not blacklisted!"], true);
            }

            $updateParams = [
                ':profile_id' => $profileId,
            ];
            $updateUser = "UPDATE profile_blacklist SET updated_at=NOW()";

            if (is_numeric($status)) {
                $updateUser .= ",status=:status";

                $updateParams[':status'] = $status;

                if ($status == 3) {
                    $updateUser .= ",blacklisted_on=NOW(),unblacklisted_on=null";
                }

                if ($status == 1) {
                    $updateUser .= ",blacklisted_on=null,unblacklisted_on=NOW()";
                }
            }

            $updateUser .= " WHERE profile_id=:profile_id LIMIT 1";

            if (!$this->rawUpdateWithParams("dbProfile", $updateUser, $updateParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Blacklisting user failed!"], true);
            }

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => (($status == 1) ?
                            'Unblacklisted' :
                            'Blacklisted') . " User Successfully!"], false);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * EnableDisableCustomerWithdrawals
     * @return type
     */
    function EnableDisableCustomerWithdrawals($profileId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Enable/Disable Customer Withdrawals";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$profileId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }
        $status = (int)$status;
        if (!in_array($status, [0, 1, 3,])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 421,
                    'message' => "Invalid Status!"], true);
        }
        try {
            // Check if request is valid
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }
            // Check if request is valid
            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }
            // Authenticate user
            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }
            // Check if user has permission
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }
            // Check if a customer exists
            $check = $this->rawSelectOneRecord('dbProfile',
                "SELECT * FROM profile_balance WHERE profile_id=:profile_id", [':profile_id' => $profileId]);

            if (!$check) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "The Customer does not exists!"], true);
            }

            // Check if the status is already set
            if ($check['status'] == $status) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Customer withdrawal status is already set to $status!"], true);
            }
            // Update status
            if (!$this->rawUpdateWithParams('dbProfile',

                "UPDATE profile_balance SET status=:status, updated_at=NOW() WHERE profile_id=:profile_id LIMIT 1",
                [':profile_id' => $profileId, ':status' => $status])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Failed to update customer withdrawal status. Try again later!"], true);
            }

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Customer withdrawal status updated successfully."]);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * ResendOTPCustomer
     * @return type
     */
    function ResendOTPCustomer($profileId)
    {
        $start = $this->getMicrotime();
        $permissionName = "Reset/Resend Customer OTP";
        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$profileId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!in_array($authResponse['data']['role_id'], [1, 2, 3])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require privileged access!"], true);
            }

            $res = $this->rawSelectOneRecord('dbProfile', 'select p.id,p.msisdn,p.status,exclusion_period_date from profile p join profile_login pl on p.id=pl.profile_id where p.id=:profile_id', [':profile_id' => $profileId]);
            if (!$res) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Invalid Account."], true);
            }

            if (in_array($res['status'], [3])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Account is in a suspended state. Refer case to the Risk Team."], true);
            }

            if ($res['exclusion_period_date'] != null) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Account is in a Self-Exclusion state. Refer case to the Risk Team."], true);
            }

            $verification_code = rand(100000, 999999);
            if (!$this->rawUpdateWithParams('dbProfile',
                'UPDATE profile_login SET status=:status,verification_code=:verification_code,failed_attempts=0,reset_attempts=0,failed_verify_attempts=0,token_expiry_date=now(),verify_blocked_timeline=null,last_reset_date=now(),blocked_timeline=null WHERE profile_id=:profile_id', [':profile_id' => $profileId, ':status' => 2, ':verification_code' => $this->security->hash(md5($verification_code . "" . $res['id']))])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    [
                        'code' => 400,
                        'message' => "Failed to reset account OTP. Contact Risk team for assistance."
                    ], true
                );
            }

            $sms_out = new Messaging();
            $result = $sms_out->SendSMs(
                ['message' => 'OTP: ' . $verification_code,
                    'user_id' => $authResponse['data']['user_id'],
                    'message_type' => 'RESET',
                    'unique_id' => $this->ReferenceNumber(),
                    'msisdn' => $res['msisdn']]);

            if (!$result['success']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Failed to reset account OTP. Sms out not delivered."], true);
            }

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Reset Account Password was successful. "]);
        } catch (Exception $e) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $e->getTraceAsString()
                . "| Message:" . $e->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EnableDisableCustomerSMS
     *
     */
    function EnableDisableCustomerSMS($profileId)
    {
        $start = $this->getMicrotime();
        $permissionName = "Enable/Disable Customer SMS";
        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $can_market = $data['can_market'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$profileId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }
        $can_market = (int)$can_market;
        if (!in_array($can_market, [0, 1,])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 421,
                    'message' => "Invalid Status!"], true);
        }
        try {
            // Check if request is valid
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }
            // Check if request is valid
            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }
            // Authenticate user
            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }
            // Check if user has permission
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }
            // Check if a customer exists
            $check = $this->rawSelectOneRecord('dbProfile',
                "SELECT can_market FROM profile_attribution WHERE profile_id=:profile_id", [':profile_id' => $profileId]);

            if (!$check) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "The Customer does not exists!"], true);
            }

            // Check if the can_market is already set
            if (isset($check['can_market']) && $check['can_market'] == $can_market) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Customer SMS status is already set to $can_market!"], true);
            }
            // Update can_market
            if (!$this->rawUpdateWithParams('dbProfile',
                "UPDATE profile_attribution SET can_market=:can_market, updated_at=NOW() WHERE profile_id=:profile_id LIMIT 1",
                [':profile_id' => $profileId, ':can_market' => $can_market])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Failed to update customer SMS status. Try again later!"], true);
            }

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Customer SMS status updated successfully."]);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * SendProfileSMS
     * @return type
     */
    function SendProfileSMS()
    {
        $start = $this->getMicrotime();
        $permissionName = 'Send Blast SMS';
        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $shortCode = $data['sender_id'] ?? false;
        $campaign_name = $data['campaign_name'] ?? false;
        $blast_filter = $data['blast_filter'] ?? false;
        $blast_message = $data['blast_message'] ?? false;
        $phone_number = $data['phone_number'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$blast_message || !$campaign_name || !$shortCode) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (strlen($blast_message) < 20) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "The Blast message should have atleast 20 characters!"], true);
        }

        $sms_pages = 1;
        if (strlen($blast_message) > 160) {
            $sms_pages = ceil(strlen($blast_message) / 160);
        }

        if (!$blast_filter) {
            $blast_filter = 'all';
        }

        if (!in_array($shortCode, $this->settings['mnoApps']['DefaultSenders'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid Sender Names!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }


            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $recipients = [];
            $searchParams = [];
            $join = " JOIN profile_attribution pa ON p.id=pa.profile_id ";

            // Build base conditions that apply to all queries
            $baseConditions = "";

            // Check if customers can receive sms
            $baseConditions .= " AND pa.can_market = 1";

            // Self exclusion
            $baseConditions .= " AND p.msisdn NOT IN (SELECT msisdn FROM profile_self_exlusion) ";

            if ($phone_number) {
                $searchQuery = " WHERE p.msisdn = :phone_number " . $baseConditions;
                $searchParams[':phone_number'] = $phone_number;
                $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";

                $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                if (!$recipients) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "No recipients found."], true);
                }
            }

            if (!$recipients) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 500,
                        'message' => "Customer Contact not found!"], true);
            }

            $fileName = "FINAL_DATA_BLAST_" . rand(1000, 99999) . ".csv";
            $filePath = $this->settings['uploadDir']['Sms'] . "" . $fileName;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $csv_row = [];
            $csv_row[] = explode(',', $this->settings['uploadDir']['SmsHeaders']);

            $recipients[] = ["254703968228", 'Allan O', '', '',];
            header('Content-Type: text/csv; charset=utf-8');

            $file = fopen($filePath, 'w');
            foreach ($csv_row as $line) {
                fputcsv($file, $line);
                foreach ($recipients as $value) {
                    fputcsv($file, $value);
                }
            }

            $correlator = $this->rawInsertBulk('dbUser',
                'bulk_sms_out',
                ['created_at' => $this->now(),
                    'bulk_filter' => $blast_filter,
                    'message' => $blast_message,
                    'recipients' => count($recipients) - 1,
                    'sms_pages' => $sms_pages,
                    'user_id' => $authResponse['data']['user_id'],
                    'status' => 0]);

            $fileType = mime_content_type($filePath);

            $msgUtils = new Messaging();
            $token = $msgUtils->GetLidenAccessToken();
            if (!$token) {
                $key = $this->settings['appName'] . '$GetLidenAccessToken_AccessKey';
                RedisUtils::redisRawDelete($key);

                $this->rawUpdateWithParams('dbUser',
                    "UPDATE bulk_sms_out SET status=:status,completed_on=NOW() WHERE id=:id LIMIT 1",
                    [':id' => $correlator,
                        ':status' => 100]);

                unlink($filePath);

//                $this->infologger->info(__LINE__ . ":" . __CLASS__
//                    . '| CorrelatorId:' . $correlator
//                    . "| Authentication process failed!");
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Blast Sending Failed. Authentication process failed"], true);
            }

            $isScheduled = 0;
            $ScheduleDate = '';
            $ScheduleTime = '';
            $url = $this->settings['mnoApps']['Urls']['BulkBroadcastUrl'];

            $header = ['Content-Type: multipart/form-data',
                'X-Authorization-Key:' . $token,
                'X-Requested-With:XMLHttpRequest'];

            $postData = new stdClass();
            $postData->shortCode = "$shortCode";
            $postData->message = "$blast_message";
            $postData->isScheduled = "$isScheduled";
            $postData->scheduleDate = "$ScheduleDate";
            $postData->scheduleTime = "$ScheduleTime";
            $postData->approval = "";
            $postData->uniqueId = $correlator;
            $postData->callbackURL = '';
            $postData->file = new CurlFile($filePath, $fileType, $fileName);

            $httpRequest = curl_init();
            curl_setopt($httpRequest, CURLOPT_URL, $url);
            curl_setopt($httpRequest, CURLOPT_POST, true);
            curl_setopt($httpRequest, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $header);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, true);
            $results = curl_exec($httpRequest);
            $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
            curl_close($httpRequest);

            $serverResponse = json_decode($results);
            $code = $serverResponse->code ?? 'Error';
            $messageBack = $serverResponse->statusDescription
                . ". Reason:" . ($serverResponse->data->message ?? "Request Failed");

            $updateParams = [
                ':id' => $correlator,
                ':status' => $status];
            $updateSql = "UPDATE bulk_sms_out SET status=:status,completed_on=NOW()";
            if ($status == 200 && $code == 'Success') {
                $updateSql .= ",campaign_id=:campaign_id";
                $updateParams[':campaign_id'] = $serverResponse->data->data->sms_data->campaign_id;
            }

            $updateSql .= " WHERE id=:id LIMIT 1";

            $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);
            unlink($filePath);

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start) . " Sec",
                200,
                $serverResponse->statusDescription,
                ['code' => ($status != 200) ? 400 : $status,
                    'message' => "Blast Sending "
                        . (($status == 200) ? 'Complete' : 'Failed') . "!!!. $messageBack"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * Adding Freebets
     */
    public function AwardFreebetsToCustomer()
    {
        $start = $this->getMicrotime();
        $permissionName = 'Award Freebets To Customer';
        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $msisdn = $data['msisdn'] ?? false;
        $amount = $data['amount'] ?? false;
        $expiry_period = $data['expiry_period'] ?? false;
        $type = $data['type'] ?? false;
        $bonus_id = $data['bonus_id'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }
        if (!$msisdn || !$amount || !$expiry_period || !$type || !$bonus_id) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }
        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }
            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $msisdn = $this->formatMobileNumber($msisdn);
            // check if msisdn exists
//            select p.id,'12','30.00','10000','1',
//     * '{"name":"Re-Activation FREEBET","type":"SPORTS","ip":98,"bonus_percentage":1,"expiry_period_in_hours":48}',
//     * DATE_ADD(NOW(), INTERVAL 48 HOUR),now() from profile p INNER JOIN profile_login as pl ON p.id=pl.profile_id
//                * INNER JOIN profile_attribution pa ON p.id = pa.profile_id where p.msisdn=254704050143;
            // insert
            $query = "SELECT p.id,pl.profile_id,pa.profile_id "
                . "FROM profile p "
                . "INNER JOIN profile_login as pl ON p.id=pl.profile_id "
                . "INNER JOIN profile_attribution pa ON p.id = pa.profile_id where p.msisdn=:msisdn";

            $profile = $this->rawSelectOneRecord('dbProfile', $query, [':msisdn' => $msisdn]);

            if (!$profile) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Customer does not exist."], true);
            }

            // check bonus with bonus_id
            $bonus = $this->rawSelectOneRecord('dbBonus',
                "SELECT * FROM bonus WHERE id=:id", [':id' => $bonus_id]);

            if (!$bonus) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Bonus does not exist."], true);
            }

            $profile_id = $profile['id'];
            $expiry_date = date('Y-m-d H:i:s', strtotime("+$expiry_period hours"));
            $extra_data = json_encode(['name' => $bonus['name'],
                'type' => $type,
                'ip' => $profile['ip'],
                'bonus_percentage' => $bonus['bonus_percentage'],
                'expiry_period_in_hours' => $expiry_period]);
            $created_at = $this->now();

            // insert into profile_bonus
            $sql = "INSERT INTO profile_bonus(profile_id,bonus_id,bonus_amount,max_win,status,extra_data,expiry_date,created_at) "
                . "VALUES(:profile_id,:bonus_id,:bonus_amount,:max_win,:status,:extra_data,:expiry_date,:created_at)";

            $result = $this->rawInsertWithParams('dbProfile', $sql, [':profile_id' => $profile_id,
                ':bonus_id' => $bonus_id,
                ':bonus_amount' => $amount,
                ':max_win' => $bonus['max_win'],
                ':status' => 1,
                ':extra_data' => $extra_data,
                ':expiry_date' => $expiry_date,
                ':created_at' => $created_at]);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Failed to add freebet to customer."], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Freebet added to customer successfully!"], false);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * SendBlastSMS
     * @return type
     */
    function SendBlastSMS()
    {
        $start = $this->getMicrotime();
        $permissionName = 'Send Blast SMS';
        $data = (array)$this->request->getJsonRawBody();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request SendBlastSMS:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $shortCode = $data['sender_id'] ?? false;
        $campaign_name = $data['campaign_name'] ?? false;
        $blast_filter = $data['blast_filter'] ?? false;
        $blast_message = $data['blast_message'] ?? false;
        $phone_number = $data['account_number'] ?? false;

        $count = $data['count'] ?? false;
        $depositor_type = $data['depositor_type'] ?? false;
        $deposit_count_min = $data['deposit_count_min'] ?? false;
        $deposit_count_max = $data['deposit_count_max'] ?? false;
        $deposit_amount_min = $data['deposit_amount_min'] ?? false;
        $deposit_amount_max = $data['deposit_amount_max'] ?? false;
        $profile_status = $data['profile_status'] ?? false;
        $balance_status = $data['balance_status'] ?? false;
        $balance_min = $data['balance_min'] ?? false;
        $balance_max = $data['balance_max'] ?? false;
        $bonus_status = $data['bonus_status'] ?? false;
        $bonus_min = $data['bonus_min'] ?? false;
        $bonus_max = $data['bonus_max'] ?? false;
        $isScheduled = $data['isScheduled'] ?? false;
        $ScheduleDate = $data['scheduleDate'] ?? false;
        $scheduleTime = $data['scheduleTime'] ?? false;
        $start_date = $data['start_date'] ?? false;
        $end_date = $data['end_date'] ?? false;
        $origin = $data['origin'] ?? false;

        if ($bonus_status === '') $bonus_status = false;
        if ($depositor_type === '') $depositor_type = false;
        if ($balance_status === '') $balance_status = false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$blast_message || !$campaign_name || !$shortCode) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (strlen($blast_message) < 20) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "The Blast message should have atleast 20 characters!"], true);
        }

        $sms_pages = 1;
        if (strlen($blast_message) > 160) {
            $sms_pages = ceil(strlen($blast_message) / 160);
        }

        if (!$blast_filter) {
            $blast_filter = 'all';
        }

        if (!in_array($shortCode, $this->settings['mnoApps']['DefaultSenders'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid Sender Names!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $recipients = [];
            $searchParams = [];
            $join = " INNER JOIN profile_attribution pa ON p.id=pa.profile_id ";

            $baseConditions = "";
            $baseConditions .= " AND pa.can_market = 1";
            $baseConditions .= " AND p.msisdn NOT IN (SELECT msisdn FROM profile_self_exlusion) ";

            if (($end_date != null) && ($start_date != null)) {
                $baseConditions .= " AND p.created_at BETWEEN :start_date AND :end_date ";
                $searchParams[':start_date'] = "$start_date 00:00:00";
                $searchParams[':end_date'] = "$end_date 23:59:59";
            } elseif (($end_date != null) && ($start_date == null)) {
                $baseConditions .= " AND p.created_at <= :end_date";
                $searchParams[':end_date'] = "$end_date 23:59:59";
            } elseif ($end_date == null && $start_date != null) {
                $baseConditions .= " AND p.created_at >= :start_date";
                $searchParams[':start_date'] = "$start_date 00:00:00";
            }

            if ($origin) {
                $join .= " INNER JOIN profile_campaign_attribution pca ON p.id=pca.profile_id ";
                $baseConditions .= " AND pca.utm_source = :origin ";
                $searchParams[':origin'] = $origin;
            }

            if ($phone_number) {
                $searchQuery = " WHERE p.msisdn = :phone_number ";
                $searchParams[':phone_number'] = $phone_number;
                $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";

                $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                if (!$recipients) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "No recipients found."], true);
                }
            } else {

                if ($profile_status == 2) {
                    $join .= " INNER JOIN profile_login pl ON p.id=pl.profile_id ";
                    $searchQuery = " WHERE pl.status = 2 " . $baseConditions;
                    $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";
                    $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                    if (!$recipients) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => "No recipients found."], true);
                    }
                }

                if ($profile_status == 1) {
                    $searchQuery = " WHERE 1 " . $baseConditions;

                    if ($balance_status !== false && $balance_status !== null) {
                        if ($balance_status == 1) {
                            $join .= " INNER JOIN profile_balance pb ON p.id=pb.profile_id ";
                            if ($balance_min && $balance_max) {
                                $searchQuery .= " AND pb.balance BETWEEN :balance_min AND :balance_max";
                                $searchParams[':balance_min'] = $balance_min;
                                $searchParams[':balance_max'] = $balance_max;
                            } else {
                                $searchQuery .= " AND pb.balance > 0";
                            }
                            $searchQuery .= " AND pb.status=1 ";
                        }

                        if ($balance_status == 0) {
                            $searchQuery .= " AND NOT EXISTS (SELECT 1 FROM profile_balance pb2 WHERE pb2.profile_id = p.id AND pb2.balance > 0 AND pb2.status = 1) ";
                        }
                    }

                    if ($bonus_status !== false && $bonus_status !== null) {
                        if ($bonus_status == 1) {
                            $join .= " INNER JOIN profile_bonus pbo ON p.id=pbo.profile_id ";
                            $searchQuery .= " AND pbo.profile_id IS NOT NULL AND pbo.status=1";

                            if ($bonus_min && $bonus_max) {
                                $searchQuery .= " GROUP BY p.id HAVING SUM(pbo.bonus_amount) BETWEEN :bonus_min AND :bonus_max";
                                $searchParams[':bonus_min'] = $bonus_min;
                                $searchParams[':bonus_max'] = $bonus_max;
                            } else {
                                $searchQuery .= " AND pbo.bonus_amount > 0";
                            }
                        }

                        if ($bonus_status == 0) {
                            $searchQuery .= " AND NOT EXISTS (SELECT 1 FROM profile_bonus pbo2 WHERE pbo2.profile_id = p.id AND pbo2.bonus_amount > 0 AND pbo2.status = 1) ";
                        }
                    }

                    if ($depositor_type !== false && $depositor_type !== null) {
                        if ($depositor_type == 0) {
                            $searchQuery .= " AND (pa.deposit_count = 0 OR pa.deposit_count IS NULL) ";
                        } else if ($depositor_type == 1) {
                            $searchQuery .= " AND pa.deposit_count = 1 ";
                        } else if ($depositor_type == 2) {
                            $searchQuery .= " AND pa.deposit_count > 1 ";
                            if ($deposit_count_min && $deposit_count_max) {
                                $searchQuery .= " AND pa.deposit_count BETWEEN :deposit_count_min AND :deposit_count_max";
                                $searchParams[':deposit_count_min'] = $deposit_count_min;
                                $searchParams[':deposit_count_max'] = $deposit_count_max;
                            }
                            if ($deposit_amount_min && $deposit_amount_max) {
                                $searchQuery .= " AND pa.total_deposits BETWEEN :deposit_amount_min AND :deposit_amount_max";
                                $searchParams[':deposit_amount_min'] = $deposit_amount_min;
                                $searchParams[':deposit_amount_max'] = $deposit_amount_max;
                            }
                        } else if ($depositor_type == 3) {
                            $searchQuery .= " AND pa.deposit_count >= 1 ";
                        }
                    }

                    $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";
                    $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                    if (!$recipients) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => "No recipients found."], true);
                    }
                }

                if ($profile_status == 6) {
                    $join .= " INNER JOIN profile_login pl ON p.id=pl.profile_id ";
                    $searchQuery = " WHERE pl.status = 6 " . $baseConditions;

                    if ($balance_status !== false && $balance_status !== null) {
                        if ($balance_status == 1) {
                            $join .= " INNER JOIN profile_balance pb ON p.id=pb.profile_id ";
                            if ($balance_min && $balance_max) {
                                $searchQuery .= " AND pb.balance BETWEEN :balance_min AND :balance_max";
                                $searchParams[':balance_min'] = $balance_min;
                                $searchParams[':balance_max'] = $balance_max;
                            } else {
                                $searchQuery .= " AND pb.balance > 0";
                            }
                            $searchQuery .= " AND pb.status=1 ";
                        }

                        if ($balance_status == 0) {
                            $searchQuery .= " AND NOT EXISTS (SELECT 1 FROM profile_balance pb2 WHERE pb2.profile_id = p.id AND pb2.balance > 0 AND pb2.status = 1) ";
                        }
                    }

                    if ($bonus_status !== false && $bonus_status !== null) {
                        if ($bonus_status == 1) {
                            $join .= " INNER JOIN profile_bonus pbo ON p.id=pbo.profile_id ";
                            $searchQuery .= " AND pbo.profile_id IS NOT NULL AND pbo.status=1";

                            if ($bonus_min && $bonus_max) {
                                $searchQuery .= " GROUP BY p.id HAVING SUM(pbo.bonus_amount) BETWEEN :bonus_min AND :bonus_max";
                                $searchParams[':bonus_min'] = $bonus_min;
                                $searchParams[':bonus_max'] = $bonus_max;
                            } else {
                                $searchQuery .= " AND pbo.bonus_amount > 0";
                            }
                        }

                        if ($bonus_status == 0) {
                            $searchQuery .= " AND NOT EXISTS (SELECT 1 FROM profile_bonus pbo2 WHERE pbo2.profile_id = p.id AND pbo2.bonus_amount > 0 AND pbo2.status = 1) ";
                        }
                    }

                    if ($depositor_type !== false && $depositor_type !== null) {
                        if ($depositor_type == 0) {
                            $searchQuery .= " AND (pa.deposit_count = 0 OR pa.deposit_count IS NULL) ";
                        } else if ($depositor_type == 1) {
                            $searchQuery .= " AND pa.deposit_count = 1 ";
                        } else if ($depositor_type == 2) {
                            $searchQuery .= " AND pa.deposit_count > 1 ";
                            if ($deposit_count_min && $deposit_count_max) {
                                $searchQuery .= " AND pa.deposit_count BETWEEN :deposit_count_min AND :deposit_count_max";
                                $searchParams[':deposit_count_min'] = $deposit_count_min;
                                $searchParams[':deposit_count_max'] = $deposit_count_max;
                            }
                            if ($deposit_amount_min && $deposit_amount_max) {
                                $searchQuery .= " AND pa.total_deposits BETWEEN :deposit_amount_min AND :deposit_amount_max";
                                $searchParams[':deposit_amount_min'] = $deposit_amount_min;
                                $searchParams[':deposit_amount_max'] = $deposit_amount_max;
                            }
                        } else if ($depositor_type == 3) {
                            $searchQuery .= " AND pa.deposit_count >= 1 ";
                        }
                    }

                    $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";
                    $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                    if (!$recipients) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => "No recipients found."], true);
                    }
                }
            }

            if (!$recipients) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 500,
                        'message' => "Query returned an empty Contact list(s)!"], true);
            } else if ($count) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is successful',
                    ['code' => 200,
                        'message' => "Query returned " . count($recipients) . " Contact(s)!",
                        'data' => ['record_count' => count($recipients),
                            'result' => count($recipients)
                        ]], false, true);
            }

            $fileName = "FINAL_DATA_BLAST_" . rand(1000, 99999) . ".csv";
            $filePath = $this->settings['uploadDir']['Sms'] . "" . $fileName;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $csv_row = [];
            $csv_row[] = explode(',', $this->settings['uploadDir']['SmsHeaders']);


            $recipients[] = ["254704050143", 'Josephat', '', '',];
            $recipients[] = ["254725547995", 'Daniel', '', '',];
            $recipients[] = ["254707089565", 'Daniel', '', '',];
            $recipients[] = ["254721137826", 'Josephat M', '', '',];
            $recipients[] = ["254703968228", 'Allan O', '', '',];
            $recipients[] = ["254714919776", 'J K', '', '',];
            header('Content-Type: text/csv; charset=utf-8');

            $file = fopen($filePath, 'w');
            foreach ($csv_row as $line) {
                fputcsv($file, $line);
                foreach ($recipients as $value) {
                    fputcsv($file, $value);
                }
            }

            $correlator = $this->rawInsertBulk('dbUser',
                'bulk_sms_out',
                ['created_at' => $this->now(),
                    'bulk_filter' => $blast_filter,
                    'message' => $blast_message,
                    'recipients' => count($recipients) - 1,
                    'sms_pages' => $sms_pages,
                    'user_id' => $authResponse['data']['user_id'],
                    'is_scheduled' => $isScheduled,
                    'schedule_date' => $isScheduled > 0 ? $ScheduleDate : null,
                    'schedule_time' => $isScheduled > 0 ? $scheduleTime : null,
                    'status' => $isScheduled > 0 ? 1 : 0]
            );

            $fileType = mime_content_type($filePath);

            $msgUtils = new Messaging();
            $token = $msgUtils->GetLidenAccessToken();
            if (!$token) {
                $key = $this->settings['appName'] . '$GetLidenAccessToken_AccessKey';
                RedisUtils::redisRawDelete($key);

                $this->rawUpdateWithParams('dbUser',
                    "UPDATE bulk_sms_out SET status=:status,completed_on=NOW() WHERE id=:id LIMIT 1",
                    [':id' => $correlator,
                        ':status' => 100]);

                unlink($filePath);

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . '| CorrelatorId:' . $correlator
                    . "| Authentication process failed!");

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Blast Sending Failed. Authentication process failed"], true);
            }

            $url = $this->settings['mnoApps']['Urls']['BulkBroadcastUrl'];

            $header = ['Content-Type: multipart/form-data',
                'X-Authorization-Key:' . $token,
                'X-Requested-With:XMLHttpRequest'];

            $postData = new stdClass();
            $postData->shortCode = "$shortCode";
            $postData->message = "$blast_message";
            $postData->isScheduled = (int)$isScheduled;
            $postData->scheduleDate = "$ScheduleDate";
            $postData->scheduleTime = "$scheduleTime";
            $postData->approval = "";
            $postData->uniqueId = $correlator;
            $postData->callbackURL = '';
            $postData->file = new CurlFile($filePath, $fileType, $fileName);

            $httpRequest = curl_init();
            curl_setopt($httpRequest, CURLOPT_URL, $url);
            curl_setopt($httpRequest, CURLOPT_POST, true);
            curl_setopt($httpRequest, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $header);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, true);
            $results = curl_exec($httpRequest);
            $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
            curl_close($httpRequest);

            $serverResponse = json_decode($results);
            $code = $serverResponse->code ?? 'Error';
            $messageBack = $serverResponse->statusDescription
                . ". Reason:" . ($serverResponse->data->message ?? "Request Failed");

            $updateParams = [
                ':id' => $correlator,
                ':status' => $status];

            $updateSql = "UPDATE bulk_sms_out SET status=:status,completed_on=NOW()";

            if ($status == 200 && $code == 'Success') {
                $updateSql .= ",campaign_id=:campaign_id";
                $updateParams[':campaign_id'] = $serverResponse->data->data->sms_data->campaign_id;
            }

            $updateSql .= " WHERE id=:id LIMIT 1";

            $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);
            unlink($filePath);

            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start) . " Sec",
                200,
                $serverResponse->statusDescription,
                ['code' => ($status != 200) ? 400 : $status,
                    'message' => "Blast Sending "
                        . (($status == 200) ? 'Complete' : 'Failed') . "!!!. $messageBack"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    function SendBlastSMSOld()
    {
        $start = $this->getMicrotime();
        $permissionName = 'Send Blast SMS';
        $data = (array)$this->request->getJsonRawBody();

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request SendBlastSMS:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $shortCode = $data['sender_id'] ?? false;
        $campaign_name = $data['campaign_name'] ?? false;
        $blast_filter = $data['blast_filter'] ?? false;
        $blast_message = $data['blast_message'] ?? false;
        $phone_number = $data['phone_number'] ?? false;

        $count = $data['count'] ?? false;
        $depositor_type = $data['depositor_type'] ?? false;
        $deposit_count_min = $data['deposit_count_min'] ?? false;
        $deposit_count_max = $data['deposit_count_max'] ?? false;
        $deposit_amount_min = $data['deposit_amount_min'] ?? false;
        $deposit_amount_max = $data['deposit_amount_max'] ?? false;
        $profile_status = $data['profile_status'] ?? false;
        $balance_status = $data['balance_status'] ?? false;
        $balance_min = $data['balance_min'] ?? false;
        $balance_max = $data['balance_max'] ?? false;
        $bonus_status = $data['bonus_status'] ?? false;
        $bonus_min = $data['bonus_min'] ?? false;
        $bonus_max = $data['bonus_max'] ?? false;
        $isScheduled = $data['isScheduled'] ?? false;
        $ScheduleDate = $data['scheduleDate'] ?? false;
        $scheduleTime = $data['scheduleTime'] ?? false;
        $start = $data['start_date'] ?? false;
        $stop = $data['end_date'] ?? false;

        // Convert empty strings to false for proper boolean checks
        if ($bonus_status === '') $bonus_status = false;
        if ($depositor_type === '') $depositor_type = false;
        if ($balance_status === '') $balance_status = false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$blast_message || !$campaign_name || !$shortCode) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (strlen($blast_message) < 20) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "The Blast message should have atleast 20 characters!"], true);
        }

        $sms_pages = 1;
        if (strlen($blast_message) > 160) {
            $sms_pages = ceil(strlen($blast_message) / 160);
        }

        if (!$blast_filter) {
            $blast_filter = 'all';
        }

        if (!in_array($shortCode, $this->settings['mnoApps']['DefaultSenders'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid Sender Names!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }


            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

//            if (!in_array($authResponse['data']['role_id'], [1, 2, 3])) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    200,
//                    'Request is not successful',
//                    ['code' => 403,
//                        'message' => "Action require priviledged access!"], true);
//            }

            $recipients = [];
            $searchParams = [];
            $join = " INNER JOIN profile_attribution pa ON p.id=pa.profile_id ";

            // Build base conditions that apply to all queries
            $baseConditions = "";

            // Check if customers can receive sms
            $baseConditions .= " AND pa.can_market = 1";

            // Self exclusion
            $baseConditions .= " AND p.msisdn NOT IN (SELECT msisdn FROM profile_self_exlusion) ";

            // Date range filtering
            if (($stop != null) && ($start != null)) {
                $baseConditions .= " AND p.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $baseConditions .= " AND p.created_at <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $baseConditions .= " AND p.created_at >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            if ($phone_number) {
                $searchQuery = " WHERE p.msisdn = :phone_number ";
                $searchParams[':phone_number'] = $phone_number;
                $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";

                $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                if (!$recipients) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "No recipients found."], true);
                }
            } else {

                // profile_status == 2
                if ($profile_status == 2) {
                    // Join profile_login and check status = 2
                    $join .= " INNER JOIN profile_login pl ON p.id=pl.profile_id ";
                    $searchQuery = " WHERE pl.status = 2 " . $baseConditions;
                    $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";
                    $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                    if (!$recipients) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => "No recipients found."], true);
                    }

                }

                //log
                // Handle profile_status == 1 (all profiles)
                if ($profile_status == 1) {
                    // All profiles - no profile_login join needed
                    $searchQuery = " WHERE 1 " . $baseConditions;

                    // Add balance status filtering if specified
                    if ($balance_status !== false && $balance_status !== null) {
                        if ($balance_status == 1) {
                            // profiles with balance greater than 0
                            $join .= " INNER JOIN profile_balance pb ON p.id=pb.profile_id ";
                            if ($balance_min && $balance_max) {
                                $searchQuery .= " AND pb.balance BETWEEN :balance_min AND :balance_max";
                                $searchParams[':balance_min'] = $balance_min;
                                $searchParams[':balance_max'] = $balance_max;
                            } else {
                                $searchQuery .= " AND pb.balance > 0";
                            }
                            $searchQuery .= " AND pb.status=1 ";
                        }

                        if ($balance_status == 0) {
                            // profiles with 0 balance or no balance record
                            $searchQuery .= " AND NOT EXISTS (SELECT 1 FROM profile_balance pb2 WHERE pb2.profile_id = p.id AND pb2.balance > 0 AND pb2.status = 1) ";
                        }
                    }


                    // Add bonus status filtering if specified
                    if ($bonus_status !== false && $bonus_status !== null) {
                        if ($bonus_status == 1) {
                            // profiles with bonus greater than 0
                            $join .= " INNER JOIN profile_bonus pbo ON p.id=pbo.profile_id ";
                            $searchQuery .= " AND pbo.profile_id IS NOT NULL AND pbo.status=1";

                            if ($bonus_min && $bonus_max) {
                                // First group by profile_id to sum bonuses, then filter by the sum
                                $searchQuery .= " GROUP BY p.id HAVING SUM(pbo.bonus_amount) BETWEEN :bonus_min AND :bonus_max";
                                $searchParams[':bonus_min'] = $bonus_min;
                                $searchParams[':bonus_max'] = $bonus_max;
                            } else {
                                $searchQuery .= " AND pbo.bonus_amount > 0";
                            }
                        }

                        if ($bonus_status == 0) {
                            // profiles with 0 bonus or no bonus record
                            $searchQuery .= " AND NOT EXISTS (SELECT 1 FROM profile_bonus pbo2 WHERE pbo2.profile_id = p.id AND pbo2.bonus_amount > 0 AND pbo2.status = 1) ";
                        }
                    }

                    // Add depositor type filtering if specified
                    if ($depositor_type !== false && $depositor_type !== null) {
                        if ($depositor_type == 0) {
                            // pa.deposit_count = 0 or no deposit record
                            $searchQuery .= " AND (pa.deposit_count = 0 OR pa.deposit_count IS NULL) ";
                        } else if ($depositor_type == 1) {
                            // pa.deposit_count = 1
                            $searchQuery .= " AND pa.deposit_count = 1 ";
                        } else if ($depositor_type == 2) {
                            // pa.deposit_count > 1
                            $searchQuery .= " AND pa.deposit_count > 1 ";
                            if ($deposit_count_min && $deposit_count_max) {
                                $searchQuery .= " AND pa.deposit_count BETWEEN :deposit_count_min AND :deposit_count_max";
                                $searchParams[':deposit_count_min'] = $deposit_count_min;
                                $searchParams[':deposit_count_max'] = $deposit_count_max;
                            }
                            if ($deposit_amount_min && $deposit_amount_max) {
                                $searchQuery .= " AND pa.total_deposits BETWEEN :deposit_amount_min AND :deposit_amount_max";
                                $searchParams[':deposit_amount_min'] = $deposit_amount_min;
                                $searchParams[':deposit_amount_max'] = $deposit_amount_max;
                            }
                        } else if ($depositor_type == 3) {
                            // pa.deposit_count >= 1
                            $searchQuery .= " AND pa.deposit_count >= 1 ";
                        }
                    }


                    $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";

                    $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                    if (!$recipients) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => "No recipients found."], true);
                    }

                }

                // Handle profile_status == 6 (profile_login status 6) - can combine with other filters
                if ($profile_status == 6) {
                    // Join profile_login and check status = 6
                    $join .= " INNER JOIN profile_login pl ON p.id=pl.profile_id ";
                    $searchQuery = " WHERE pl.status = 6 " . $baseConditions;

                    // Add balance status filtering if specified
                    if ($balance_status !== false && $balance_status !== null) {
                        if ($balance_status == 1) {
                            // profiles with balance greater than 0
                            $join .= " INNER JOIN profile_balance pb ON p.id=pb.profile_id ";
                            if ($balance_min && $balance_max) {
                                $searchQuery .= " AND pb.balance BETWEEN :balance_min AND :balance_max";
                                $searchParams[':balance_min'] = $balance_min;
                                $searchParams[':balance_max'] = $balance_max;
                            } else {
                                $searchQuery .= " AND pb.balance > 0";
                            }
                            $searchQuery .= " AND pb.status=1 ";
                        }

                        if ($balance_status == 0) {
                            // profiles with 0 balance or no balance record
                            $searchQuery .= " AND NOT EXISTS (SELECT 1 FROM profile_balance pb2 WHERE pb2.profile_id = p.id AND pb2.balance > 0 AND pb2.status = 1) ";
                        }
                    }

                    // Add bonus status filtering if specified
                    if ($bonus_status !== false && $bonus_status !== null) {
                        if ($bonus_status == 1) {
                            // profiles with bonus greater than 0
                            $join .= " INNER JOIN profile_bonus pbo ON p.id=pbo.profile_id ";
                            $searchQuery .= " AND pbo.profile_id IS NOT NULL AND pbo.status=1";

                            if ($bonus_min && $bonus_max) {
                                // First group by profile_id to sum bonuses, then filter by the sum
                                $searchQuery .= " GROUP BY p.id HAVING SUM(pbo.bonus_amount) BETWEEN :bonus_min AND :bonus_max";
                                $searchParams[':bonus_min'] = $bonus_min;
                                $searchParams[':bonus_max'] = $bonus_max;
                            } else {
                                $searchQuery .= " AND pbo.bonus_amount > 0";
                            }
                        }

                        if ($bonus_status == 0) {
                            // profiles with 0 bonus or no bonus record
                            $searchQuery .= " AND NOT EXISTS (SELECT 1 FROM profile_bonus pbo2 WHERE pbo2.profile_id = p.id AND pbo2.bonus_amount > 0 AND pbo2.status = 1) ";
                        }
                    }

                    // Add depositor type filtering if specified
                    if ($depositor_type !== false && $depositor_type !== null) {
                        if ($depositor_type == 0) {
                            // pa.deposit_count = 0 or no deposit record
                            $searchQuery .= " AND (pa.deposit_count = 0 OR pa.deposit_count IS NULL) ";
                        } else if ($depositor_type == 1) {
                            // pa.deposit_count = 1
                            $searchQuery .= " AND pa.deposit_count = 1 ";
                        } else if ($depositor_type == 2) {
                            // pa.deposit_count > 1
                            $searchQuery .= " AND pa.deposit_count > 1 ";
                            if ($deposit_count_min && $deposit_count_max) {
                                $searchQuery .= " AND pa.deposit_count BETWEEN :deposit_count_min AND :deposit_count_max";
                                $searchParams[':deposit_count_min'] = $deposit_count_min;
                                $searchParams[':deposit_count_max'] = $deposit_count_max;
                            }
                            if ($deposit_amount_min && $deposit_amount_max) {
                                $searchQuery .= " AND pa.total_deposits BETWEEN :deposit_amount_min AND :deposit_amount_max";
                                $searchParams[':deposit_amount_min'] = $deposit_amount_min;
                                $searchParams[':deposit_amount_max'] = $deposit_amount_max;
                            }
                        } else if ($depositor_type == 3) {
                            // pa.deposit_count >= 1
                            $searchQuery .= " AND pa.deposit_count >= 1 ";
                        }
                    }

                    $sql = "SELECT p.msisdn FROM profile p $join $searchQuery";

//                    $this->infologger->info(__LINE__ . ":" . __CLASS__
//                        . " | Final SQL: " . $sql
//                        . " | Params: " . json_encode($searchParams)
//                    );

                    $recipients = $this->rawSelect('dbProfile', $sql, $searchParams);

                    if (!$recipients) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => "No recipients found."], true);
                    }
                }

            }

            if (!$recipients) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 500,
                        'message' => "Query returned an empty Contact list(s)!"], true);
            } else if ($count) {
                // return the count
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is successful',
                    ['code' => 200,
                        'message' => "Query returned " . count($recipients) . " Contact(s)!",
                        'data' => ['record_count' => count($recipients),
                            'result' => count($recipients)
                        ]], false, true);
            }


//            if ($timestamp) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    200,
//                    'Request is successful',
//                    ['code' => 500,
//                        'message' => "You said i stop here!"], true);
//            }

            $fileName = "FINAL_DATA_BLAST_" . rand(1000, 99999) . ".csv";
            $filePath = $this->settings['uploadDir']['Sms'] . "" . $fileName;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $csv_row = [];
            $csv_row[] = explode(',', $this->settings['uploadDir']['SmsHeaders']);

            $recipients[] = ["254704050143", 'Josephat', '', '',];
            $recipients[] = ["254725547995", 'Daniel', '', '',];
            $recipients[] = ["254707089565", 'Daniel', '', '',];
            $recipients[] = ["254721137826", 'Josephat M', '', '',];
            $recipients[] = ["254703968228", 'Allan O', '', '',];
            $recipients[] = ["254714919776", 'J K', '', '',];
            header('Content-Type: text/csv; charset=utf-8');

            $file = fopen($filePath, 'w');
            foreach ($csv_row as $line) {
                fputcsv($file, $line);
                foreach ($recipients as $value) {
                    fputcsv($file, $value);
                }
            }

            $correlator = $this->rawInsertBulk('dbUser',
                'bulk_sms_out',
                ['created_at' => $this->now(),
                    'bulk_filter' => $blast_filter,
                    'message' => $blast_message,
                    'recipients' => count($recipients) - 1,
                    'sms_pages' => $sms_pages,
                    'user_id' => $authResponse['data']['user_id'],
                    'is_scheduled' => $isScheduled,
                    'schedule_date' => $isScheduled > 0 ? $ScheduleDate : null,
                    'schedule_time' => $isScheduled > 0 ? $scheduleTime : null,
                    'status' => $isScheduled>0?1:0]
            );

            $fileType = mime_content_type($filePath);

            $msgUtils = new Messaging();
            $token = $msgUtils->GetLidenAccessToken();
            if (!$token) {
                $key = $this->settings['appName'] . '$GetLidenAccessToken_AccessKey';
                RedisUtils::redisRawDelete($key);

                $this->rawUpdateWithParams('dbUser',
                    "UPDATE bulk_sms_out SET status=:status,completed_on=NOW() WHERE id=:id LIMIT 1",
                    [   ':id' => $correlator,
                        ':status' => 100]);

                unlink($filePath);

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . '| CorrelatorId:' . $correlator
                    . "| Authentication process failed!");

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Blast Sending Failed. Authentication process failed"], true);
            }

            $url = $this->settings['mnoApps']['Urls']['BulkBroadcastUrl'];

            $header = ['Content-Type: multipart/form-data',
                'X-Authorization-Key:' . $token,
                'X-Requested-With:XMLHttpRequest'];

            $postData = new stdClass();
            $postData->shortCode = "$shortCode";
            $postData->message = "$blast_message";
            $postData->isScheduled = (int)$isScheduled;
            $postData->scheduleDate = "$ScheduleDate";
            $postData->scheduleTime = "$scheduleTime";
            $postData->approval = "";
            $postData->uniqueId = $correlator;
            $postData->callbackURL = '';
            $postData->file = new CurlFile($filePath, $fileType, $fileName);

            $httpRequest = curl_init();
            curl_setopt($httpRequest, CURLOPT_URL, $url);
            curl_setopt($httpRequest, CURLOPT_POST, true);
            curl_setopt($httpRequest, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $header);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, true);
            $results = curl_exec($httpRequest);
            $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
            curl_close($httpRequest);

            $serverResponse = json_decode($results);
            $code = $serverResponse->code ?? 'Error';
            $messageBack = $serverResponse->statusDescription
                . ". Reason:" . ($serverResponse->data->message ?? "Request Failed");

            // Update bulk_sms_out with campaign_id and status
            $updateParams = [
                ':id' => $correlator,
                ':status' => $status];

            $updateSql = "UPDATE bulk_sms_out SET status=:status,completed_on=NOW()";

            if ($status == 200 && $code == 'Success') {
                $updateSql .= ",campaign_id=:campaign_id";
                $updateParams[':campaign_id'] = $serverResponse->data->data->sms_data->campaign_id;
            }

            $updateSql .= " WHERE id=:id LIMIT 1";

            $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);
            unlink($filePath);

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start) . " Sec",
                200,
                $serverResponse->statusDescription,
                ['code' => ($status != 200) ? 400 : $status,
                    'message' => "Blast Sending "
                        . (($status == 200) ? 'Complete' : 'Failed') . "!!!. $messageBack"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetScheduledSMS
     * @return type
     */
    function GetScheduledSMS() {
        $start = $this->getMicrotime();

        $permissionName = "View Scheduled SMS";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request GetScheduledSMS:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $campaign_id = $data['campaign_id'] ?? false;
        $count = $data['count'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $skipCache = $data['skip_cache'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;

//        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp) {
//            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                200,
//                'Request is not successful',
//                ['code' => 422,
//                    'message' => "Mandatory field(s) required!"], true);
//        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($skipCache, [1, 2])) {
            $skipCache = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "bulk_sms_out." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = $order_arr[1] ?? 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'bulk_sms_out.id';
            $order = 'DESC';
        }

        try {

            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

//            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    200,
//                    'Request is not successful',
//                    ['code' => 403,
//                        'message' => "Action require priviledged access!"], true);
//            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($campaign_id) {
                $searchParams[':campaign_id'] = $campaign_id;
                $searchQuery .= " AND bulk_sms_out.campaign_id = :campaign_id";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            $sql_query = "SELECT *, COUNT(bulk_sms_out.id) OVER() AS trx_count
        FROM bulk_sms_out
        $searchQuery
        $sorting";

            $results = $this->rawSelect('dbUser', $sql_query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Bulk SMS!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Bulk SMS successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        }  catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

}


