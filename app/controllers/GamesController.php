<?php

/**
 * GamesController
 */
class GamesController extends \ControllerBase
{

    function GetManualResultFixtures()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Manual Result Fixtures";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetManualResultFixtures Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $team_name = $data['team_name'] ?? false;
        $game_start_time = $data['start_time'] ?? false;
        $date = $data['date'] ?? false;
        $country = $data['country'] ?? false;
        $tournament_id = $data['tournament_id'] ?? false;
        $sport_id = $data['sport_id'] ?? false;
        $game_id = $data['game_id'] ?? false;
        $match_id = $data['match_id'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? "0";
        $start = $data['start'] ?? false;
        $end = $data['end'] ?? false;
        $skipCache = $data['skip_cache'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "sbs." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'f.date';
            $order = 'ASC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Database Query
            $searchQuery = " WHERE 1";
            $searchParams = [];
            if (is_numeric($match_id)) {
                $searchParams[':match_id'] = $match_id;
                $searchQuery .= " AND sbs.parent_match_id = :match_id";
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND sbs.status = :status";
            }

            if ($tournament_id) {
                $searchParams[':tournament_id'] = $tournament_id;
                $searchQuery .= " AND f.tournament_id = :tournament_id";
            }

            if ($match_id) {
                $searchParams[':match_id'] = $match_id;
                $searchQuery .= " AND sbs.parent_match_id = :match_id";
            }

            if ($game_id) {
                $searchParams[':game_id'] = $game_id;
                $searchQuery .= " AND f.game_id = :game_id";
            }

            if ($team_name) {
                $searchParams[':team_name'] = "%$team_name%";
                $searchQuery .= " AND f.name LIKE :team_name";
            }

            if ($sport_id) {
                $searchParams[':sport_id'] = $sport_id;
                $searchQuery .= " AND f.sport_id = :sport_id";
            }

            if ($country) {
                $searchParams[':country'] = $country;
                $searchQuery .= " AND f.country = :country";
            }

            $searchQuery .= " AND sbs.winning_outcome IS NULL";
//            $searchQuery .= " AND sbs.ft_scores IS NULL";

            if (!$date) {
                $date = date('Y-m-d H:i:s', strtotime('-4 days'));
            }

            $searchParams[':date'] = $date;
            $searchQuery .= " AND f.date BETWEEN :date AND NOW()";

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = 'SELECT (SELECT count(sbs.bet_id) '
                . 'FROM mossbets_bets.sports_bet_slip sbs '
                . 'JOIN mossbets_sports_book.fixture f ON sbs.parent_match_id = f.match_id '
                . 'JOIN mossbets_sports_book.sports s ON f.sport_id = s.sport_id '
                . $searchQuery . ') as trx_count, '

                . 'f.match_id, f.game_id, f.name, f.country, f.category_name, sbs.parent_market_id, sbs.outcome_name,'
                . 'f.half_time_scores, f.full_time_scores, f.extra_time_scores, sbs.pick_name, s.sport_name, '
                . 'sbs.ht_scores, sbs.ft_scores, sbs.et_scores, sbs.live_bet, sbs.extra_data, sbs.winning_outcome, '
                . 'sbs.status, sbs.start_time, f.date as game_start_time, sbs.created_at, sbs.updated_at, '
                . '(SELECT COUNT(bet_id) FROM mossbets_bets.sports_bet_slip WHERE parent_match_id = f.match_id) as bet_slip_count '

                . 'FROM mossbets_bets.sports_bet_slip sbs '
                . 'JOIN mossbets_sports_book.fixture f ON sbs.parent_match_id = f.match_id '
                . 'JOIN mossbets_sports_book.sports s ON f.sport_id = s.sport_id '
                . $searchQuery . ' GROUP BY  f.match_id ' . $sorting;

            $results = $this->rawSelect('dbSports', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Fixtures found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Fixtures fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetManualResultFixturesArchive
     * @return \Phalcon\Http\Response
     */
    function GetManualResultFixturesArchive()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Manual Result Fixtures";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetManualResultFixtures Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $team_name = $data['team_name'] ?? false;
        $game_start_time = $data['start_time'] ?? false;
        $date = $data['date'] ?? false;
        $country = $data['country'] ?? false;
        $tournament_id = $data['tournament_id'] ?? false;
        $sport_id = $data['sport_id'] ?? false;
        $game_id = $data['game_id'] ?? false;
        $match_id = $data['match_id'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? "0";
        $start = $data['start'] ?? false;
        $end = $data['end'] ?? false;
        $skipCache = $data['skip_cache'] ?? false;
        $archive = $data['archive'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "sbs." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'f.date';
            $order = 'ASC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Database Query
            $searchQuery = " WHERE 1";
            $searchParams = [];
            if (is_numeric($match_id)) {
                $searchParams[':match_id'] = $match_id;
                $searchQuery .= " AND sbs.parent_match_id = :match_id";
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND sbs.status = :status";
            }

            if ($tournament_id) {
                $searchParams[':tournament_id'] = $tournament_id;
                $searchQuery .= " AND f.tournament_id = :tournament_id";
            }

            if ($match_id) {
                $searchParams[':match_id'] = $match_id;
                $searchQuery .= " AND sbs.parent_match_id = :match_id";
            }

            if ($game_id) {
                $searchParams[':game_id'] = $game_id;
                $searchQuery .= " AND f.game_id = :game_id";
            }

            if ($team_name) {
                $searchParams[':team_name'] = "%$team_name%";
                $searchQuery .= " AND f.name LIKE :team_name";
            }

            if ($sport_id) {
                $searchParams[':sport_id'] = $sport_id;
                $searchQuery .= " AND f.sport_id = :sport_id";
            }

            if ($country) {
                $searchParams[':country'] = $country;
                $searchQuery .= " AND f.country = :country";
            }

            $searchQuery .= " AND sbs.winning_outcome IS NULL";
//            $searchQuery .= " AND sbs.ft_scores IS NULL";

            if (!$date) {
                $date = date('Y-m-d H:i:s', strtotime('-4 days'));
            }

            $searchParams[':date'] = $date;
            $searchQuery .= " AND f.date BETWEEN :date AND NOW()";

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            $archive_table_name = "fixture_archive_$archive";


            $sql = 'SELECT (SELECT count(sbs.bet_id) '
                . 'FROM mossbets_bets.sports_bet_slip sbs '
                . 'JOIN mossbets_sports_book.' . $archive_table_name . ' f ON sbs.parent_match_id = f.match_id '
                . 'JOIN mossbets_sports_book.sports s ON f.sport_id = s.sport_id '
                . $searchQuery . ') as trx_count, '

                . 'f.match_id, f.game_id, f.name, f.country, f.category_name, sbs.parent_market_id, sbs.outcome_name,'
                . 'f.half_time_scores, f.full_time_scores, f.extra_time_scores, sbs.pick_name, s.sport_name, '
                . 'sbs.ht_scores, sbs.ft_scores, sbs.et_scores, sbs.live_bet, sbs.extra_data, sbs.winning_outcome, '
                . 'sbs.status, sbs.start_time, f.date as game_start_time, sbs.created_at, sbs.updated_at, '
                . '(SELECT COUNT(bet_id) FROM mossbets_bets.sports_bet_slip WHERE parent_match_id = f.match_id) as bet_slip_count '

                . 'FROM mossbets_bets.sports_bet_slip sbs '
                . 'JOIN mossbets_sports_book.' . $archive_table_name . ' f ON sbs.parent_match_id = f.match_id '
                . 'JOIN mossbets_sports_book.sports s ON f.sport_id = s.sport_id '
                . $searchQuery . ' GROUP BY  f.match_id ' . $sorting;

            $results = $this->rawSelect('dbSports', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Fixtures found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Fixtures fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * UpdateFixtureScores
     * @param type $fixtureId
     * @return type
     */
    function UpdateFixtureScoresArchive($fixtureId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Update Fixture Scores";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request UpdateFixtureScores:" . json_encode($data) . " Fixture Id: " . $fixtureId);

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $half_time_scores = $data['half_time_scores'] ?? false;
        $full_time_scores = $data['full_time_scores'] ?? false;
        $extra_time_scores = $data['extra_time_scores'] ?? false;
        $extra_results = $data['extra_results'] ?? false;
        $resulting_type = $data['resulting_type'] ?? false;
        $archive = $data['archive'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$fixtureId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $archive_table_name = "fixture_archive_$archive";

            $results = $this->rawSelectOneRecord('dbSportsRead',
                "SELECT f.match_id, f.sport_id, f.competitor1, f.competitor2, "
                . "f.fixture_priority, f.highlights_priority,"
                . "f.upcoming_priority, f.boosted_priority, f.tournament_priority "
                . "FROM $archive_table_name f",
                [':match_id' => $fixtureId]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Fixture Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':match_id' => $fixtureId];

            // Manual Resulting
            if ($half_time_scores) {
                $fields[] = "ht_scores = :ht_scores";
                $params[':ht_scores'] = $half_time_scores;
            }
            if ($full_time_scores) {
                $fields[] = "ft_scores = :ft_scores";
                $params[':ft_scores'] = $full_time_scores;
            }
            if ($extra_time_scores) {
                $fields[] = "et_scores = :et_scores";
                $params[':et_scores'] = $extra_time_scores;
            }
            if ($extra_results) {
                $fields[] = "extra_data = :extra_data";
                $params[':extra_data'] = $extra_results;
            }
            if ($resulting_type) {
                $fields[] = "resulting_type = :resulting_type";
                $params[':resulting_type'] = $resulting_type;
            }

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE sports_bet_slip SET " . implode(', ', $fields) . " WHERE parent_match_id = :match_id";

            if (!$this->rawUpdateWithParams('dbBets', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful', [
                        'code' => 400,
                        'message' => "Sorry, Scores Update failed for fixture ID: $fixtureId"
                    ], true);
            }

            function parseScore($score): array
            {
                if (!$score && $score !== '') return ['ht' => null, 'at' => null]; // Return null if empty

                // Replace different possible separators with a single "-"
                $score = str_replace([" ", ":", "/"], "-", $score);

                // Explode into two values
                $scores = explode("-", $score);
                return [
                    'ht' => isset($scores[0]) && is_numeric($scores[0]) ? intval($scores[0]) : null,
                    'at' => isset($scores[1]) && is_numeric($scores[1]) ? intval($scores[1]) : null
                ];
            }

            // Ensure extra_results is decoded properly
            $extra_results = is_string($extra_results) ? json_decode($extra_results, true) : $extra_results;
            $extra_results = is_array($extra_results) ? $extra_results : [];

            // Queue
            if ($resulting_type == "Manual") {
                if ($full_time_scores === 'void') {
                    $payload = [
                        'match_id' => $fixtureId,
                        'sport_id' => $results['sport_id'],
                        'resulting_type' => $resulting_type,
                        'status' => 7,
                        'Results' => [
                            'ht_scores' => parseScore($half_time_scores),
                            'ft_scores' => parseScore($full_time_scores),
                            'et_scores' => parseScore($extra_time_scores),
                            'corners' => parseScore($extra_results['corners'] ?? ''),
                            'yellow_cards' => parseScore($extra_results['yellow_cards'] ?? ''),
                            'red_cards' => parseScore($extra_results['red_cards'] ?? ''),
                            'penalties' => parseScore($extra_results['penalties'] ?? ''),
                        ],
                    ];
                } else {
                    $payload = [
                        'match_id' => $fixtureId,
                        'sport_id' => $results['sport_id'],
                        'resulting_type' => $resulting_type,
                        'status' => null,
                        'Results' => [
                            'ht_scores' => parseScore($half_time_scores),
                            'ft_scores' => parseScore($full_time_scores),
                            'et_scores' => parseScore($extra_time_scores),
                            'corners' => parseScore($extra_results['corners'] ?? ''),
                            'yellow_cards' => parseScore($extra_results['yellow_cards'] ?? ''),
                            'red_cards' => parseScore($extra_results['red_cards'] ?? ''),
                            'penalties' => parseScore($extra_results['penalties'] ?? ''),
                        ],
                    ];
                }

                $queue = new Queue();
                $res = $queue->ConnectAndPublishToQueue($payload,
                    'MANUAL_RESULTS', 'MANUAL_RESULTS', 'MANUAL_RESULTS'
                    , null, null, null, null, "/", 'LIDEN');

            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated fixture scores "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetBetSlipCount
     * @return type
     */
    function GetBetSlipCount()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Bet Slip Count";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetBetSlipCount Request:" . json_encode($data));

        // Do verification
        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $match_id = $data['match_id'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$match_id) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory fields are missing!"], true);
        }

        try {
            $authResponse = $this->AuthenticateRequest($Authorization, $appKey, $hashKey, $timestamp, $accessToken, $data);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }


            $sql = "SELECT COUNT(sports_bet_slip.bet_id) as bet_count FROM sports_bet_slip WHERE sports_bet_slip.parent_match_id = :match_id";
            $results = $this->rawSelect('dbSports', $sql, [':match_id' => $match_id]);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Bet Slip found!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Bet Slip count fetched successfully!',
                    'data' => $results[0]['bet_count']], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }

    }

    /**
     * GetFixtures
     * @param type
     * @return type
     */
    function GetFixtures()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Fixtures";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetFixtures Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $match_id = $data['match_id'] ?? false;
        $game_id = $data['game_id'] ?? false;
        $team_name = $data['team_name'] ?? false;
        $sport_id = $data['sport_id'] ?? false;
        $tournament_id = $data['tournament_id'] ?? false;
        $country = $data['country'] ?? false;
        $is_live = $data['is_live'] ?? false;
        $play_date = $data['play_date'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;
        $start = $data['start'] ?? false;
        $end = $data['end'] ?? false;
        $skipCache = $data['skip_cache'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "fixture." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'fixture.date';
            $order = 'ASC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Database Query
            $searchQuery = " WHERE 1";
            $searchParams = [];
            if (is_numeric($match_id)) {
                $searchParams[':match_id'] = $match_id;
                $searchQuery .= " AND fixture.match_id = :match_id";
            }

            if ($game_id) {
                $searchParams[':game_id'] = $game_id;
                $searchQuery .= " AND fixture.game_id = :game_id";
            }

            // search competitor1 and competitor2 where they are = team_name
            if ($team_name) {
                $searchParams[':team_name'] = "%$team_name%"; // Use wildcard for partial match
                $searchQuery .= " AND (fixture.competitor1 LIKE :team_name OR fixture.competitor2 LIKE :team_name)";
            }

            if (is_numeric($sport_id)) {
                $searchParams[':sport_id'] = $sport_id;
                $searchQuery .= " AND fixture.sport_id = :sport_id";
            }

            if (is_numeric($tournament_id)) {
                $searchParams[':tournament_id'] = $tournament_id;
                $searchQuery .= " AND fixture.category_id = :tournament_id";
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND fixture.status = :status";
            }

            if ($country != null) {
                $searchParams[':country'] = $country;
                $searchQuery .= " AND fixture.country = :country";
            }

            if ($is_live !== null) {
                if ($is_live === '1') {
                    // Only include fixtures that exist in `fixture_status`
                    $searchQuery .= " AND EXISTS (
                        SELECT 1 FROM fixture_status
                        WHERE fixture_status.match_id = fixture.match_id
                    )";
                } elseif ($is_live === '0') {
                    // Only include fixtures that DO NOT exist in `fixture_status`
                    $searchQuery .= " AND NOT EXISTS (
                        SELECT 1 FROM fixture_status
                        WHERE fixture_status.match_id = fixture.match_id
                    )";
                }
            }

            if ($play_date != null) {
                $start = null;
                $end = null;
                // split $play_start by space
                $play_date_arr = explode(" ", $play_date);
                $play_start = $play_date_arr[0];
                $play_end = $play_date_arr[1];

                $searchParams[':play_start'] = "$play_start 00:00:00";
                $searchParams[':play_end'] = "$play_end 23:59:59";
                $searchQuery .= " AND fixture.date BETWEEN :play_start AND :play_end";
            }

            if (($end != null) && ($start != null)) {
                $searchQuery .= " AND fixture.created BETWEEN :start AND :end ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':end'] = "$end 23:59:59";
            } elseif (($end != null) && ($start == null)) {
                $searchQuery .= " AND fixture.created <=:end";
                $searchParams[':end'] = "$end 23:59:59";
            } elseif ($end == null && $start != null) {
                $searchQuery .= " AND fixture.created>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            // Dynamically add JOINs if needed
            $joins = "JOIN sports ON fixture.sport_id = sports.sport_id "
                . "JOIN tournaments ON fixture.category_id = tournaments.tournament_id "
                . "LEFT JOIN fixture_status ON fixture.match_id = fixture_status.match_id ";

            // Combine with search query
            $sql = $searchQuery . ' ' . $sorting;

            // Query for main data
            $query = "SELECT (SELECT COUNT(fixture.id) "
                . " FROM fixture $joins $searchQuery) AS trx_count,"
                . "fixture.match_id, fixture.game_id, fixture.name, fixture.country, fixture.sport_id,"
                . "fixture.tournament, fixture.tournament_id, fixture.category_id AS new_tournament_id, fixture.category_name,"
                . "fixture.competitor1, fixture.competitor2, fixture.home_competitor_id, fixture.away_competitor_id, "
                . "fixture.date, fixture.highlights_priority, fixture.fixture_priority, fixture.upcoming_priority, "
                . "fixture.boosted_priority, fixture.withdrawal, fixture.highlight_tournament, "
                . "IFNULL(fixture.coverage,0)AS pre_match_coverage, fixture_status.coverage AS live_coverage, "
                . "fixture_status.match_status, fixture_status.away_score, fixture_status.home_score, "
                . "sports.sport_name, sports.priority AS sport_priority, sports.total_fixtures, fixture.created, fixture.updated "
                . "FROM fixture $joins $sql";

//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . ": SQL: $query "
//                . "| Fixtures params:" . json_encode($searchParams) . " searchQry " . json_encode($searchQuery));

            $results = $this->rawSelect('dbSports', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Fixtures found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];


            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Fixtures fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * EditFixture
     * @param type $fixtureId
     * @return type
     */
    function EditFixture($fixtureId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Edit Fixture";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request EditFixture:" . json_encode($data) . " Fixture Id: " . $fixtureId);

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $half_time_scores = $data['half_time_scores'] ?? false;
        $full_time_scores = $data['full_time_scores'] ?? false;
        $extra_time_scores = $data['extra_time_scores'] ?? false;
        $extra_results = $data['extra_results'] ?? false;
        $resulting_type = $data['resulting_type'] ?? false;

        $pre_match_coverage = $data['pre_match_coverage'] ?? false;
        $live_coverage = $data['live_coverage'] ?? false;
        $fixture_priority = $data['fixture_priority'] ?? false;
        $tournament_priority = $data['tournament_priority'] ?? false;
        $highlights_priority = $data['highlights_priority'] ?? false;
        $boosted_priority = $data['boosted_priority'] ?? false;
        $upcoming_priority = $data['upcoming_priority'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$fixtureId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbSportsRead',
                "SELECT f.match_id, f.competitor1, f.competitor2, f.fixture_priority, f.highlights_priority,
                f.upcoming_priority, f.boosted_priority, f.tournament_priority, f.coverage "
                . "FROM fixture f",
                [':match_id' => $fixtureId]);


            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Fixture Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':match_id' => $fixtureId];

            if (is_numeric($tournament_priority)) {
                $fields[] = "tournament_priority = :tournament_priority";
                $params[':tournament_priority'] = $tournament_priority;
            }
            if (is_numeric($highlights_priority)) {
                $fields[] = "highlights_priority = :highlights_priority";
                $params[':highlights_priority'] = $highlights_priority;
            }
            if (is_numeric($boosted_priority)) {
                $fields[] = "boosted_priority = :boosted_priority";
                $params[':boosted_priority'] = $boosted_priority;
            }
            if (is_numeric($upcoming_priority)) {
                $fields[] = "upcoming_priority = :upcoming_priority";
                $params[':upcoming_priority'] = $upcoming_priority;
            }
            if (is_numeric($fixture_priority)) {
                $fields[] = "fixture_priority = :fixture_priority";
                $params[':fixture_priority'] = $fixture_priority;
            }

            if (is_numeric($pre_match_coverage)) {
                $fields[] = "coverage = :coverage";
                $params[':coverage'] = $pre_match_coverage;
            }
            // Manual Resulting
            if ($half_time_scores) {
                $fields[] = "half_time_scores = :half_time_scores";
                $params[':half_time_scores'] = $half_time_scores;
            }
            if ($full_time_scores) {
                $fields[] = "full_time_scores = :full_time_scores";
                $params[':full_time_scores'] = $full_time_scores;
            }
            if ($extra_time_scores) {
                $fields[] = "extra_time_scores = :extra_time_scores";
                $params[':extra_time_scores'] = $extra_time_scores;
            }
            if ($extra_results) {
                $fields[] = "extra_results = :extra_results";
                $params[':extra_results'] = $extra_results;
            }
            if ($resulting_type) {
                $fields[] = "resulting_type = :resulting_type";
                $params[':resulting_type'] = $resulting_type;
            }
            // update updated
            $fields[] = "updated = NOW()";

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400, 'No valid fields provided for update', [], true);
            }

            if (is_numeric($live_coverage)) {
                $fields[] = "coverage = :coverage";
                $params[':coverage'] = $live_coverage;
                $fields[] = "updated = NOW()";

                $sql = "UPDATE fixture_status SET " . implode(', ', $fields) . " WHERE match_id = :match_id Limit 1";

                if (!$this->rawUpdateWithParams('dbSports', $sql, $params)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful', [
                            'code' => 400,
                            'message' => "Sorry, Update failed for fixture ID: $fixtureId"
                        ], true);
                }
            } else {

                $sql = "UPDATE fixture SET " . implode(', ', $fields) . " WHERE match_id = :match_id Limit 1";

                if (!$this->rawUpdateWithParams('dbSports', $sql, $params)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful', [
                            'code' => 400,
                            'message' => "Sorry, Update failed for fixture ID: $fixtureId"
                        ], true);
                }
            }

            // Queue
            if ($resulting_type == "Manual") {
                $payload = [
                    'match_id' => $fixtureId,
                    'sport_id' => $resulting_type,
                    'Results' => [
                        'ht_scores' => [
                            'ht' => '1',
                            'at' => '2'
                        ],
                        'ft_scores' => [
                            'ht' => '1',
                            'at' => '2'
                        ],
                        'et_scores' => [],
                        'corners' => [],
                        'cards' => [],
                        'penalties' => [],
                    ],
                ];

                $queue = new Queue();
                $res = $queue->ConnectAndPublishToQueue($payload,
                    'RESULTS_QUEUE', 'RESULTS_EXCHANGE', 'RESULTS_ROUTE'
                    , null, null, null, null, "/", 'LIDEN');

            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated fixture "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdateFixtureScores
     * @param type $fixtureId
     * @return type
     */
    function UpdateFixtureScores($fixtureId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Update Fixture Scores";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request UpdateFixtureScores:" . json_encode($data) . " Fixture Id: " . $fixtureId);

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $half_time_scores = $data['half_time_scores'] ?? false;
        $full_time_scores = $data['full_time_scores'] ?? false;
        $extra_time_scores = $data['extra_time_scores'] ?? false;
        $extra_results = $data['extra_results'] ?? false;
        $resulting_type = $data['resulting_type'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$fixtureId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbSportsRead',
                "SELECT f.match_id, f.sport_id, f.competitor1, f.competitor2, "
                . "f.fixture_priority, f.highlights_priority,"
                . "f.upcoming_priority, f.boosted_priority, f.tournament_priority "
                . "FROM fixture f",
                [':match_id' => $fixtureId]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Fixture Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':match_id' => $fixtureId];

            // Manual Resulting
            if ($half_time_scores) {
                $fields[] = "ht_scores = :ht_scores";
                $params[':ht_scores'] = $half_time_scores;
            }
            if ($full_time_scores) {
                $fields[] = "ft_scores = :ft_scores";
                $params[':ft_scores'] = $full_time_scores;
            }
            if ($extra_time_scores) {
                $fields[] = "et_scores = :et_scores";
                $params[':et_scores'] = $extra_time_scores;
            }
            if ($extra_results) {
                $fields[] = "extra_data = :extra_data";
                $params[':extra_data'] = $extra_results;
            }
            if ($resulting_type) {
                $fields[] = "resulting_type = :resulting_type";
                $params[':resulting_type'] = $resulting_type;
            }

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE sports_bet_slip SET " . implode(', ', $fields) . " WHERE parent_match_id = :match_id";

            if (!$this->rawUpdateWithParams('dbBets', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful', [
                        'code' => 400,
                        'message' => "Sorry, Scores Update failed for fixture ID: $fixtureId"
                    ], true);
            }

            function parseScore($score): array
            {
                if (!$score && $score !== '') return ['ht' => null, 'at' => null]; // Return null if empty

                // Replace different possible separators with a single "-"
                $score = str_replace([" ", ":", "/"], "-", $score);

                // Explode into two values
                $scores = explode("-", $score);
                return [
                    'ht' => isset($scores[0]) && is_numeric($scores[0]) ? intval($scores[0]) : null,
                    'at' => isset($scores[1]) && is_numeric($scores[1]) ? intval($scores[1]) : null
                ];
            }

            // Ensure extra_results is decoded properly
            $extra_results = is_string($extra_results) ? json_decode($extra_results, true) : $extra_results;
            $extra_results = is_array($extra_results) ? $extra_results : [];

            // Queue
            if ($resulting_type == "Manual") {
                if ($full_time_scores === 'void') {
                    $payload = [
                        'match_id' => $fixtureId,
                        'sport_id' => $results['sport_id'],
                        'resulting_type' => $resulting_type,
                        'status' => 7,
                        'Results' => [
                            'ht_scores' => parseScore($half_time_scores),
                            'ft_scores' => parseScore($full_time_scores),
                            'et_scores' => parseScore($extra_time_scores),
                            'corners' => parseScore($extra_results['corners'] ?? ''),
                            'yellow_cards' => parseScore($extra_results['yellow_cards'] ?? ''),
                            'red_cards' => parseScore($extra_results['red_cards'] ?? ''),
                            'penalties' => parseScore($extra_results['penalties'] ?? ''),
                        ],
                    ];
                } else {
                    $payload = [
                        'match_id' => $fixtureId,
                        'sport_id' => $results['sport_id'],
                        'resulting_type' => $resulting_type,
                        'status' => null,
                        'Results' => [
                            'ht_scores' => parseScore($half_time_scores),
                            'ft_scores' => parseScore($full_time_scores),
                            'et_scores' => parseScore($extra_time_scores),
                            'corners' => parseScore($extra_results['corners'] ?? ''),
                            'yellow_cards' => parseScore($extra_results['yellow_cards'] ?? ''),
                            'red_cards' => parseScore($extra_results['red_cards'] ?? ''),
                            'penalties' => parseScore($extra_results['penalties'] ?? ''),
                        ],
                    ];
                }

                $queue = new Queue();
                $res = $queue->ConnectAndPublishToQueue($payload,
                    'MANUAL_RESULTS', 'MANUAL_RESULTS', 'MANUAL_RESULTS'
                    , null, null, null, null, "/", 'LIDEN');

            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated fixture scores "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    function GetOddsLive()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Odds Live";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetOddsLive Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $matchId = $data['match_id'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "odds_live." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'odds_live.market_priority';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Database Query
            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($matchId) {
                $searchParams["match_id"] = $matchId;
                $searchQuery .= " AND odds_live.match_id = " . $matchId;
            }

            if ($status) {
                $searchParams["status"] = $status;
                $searchQuery .= " AND odds_live.status = " . $status;
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            // Combine with search query
            $sql = $searchQuery . ' ' . $sorting;

            // Query for main data
            $query = "SELECT (SELECT COUNT(odds_live.id) FROM odds_live  $searchQuery) as trx_count,
odds_live.id, odds_live.match_id, odds_live.odd_status, odds_live.outcome_name, odds_live.odd_status, odds_live.odds,
odds_live.prevous_odds, odds_live.direction,odds_live.producer_name,odds_live.market_name,odds_live.status_name,
odds_live.market_priority, odds_live.alias_priority FROM odds_live $sql";


//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . ": SQL: $query"
//                . "| OddsLive params:" . json_encode($searchParams));

            $results = $this->rawSelect('dbSports', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Odds Live found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];


            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Odds Live fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    function GetCountries()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Countries";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetCountries Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;

        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "countries." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'countries.priority';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Database Query
            $searchQuery = " WHERE 1";
            $searchParams = [];

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            // Combine with search query
            $sql = $searchQuery . ' ' . $sorting;

            // Query for main data
            $query = "SELECT (SELECT COUNT(countries.id) FROM countries  $searchQuery) as trx_count,countries.id,
                    countries.country,countries.country_code,countries.priority FROM countries $sql";

            $results = $this->rawSelect('dbSports', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Countries found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Countries fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    function GetSports()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Sports";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetSports Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;

        $sport_id = $data['sport_id'] ?? false;
        $sport_name = $data['sport_name'] ?? false;
        $priority = $data['priority'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "sports." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'sports.priority';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Database Query
            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($sport_id) {
                $searchParams["sport_id"] = $sport_id;
                $searchQuery .= " AND sports.id = :sport_id";
            }
            if ($sport_name) {
                $searchParams["sport_name"] = $sport_name;
                $searchQuery .= " AND sports.name = :sport_name";
            }
            if ($priority) {
                $searchParams["priority"] = $priority;
                $searchQuery .= " AND sports.priority = :priority";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            // Combine with search query
            $sql = $searchQuery . ' ' . $sorting;

            // Query for main data
            $query = "SELECT (SELECT COUNT(sports.id) FROM sports  $searchQuery) as trx_count,sports.id,
                    sports.sport_id,sports.sport_name,sports.priority FROM sports $sql";

            $results = $this->rawSelect('dbSports', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Sports found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Sports fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetTournaments
     * @param type
     * @return type
     */
    function GetTournaments()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Tournaments";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetTournaments1 Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;

        $sport_id = $data['sport_id'] ?? false;
        $category_id = $data['category_id'] ?? false;
        $tournament_id = $data['tournament_id'] ?? false;
        $country = $data['country'] ?? false;
        $priority = $data['priority'] ?? false;
        $tournament_name = $data['tournament_name'] ?? false;
        $tournament_priority = $data['tournament_priority'] ?? false;
        $page = $data['page'] ?? false;
        $offset = $data['offset'] ?? 0;
        $sort = $data['sort'] ?? false;
        $limit = $data['limit'] ?? false;
        $export = $data['export'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "t." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 't.priority';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Database Query
            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($tournament_name) {
                $searchParams["tournament_name"] = "%$tournament_name%";
                $searchQuery .= " AND t.tournament_name LIKE :tournament_name";
            }
            if (is_numeric($sport_id)) {
                $searchParams[":sport_id"] = $sport_id;
                $searchQuery .= " AND t.sport_id = :sport_id";
            }
            if ($category_id) {
                $searchParams["category_id"] = $category_id;
                $searchQuery .= " AND t.category_id = :category_id";
            }
            if ($tournament_id) {
                $searchParams["tournament_id"] = $tournament_id;
                $searchQuery .= " AND t.tournament_id = :tournament_id";
            }
            if ($country) {
                $searchParams["country"] = $country;
                $searchQuery .= " AND t.country = :country";
            }
            if ($priority) {
                $searchParams["priority"] = $priority;
                $searchQuery .= " AND t.priority = :priority";
            }
            if ($tournament_priority) {
                $searchParams["tournament_priority"] = $tournament_priority;
                $searchQuery .= " AND t.tournament_priority = :tournament_priority";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            // Combine with search query
            $sql = $searchQuery . ' ' . $sorting;

            // Query for main data
            $query = "SELECT (SELECT COUNT(t.id) FROM tournaments t $searchQuery) AS trx_count,"
                . " t.id, t.tournament_id, t.tournament_name, t.season_name, t.sport_id, s.sport_name, t.country, t.priority,"
                . " t.tournament_priority, t.total_fixtures, t.created "
                . "FROM tournaments t INNER JOIN sports s ON t.sport_id = s.sport_id $sql";

            $results = $this->rawSelect('dbSports', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Tournaments found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Tournaments fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EditTournament
     * @param type $tournamentId
     * @return type
     */
    function EditTournament($tournamentId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Edit Tournament";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request EditTournament:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $tournament_name = $data['tournament_name'] ?? false;
        $priority = $data['priority'] ?? false;
        $tournament_priority = $data['tournament_priority'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$tournamentId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbSports',
                "SELECT tournaments.tournament_id, tournaments.tournament_id,tournaments.tournament_name,tournaments.priority "
                . "FROM tournaments",
                [':id' => $tournamentId]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Tournament Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':tournament_id' => $tournamentId];

            if (!$tournament_name) {
                $fields[] = "tournament_name = :tournament_name";
                $params[':tournament_name'] = $tournament_name;
            }

            if (is_numeric($priority)) {
                $fields[] = "priority = :priority";
                $params[':priority'] = $priority;
            }

            if (is_numeric($tournament_priority)) {
                $fields[] = "tournament_priority = :tournament_priority";
                $params[':tournament_priority'] = $tournament_priority;
            }

            $fields[] = "updated = NOW()";

            if (empty($fields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE tournaments SET " . implode(', ', $fields) . " WHERE tournament_id = :tournament_id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbSports', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful', [
                    'code' => 400,
                    'message' => "Sorry, Update failed for tournament ID: $tournamentId"
                ], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated tournament "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    function GetMarkets()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Markets";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | GetMarkets Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;

        $market_name = $data['market_name'] ?? false;
        $sport_id = $data['sport_id'] ?? false;
        $priority = $data['priority'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $limit = $data['limit'] ?? false;
        $export = $data['export'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "m." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'm.priority';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action requires privileged access!"], true);
            }

            // Database Query
            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($priority) {
                $searchParams["priority"] = $priority;
                $searchQuery .= " AND m.priority = :priority";
            }

            if ($market_name) {
                $searchParams["market_name"] = "%$market_name%";
                $searchQuery .= " AND (m.name LIKE :market_name)";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            // Combine with search query
            $sql = $searchQuery . ' ' . $sorting;

            // Query for main data
            $query = "SELECT (SELECT COUNT(m.market_id) "
                . "FROM all_markets m  $searchQuery) as trx_count, "
                . "m.market_id, m.name, m.alias, m.priority, m.created "
                . "FROM all_markets m $sql";


//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . ": SQL: $query"
//                . "| all_markets params:" . json_encode($searchParams));

            $results = $this->rawSelect('dbSportsRead', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Tournaments found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Tournaments fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EditTournament
     * @param type $tournamentId
     * @return type
     */
    function EditMarket($marketId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Edit Market";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request EditTournament1:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $alias = $data['alias'] ?? false;
        $priority = $data['priority'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$marketId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbSports',
                "SELECT m.market_id, m.name, m.priority "
                . "FROM all_markets m",
                [':market_id' => $marketId]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Market Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':market_id' => $marketId];

            if (!empty($alias)) {
                $fields[] = "alias = :alias";
                $params[':alias'] = $alias;
            }

            if (!empty($priority)) {
                $fields[] = "priority = :priority";
                $params[':priority'] = $priority;
            }

            $fields[] = "updated = NOW()";

            if (empty($fields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE all_markets SET " . implode(', ', $fields) . " WHERE market_id = :market_id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbSports', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful', [
                    'code' => 400,
                    'message' => "Sorry, Update failed for market ID: $marketId"
                ], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated market "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreateGameCategories
     * @return type
     */
    function CreateGameCategories()
    {
        $start = $this->getMicrotime();

        $permissionName = "Create Game Categories";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $category_name = $data['category_name'] ?? false;
        $priority = $data['priority'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$category_name) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT game_categories.id, game_categories.category_name, game_categories.created_at, "
                . "game_categories.updated_at "
                . "FROM game_categories WHERE game_categories.category_name = :category_name",
                [':category_name' => $category_name]);

            if ($results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Category Name already exists!"], true);
            }

            $id = $this->rawInsertBulk('dbBets',
                'game_categories',
                ['category_name' => $category_name,
                    'created_at' => $this->now(),
                    'updated_at' => $this->now()
//                    'created_by' => $authResponse['data']['user_id']
                ]);

            // log activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Game Category Created Successfully!"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    function EditGameCategories($categoryId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Edit Game Categories";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $category_name = $data['category_name'] ?? false;
        $priority = $data['priority'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$categoryId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT game_categories.id, game_categories.category_name, game_categories.created_at, "
                . "game_categories.updated_at "
                . "FROM game_categories WHERE game_categories.id = :id",
                [':id' => $categoryId]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Category Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':id' => $categoryId];

            if ($category_name) {
                $fields[] = "category_name = :category_name";
                $params[':category_name'] = $category_name;
            }

            if (is_numeric($priority)) {
                $fields[] = "priority = :priority";
                $params[':priority'] = $priority;
            }

            $fields[] = "updated_at = NOW()";

            if (empty($fields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE game_categories SET " . implode(', ', $fields) . " WHERE id = :id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbBets', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful', [
                    'code' => 400,
                    'message' => "Sorry, Update failed for game category ID: $categoryId"
                ], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated game category "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetGameCategories
     * @return type
     */
    function GetGameCategories()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Game Categories";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "game_categories." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'game_categories.id';
            $order = 'ASC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelect('dbBetsRead',
                "SELECT game_categories.id, game_categories.category_name, game_categories.created_at, "
                . "game_categories.updated_at "
                . "FROM game_categories",
                []);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Game Categories found!'], true);
            }

            $responseData = [
                'record_count' => count($results),
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Game Categories fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetGames
     * @return type
     */
    function GetGames()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Games";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $category_id = $data['category_id'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        $order_arr = explode("|", $this->cleanStrSQL($sort));
        if (count($order_arr) > 1) {
            $sort = "games." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
//            $sort = 'games.game_name';
//            $order = 'ASC';
            $sort = 'games.category_id';
            $order = 'ASC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($category_id) {
                $searchParams["category_id"] = $category_id;
                $searchQuery .= " AND games.category_id = :category_id";
            }

            if ($status) {
                $searchParams["status"] = $status;
                $searchQuery .= " AND games.status = :status";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
            if ($export == 1) {
                $sorting = "";
            }

            // Combine with search query
            $sql = $searchQuery . ' ' . $sorting;
            $joins = "LEFT JOIN game_categories ON games.category_id = game_categories.id ";
            $query = "SELECT (SELECT COUNT(games.id) FROM games  $searchQuery) as trx_count,"
                . "games.id, games.game_name, games.provider, games.description, games.status, games.extra_data, "
                . "games.hot, games.favourite, games.image_url, games.created_at, game_categories.category_name "
                . "FROM games $joins $sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Games found!'], true);
            }

            $responseData = [
                'record_count' => $results[0]['trx_count'],
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Games fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * UpdateGame
     * @param type $gameId
     * @return type
     */
    function UpdateGame($gameId = false)
    {
        $start_time = $this->getMicrotime();

        $permissionName = "Update Game";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request UpdateGame:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $category_id = $data['category_id'] ?? false;
        $game_ids = $data['game_ids'] ?? false;

        // Individual game update fields
        $game_name = $data['game_name'] ?? false;
        $provider = $data['provider'] ?? false;
        $description = $data['description'] ?? false;
        $status = $data['status'] ?? false;
        $image_url = $data['image_url'] ?? false;
        $hot = $data['hot'] ?? false;
        $favourite = $data['favourite'] ?? false;
        $extra_data = $data['extra_data'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            // Handle bulk update by category_id
            if ($game_ids && $category_id) {
                // Ensure game_ids is an array
                if (!is_array($game_ids)) {
                    $game_ids = [$game_ids];
                }

                // Validate that all game_ids are numeric
                foreach ($game_ids as $id) {
                    if (!is_numeric($id)) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => "Invalid game ID provided!"], true);
                    }
                }

                // Create placeholders for IN clause
                $placeholders = implode(',', array_fill(0, count($game_ids), '?'));
                $sql = "UPDATE games SET category_id = ?, updated_at = NOW() WHERE id IN ($placeholders)";

                // Prepare parameters array
                $params = array_merge([$category_id], $game_ids);

                if (!$this->rawUpdateWithParams('dbBets', $sql, $params)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful', [
                            'code' => 400,
                            'message' => "Sorry, Bulk update failed for game IDs: " . implode(', ', $game_ids)
                        ], true);
                }

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is successful',
                    ['code' => 200,
                        'message' => "Successfully updated " . count($game_ids) . " games with category ID: $category_id"]);
            }

            // Handle individual game update
            if ($gameId) {
                // Check if game exists
                $results = $this->rawSelectOneRecord('dbBets',
                    "SELECT id, game_name, provider, description, status, category_id, image_url, hot, favourite, extra_data "
                    . "FROM games WHERE id = :id", [':id' => $gameId]);

                if (!$results) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 404,
                            'message' => "Game ID does not exist!"], true);
                }

                // Build the SET part of the SQL dynamically
                $fields = [];
                $params = [':id' => $gameId];

                if ($game_name !== false && !empty($game_name)) {
                    $fields[] = "game_name = :game_name";
                    $params[':game_name'] = $game_name;
                }

                if ($provider !== false && !empty($provider)) {
                    $fields[] = "provider = :provider";
                    $params[':provider'] = $provider;
                }

                if ($description !== false) {
                    $fields[] = "description = :description";
                    $params[':description'] = $description;
                }

                if (is_numeric($status)) {
                    $fields[] = "status = :status";
                    $params[':status'] = $status;
                }

                if (is_numeric($category_id)) {
                    $fields[] = "category_id = :category_id";
                    $params[':category_id'] = $category_id;
                }

                if ($image_url !== false) {
                    $fields[] = "image_url = :image_url";
                    $params[':image_url'] = $image_url;
                }

                if (is_numeric($hot)) {
                    $fields[] = "hot = :hot";
                    $params[':hot'] = $hot;
                }

                if (is_numeric($favourite)) {
                    $fields[] = "favourite = :favourite";
                    $params[':favourite'] = $favourite;
                }

                if ($extra_data !== false) {
                    $fields[] = "extra_data = :extra_data";
                    $params[':extra_data'] = $extra_data;
                }

                // Always update the updated_at timestamp
                $fields[] = "updated_at = NOW()";

                if (count($fields) <= 1) { // Only updated_at field
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        400, 'No valid fields provided for update', [], true);
                }

                $sql = "UPDATE games SET " . implode(', ', $fields) . " WHERE id = :id LIMIT 1";

                if (!$this->rawUpdateWithParams('dbBets', $sql, $params)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful', [
                            'code' => 400,
                            'message' => "Sorry, Update failed for game ID: $gameId"
                        ], true);
                }

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is successful',
                    ['code' => 200,
                        'message' => "Successfully updated game ID: $gameId"]);
            }

            // If neither bulk update nor individual update conditions are met
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 400,
                    'message' => "Invalid request. Provide either game_ids with category_id for bulk update, or gameId with fields for individual update."], true);


        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreateGames
     * @return type
     */
    function CreateGames()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "Create new system games";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $game_name = $data['game_name'] ?? false;
        $game_desc = $data['game_desc'] ?? false;
        $game_logo = $data['game_logo'] ?? false;
        $currency = $data['currency'] ?? false;
        $min_stake = $data['min_stake'] ?? false;
        $max_stake = $data['max_stake'] ?? false;
        $max_win = $data['max_win'] ?? false;
        $game_url_prefix = $data['game_url_prefix'] ?? false;
        $denominations = $data['denominations'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$game_name || !$game_desc || !$game_logo ||
            !$currency || !$min_stake || !$max_stake ||
            !$max_win || !$game_url_prefix || !$denominations) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if ($min_stake < 1) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 400,
                    'message' => 'Invalid Minimum stake!'], true);
        }

        if ($max_stake < 1 || ($max_stake < $min_stake)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 400,
                    'message' => 'Invalid Maximum stake!'], true);
        }

        if ($max_win < 1 || ($max_win < $min_stake) || ($max_win <= $max_stake)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 400,
                    'message' => 'Invalid Maximum Win configurations!'], true);
        }

        $denominations_arr = explode(',', $denominations);
        if (!$denominations_arr || count($denominations_arr) < 1) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 400,
                    'message' => 'Invalid Stake Denominations!'], true);
        }

        $denominations_arr = array_unique($denominations_arr);

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $res = $this->rawSelectOneRecord('dbBonus',
                "SELECT * FROM games WHERE game_name=:game_name", [':game_name' => $game_name]);
            if ($res) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => 'Game already exist!'], true);
            }

            $id = $this->rawInsertBulk('dbBonus',
                'game_settings',
                ['game_id' => $this->rawInsertBulk('dbBonus',
                    "games",
                    ['game_name' => $game_name,
                        'game_desc' => $game_desc,
                        'game_logo' => $game_logo,
                        'game_url_prefix' => $game_url_prefix,
                        'status' => 1,
                        'created_at' => $this->now(),
                        'created_by' => $authResponse['data']['user_id']]),
                    'currency' => $currency,
                    'min_stake' => $min_stake,
                    'max_stake' => $max_stake,
                    'max_win' => $max_win,
                    'denominations' => implode(':', $denominations_arr),
                    'created_at' => $this->now()]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => $game_name . ' Created created successfully'], true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

}
