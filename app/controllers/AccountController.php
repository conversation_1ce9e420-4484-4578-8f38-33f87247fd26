<?php

class AccountController extends \ControllerBase
{

    /**
     * GetSystemUsers
     * @return type
     */
    function GetSystemUsers()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View System Users";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $status = $data['status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['user_name'] = $data['user_name'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "user_login." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'user_login.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($status) {
                $searchParams[':status'] = $params['status'];
                $searchQuery .= " AND user_login.status = :status";

                $key .= '$status:' . $$searchParams;
            }

            if ($params['user_name']) {
                $searchParams[':user_name'] = $params['user_name'];
                $searchQuery .= " AND user.user_name REGEXP :user_name";

                $key .= '$user_name:' . $params['user_name'];
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND user.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND user.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND user.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            if ($params['skipCache'] == 1) {
                $cacheData = RedisUtils::redisRawSelectData($key);
                if ($cacheData) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => 'Queried Cache ' . $cacheData->record_count
                                . ' system Users successfully!',
                            'data' => [
                                'record_count' => $cacheData->record_count,
                                'result' => $cacheData->result]], false, true);
                }
            }

            RedisUtils::redisRawDelete($key);

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbUser',
                "select (select count(user.id) from  user join user_login on user.id=user_login.user_id "
                . "join user_roles on user_login.role_id=user_roles.id $searchQuery)trx_count"
                . ",user.id user_id ,user.user_name,user.status,user_login.status login_status "
                . ",user_login.role_id,user_roles.name role_name,user_login.success_attempts "
                . ",user_login.failed_reset_attempts,user_login.failed_attempts "
                . ",user_login.cumlative_failed_attempts,user_login.cumulative_success_login "
                . ",user_login.blocked_timeline,user_login.reset_expiry_date"
                . ",IFNULL(user_login.permission_acl,user_roles.permissions_acl) as permission_acl  "
                . ",user_login.last_logged_on,user_login.activation_date ,user_login.last_reset_date"
                . ",user_login.next_passwd_change_date ,user_login.created_at from user "
                . "join user_login on user.id=user_login.user_id "
                . "join user_roles on user_login.role_id=user_roles.id $searchQuery $sorting",
                $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no system users!'], true);
            }

            RedisUtils::redisRawInsertData($key,
                ['record_count' => $results[0]['trx_count'],
                    'result' => $results], 600);

            $arr = [];
            foreach ($results as $result) {
                $result['permissions'] = UserUtils
                    ::GetUserPermissions(str_replace(':', ',', $result['permission_acl']));
                unset($result['permission_acl']);
                $arr[] = $result;
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $arr[0]['trx_count']
                        . ' system users successfully!',
                    'data' => ['record_count' => $arr[0]['trx_count'],
                        'result' => $arr]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreateReconCiliation
     * @return type
     */
    function CreateReconCiliation()
    {
        $start = $this->getMicrotime();

        $permissionName = "Initiate System Reconciliation";

        $data = (array)$this->request->getPost();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $this->request->getPost('timestamp');
        $type = $this->request->getPost('type');
        $unique_id = $this->request->getPost('unique_id');
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$unique_id) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!in_array($type, [1, 2])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid Reconcilation Type!"], true);
        }

        if ($type == 1) {//Deposits
            $permissionName .= " Mpesa Deposit";
        }

        if ($type == 2) {//Withdrawals
            $permissionName .= " Mpesa Payouts/Withdrawals";
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $res = $this->rawSelectOneRecord('dbTrxn',
                "SELECT * FROM payment_reconcilation WHERE type=:type and unique_id=:unique_id",
                [':unique_id' => $unique_id, ':type' => $type]);
            if ($res) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => 'SimilaUploaded File already exists!'], true);
            }

            $files = $this->request->getUploadedFiles();
            $file = $files[0] ?? false;
            if (!$file) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Invalid File Request!"], true);
            }

            if ($file->getSize() < 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Upload Request Failed. Reason:File " . $file->getName()
                            . " has " . $file->getSize()], true);
            }

            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mtype = finfo_file($finfo, $file->getTempName());
            if (!in_array($mtype, [
                'text/csv'
                , 'application/csv'
                , 'text/plain'
                , 'application/octet-stream'])) {
                finfo_close($finfo);
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Upload Request Failed. Reason:Invalid File type. "
                            . "Found {$mtype}"], true);
            }

            $targeFile = $this->settings['uploads']['UploadsDir']
                . "P" . rand(100, 9999) . ".csv";
            if (!move_uploaded_file($file->getTempName(), $targeFile)) {
                finfo_close($finfo);
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Upload Request Failed. "
                            . "Reason:File Is service Error.unmovable"], true);
            }

            $recipient_count = (int)(exec('perl -pe \'s/\r\n|\n|\r/\n/g\' '
                . escapeshellarg($targeFile) . ' | wc -l'));
            if ($recipient_count < 1) {
                finfo_close($finfo);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => 'Uploaded File has {' . $recipient_count . '} recipients'], true);
            }

            $targetDir = "";
            switch ($type) {
                case 1://Deposit
                    if (!FileUtils::ValidateDepositDocument($targeFile)) {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => 'Uploaded File has invalid contents!'], true);
                    }

                    $targetDir = $this->settings['uploads']['Deposit'];
                    break;
                case 2://Withdrawal
                    $targetDir = $this->settings['uploads']['Withdraw'];
                    break;
                default:
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Invalid File Type!"], true);
                    break;
            }

            $connection = null;
            try {
                $connection = $this->di->getShared("dbTrxn");
                $connection->begin();

                $connection->execute("INSERT INTO payment_reconcilation(type,unique_id"
                    . ",recipients,user_id,status,ip_address,created_at) "
                    . "VALUES(:type,:unique_id,:recipients,:user_id,:status,:ip_address,:created_at)",
                    [':type' => $type,
                        ':unique_id' => $unique_id,
                        ':recipients' => $recipient_count,
                        ':user_id' => $authResponse['data']['user_id'],
                        ':status' => 2,
                        ':ip_address' => $this->ipAddress,
                        ':created_at' => $this->now()]);
                $recon_id = $connection->lastInsertId();
                $connection->commit();

                $destination = trim($this->settings['Uploads']['UploadsDir'] . "$type" . "_" . trim($recon_id) . ".csv");
                if (!copy("$targeFile", "$destination")) {
                    unlink($targeFile);

                    if (!$this->rawUpdateWithParams('dbTrxn',
                        "UPDATE payment_reconcilation set status=3,completed_date=now() "
                        . "WHERE id=:id LIMIT 1", [':id' => $recon_id])) {
                        $this->errorlogger->error(__LINE__ . ":" . __CLASS__
                            . "| UniqueId:::$unique_id"
                            . "| Error: ERR-500"
                            . "| Message:: Updating Failed!");
                    }

                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => 'Uploaded File Fail to move '
                                . 'to processing Location. ERR-500'], true);
                }

                unlink($targeFile);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is successful',
                    ['code' => 200,
                        'message' => 'Uploaded queued for processing!',
                        'data' => ['id' => $recon_id]]);
            } catch (Exception $ex) {
                if ($connection != null) {
                    $connection->rollback();
                }

                $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . "| Exception:::"
                    . "| Trace:" . $ex->getTraceAsString()
                    . "| Message::" . $ex->getMessage());
                throw $ex;
            }
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * DepositManualReconcile
     * @return type
     */
    function DepositManualReconcile()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "Initiate Manual Deposit Reconciliation";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $payer_name = $data['payer_name'] ?? false;
        $params['MSISDN'] = $data['msisdn'] ?? false;
        $params['TransID'] = $data['trans_id'] ?? false;
        $payer_trans_date = $data['trans_date'] ?? false;
        $params['TransAmount'] = $data['amount'] ?? false;
        $payer_paybill_number = $data['paybill_number'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken ||
            !$payer_name || !$params['TransAmount'] || !$params['MSISDN'] ||
            !$params['TransID'] || !$payer_trans_date) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$payer_paybill_number || !in_array($payer_paybill_number, ['736736'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid Transaction!"], true);
        }

        $params['BusinessShortCode'] = $payer_paybill_number;
        $params['MSISDN'] = $this->formatMobileNumber($params['MSISDN'], '254');
        $params['Network'] = $this->getMobileNetwork($params['MSISDN'], 254);
        if ($params['Network'] == 'UNKNOWN') {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 421,
                    'message' => "Invalid Mobile Number!"], true);
        }

        $utils = new Utilities();
        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $checkDuplicate = $this->rawSelectOneRecord("dbTrxn",
                "SELECT id FROM payin_transaction WHERE trxn_code=:trxn_code",
                [':trxn_code' => $params['TransID']]);
            if ($checkDuplicate) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 202,
                        'message' => "Duplicate Transaction!"], true);
            }

            $TransactionType = 'Pay Bill';
            $names = preg_split("~\s+~", $payer_name);
            $params['FirstName'] = $names[0] ?? false;
            $params['MiddleName'] = $names[1] ?? false;
            $params['LastName'] = $names[2] ?? false;

            $params['Signature'] = hash('sha256',
                ($params['TransID']
                    . '' . $params['MSISDN']
                    . '' . $params['BusinessShortCode']
                    . '' . $params['TransAmount']
                    . '' . $TransactionType
                    . '' . $this->settings['Authentication']['BCPublicKey']));

            $params['BillRefNumber'] = 'BO_RECON';
            $params['OrgAccountBalance'] = 0.0;
            $params['TransTime'] = $this->now('YmdHis');
            $params['CreatedBy'] = 'BO_RECON';
            $params['Currency'] = 'KES';
            $trxn_extra_data = ['trans_type' => $TransactionType];

            $time = $this->getMicrotime();
            $repayment_reference_id = $this->rawInsertBulk("dbTrxn",
                'payin_transaction',
                ['created' => $this->now(),
                    'txn_ip_address' => $this->ipAddress,
                    'trxn_extra_data' => json_encode($trxn_extra_data),
                    'trxn_repayment_type' => 'M-PESA C2B',
                    'trxn_timestamp' => $params['TransTime'],
                    'trxn_org_balance' => $params['OrgAccountBalance'],
                    'trxn_paybill' => $params['BusinessShortCode'],
                    'trxn_amount' => $params['TransAmount'],
                    'trxn_sender' => trim($payer_name),
                    'trxn_msisdn' => $params['MSISDN'],
                    'trx_status' => 200,
                    'trxn_account' => $params['BillRefNumber'],
                    'trxn_code' => $params['TransID']]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Payment reconciliation Posted successfully!']);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "|Exception Trace:" . $ex->getTraceAsString()
                . "|Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EditAuthChannel
     * @param type $channelId
     * @return type
     */
    function EditAuthChannel($channelId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Edit System Auth Channel";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $name = $data['name'] ?? false;
        $key = $data['key'] ?? false;
        $status = $data['status'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$channelId || !is_numeric($channelId)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        $status = (int)$status;
        if (!in_array($status, [1, 3])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 421,
                    'message' => "Invalid Status!"], true);
        }

        if ($key) {
            if (strlen($key) < 10 || strlen($key) > 100) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Channel AppKey should be between 10-100 in length!"], true);
            }
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $name = strtoupper($name);

            $check = $this->rawSelectOneRecord("dbProfile",
                "SELECT * FROM auth_channels WHERE id=:id",
                [':id' => $channelId]
            );
            if (!$check) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => 'Auth channel does not exist'], true);
            }

            if (!$name) {
                $name = $check['name'];
            }

            $updatParams = [
                ':id' => $check['id'],
                ':name' => $name,
                ':updated_by' => $authResponse['data']['user_id']
            ];

            $updateSQl = "UPDATE auth_channels SET name=:name,updated_by=:updated_by";

            if ($key) {
                $updateSQl .= ",app_key=:app_key";
                $updatParams[':app_key'] = $key;
            }

            if ($status) {
                $updateSQl .= ",status=:status";
                $updatParams[':status'] = $status;
            }

            $updateSQl .= " WHERE id=:id LIMIT 1";
            if ($this->rawUpdateWithParams('dbProfile', $updateSQl, $updatParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => 'Auth channel update failed'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Auth Channel updated Successfully!"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreateAuthChannel
     * @return type
     */
    function CreateAuthChannel()
    {
        $start = $this->getMicrotime();

        $permissionName = "Create Auth Channels";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $channelName = $data['channel_name'] ?? false;
        $channelAppKey = $data['app_key'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$appKey ||
            !$channelName || !$channelAppKey) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (strlen($channelAppKey) < 10 || strlen($channelAppKey) > 100) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Channel AppKey should be between 10-100 in length!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $channelName = strtoupper($channelName);

            $results = $this->rawSelect('dbProfile',
                "select * from auth_channels where name=:name",
                [':name' => $channelName]);
            if ($results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "ChannelName already exists!"], true);
            }

            $id = $this->rawInsertBulk('dbProfile',
                'auth_channels',
                ['name' => $channelName,
                    'app_key' => $channelAppKey,
                    'status' => 1,
                    'created_by' => $authResponse['data']['user_id']]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Auth Channel Created Successfully!"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreatePaybillEntry
     * @return type
     */
    function CreatePaybillEntry()
    {
        $start = $this->getMicrotime();

        $permissionName = "Create Mpesa Paybill Entry";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $paybillNumber = $data['paybill_number'] ?? false;
        $paybillType = $data['paybill_type'] ?? false;
        $paybillCode = $data['paybill_code'] ?? false;
        $actionType = $data['action_type'] ?? false;
        $initiatorName = $data['initiator_name'] ?? false;
        $initiatorPwd = $data['initiator_pwd'] ?? false;
        $secretKey = $data['secret_key'] ?? false;
        $consumerKey = $data['consumer_key'] ?? false;
        $serviceAppKey = $data['service_app_key'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$initiatorPwd ||
            !$accessToken || !$paybillNumber || !$initiatorName || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$serviceAppKey) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Service AppKey is required!"], true);
        }

        if ((strlen($paybillNumber) < 6) || (strlen($paybillNumber) > 7) || !is_numeric($paybillNumber)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid Paybill Number!"], true);
        }

        if (!in_array($paybillType, [1, 2])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid Paybill Type!"], true);
        }

        if ($paybillType == 2) {
            if (!in_array($actionType, [1, 2])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Invalid Paybill Action Type!"], true);
            }

            if ($actionType == 1) {
                if (!$secretKey || !$consumerKey) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 422,
                            'message' => "Required SecretKey or ConsumerKey "
                                . "from Safaricom Daraja portal!"], true);
                }
            }

            if ($actionType == 2) {
                if (!$paybillCode) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 422,
                            'message' => "Paybill Code is required!"], true);
                }

                if (!$serviceAppKey) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 422,
                            'message' => "Required AppKey from Broker Portal!"], true);
                }
            }
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelect('dbTrxn',
                "select * from paybills where paybill_number=:paybill_number "
                . "and paybill_type=:paybill_type",
                [':paybill_number' => $paybillNumber,
                    ':paybill_type' => $paybillType]);
            if ($results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Paybill Number already exists!"], true);
            }

            $id = $this->rawInsertBulk('dbTrxn',
                'paybill_settings',
                ['paybill_id' => $this->rawInsertBulk('dbTrxn',
                    'paybills',
                    ['paybill_number' => $paybillNumber,
                        'paybill_type' => $paybillType,
                        'reference_id' => (($paybillCode == false) ?
                            $this->ReferenceNumber() : $paybillCode),
                        'status' => 1,
                        'created_at' => $this->now()]),
                    'action_type' => $actionType,
                    'initiator_name' => $initiatorName,
                    'initiatior_pwd' => $this->Encrypt($initiatorPwd, $paybillNumber),
                    'secret_key' => $secretKey,
                    'consumer_key' => $consumerKey,
                    'service_app_key' => $serviceAppKey,
                    'created_at' => $this->now()]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Paybill Number Created Successfully!"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EditPaybillEntry
     * @param type $paybillId
     * @return type
     */
    function EditPaybillEntry($paybillId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Edit MPESA Paybill Settings";

        $data = (array)$this->request->getJsonRawBody();
        $string = (preg_replace('/"initiator_pwd":"[^"]*?"/', '"initiator_pwd":***password***'
            , json_encode($this->request->getJsonRawBody()) . PHP_EOL));
        $string = (preg_replace('/"secret_key":"[^"]*?"/', '"secret_key":***ket***'
            , json_encode($this->request->getJsonRawBody()) . PHP_EOL));
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:$string");

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $secretKey = $data['secret_key'] ?? false;
        $actionType = $data['action_type'] ?? false;
        $paybillType = $data['paybill_type'] ?? false;
        $consumerKey = $data['consumer_key'] ?? false;
        $initiatorPwd = $data['initiator_pwd'] ?? false;
        $initiatorName = $data['initiator_name'] ?? false;
        $paybillNumber = $data['paybill_number'] ?? false;
        $nominatedNumber = $data['nominated_number'] ?? false;
        $paybillCode = $data['paybill_code'] ?? false;
        $serviceAppKey = $data['service_app_key'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$paybillId || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbTrxn',
                "SELECT paybills.id,paybills.paybill_number,paybills.paybill_type"
                . ",paybill_settings.id settingId FROM paybills LEFT JOIN paybill_settings "
                . "ON paybills.id=paybill_settings.paybill_id WHERE paybills.id=:id "
                . "AND paybills.status=1", [':id' => $paybillId]);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Paybill is does not exists!!"], true);
            }

            $updatePaybillParams = [
                ':id' => $results['id']];
            $updatePaybillSql = "UPDATE paybills SET updated_at=NOW()";

            if ($paybillType) {
                if (!in_array($paybillType, [1, 2])) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Invalid Paybill Type!"], true);
                }

                $updatePaybillParams[':paybill_type'] = $paybillType;
                $updatePaybillSql .= ",paybill_type=:paybill_type";
            }

            if ($paybillNumber) {
                if ((strlen($paybillNumber) < 6) || (strlen($paybillNumber) > 7) ||
                    !is_numeric($paybillNumber)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Invalid Paybill Number!"], true);
                }

                if ($paybillNumber != $results['paybill_number']) {
                    $updatePaybillParams[':paybillNumber'] = $paybillNumber;
                    $updatePaybillSql .= ",paybill_number=:paybillNumber";
                }
            }

            if ($paybillCode) {
                if ($paybillType == 2) {
                    $updatePaybillParams[':reference_id'] = $paybillCode;
                    $updatePaybillSql .= ",reference_id=:reference_id";
                }
            }

            $updatePaybillSql .= " WHERE id=:id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbTrxn', $updatePaybillSql, $updatePaybillParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Editing Paybill Failed!"], true);
            }

            if (!$results['settingId']) {
                $p = [
                    'paybill_id' => $results['id'],
                    'action_type' => 1,
                    'initiator_name' => '',
                    'initiatior_pwd' => '',
                    'secret_key' => '',
                    'consumer_key' => '',
                    'service_app_key' => '',
                    'created_at' => $this->now()
                ];

                if ($nominatedNumber) {
                    if ($results['paybill_type'] == 1) {
                        $nominatedNumber = $this->formatMobileNumber($nominatedNumber);
                        $net = $this->getMobileNetwork($nominatedNumber);
                        if ($net == 'UNKNOWN') {
                            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                                200,
                                'Request is not successful',
                                ['code' => 400,
                                    'message' => "Invalid Nominated Number for C2B paybill!"], true);
                        }

                        $p['nominated_number'] = $nominatedNumber;
                    }
                }

                if ($initiatorPwd) {
                    $p['initiator_pwd'] = $this
                        ->Encrypt(base64_decode($initiatorPwd), $results['paybill_number']);
                }

                if ($initiatorName) {
                    $p['initiator_name'] = $initiatorName;
                }

                if ($secretKey) {
                    $p['secret_key'] = $secretKey;
                }

                if ($consumerKey) {
                    $p['consumer_key'] = $consumerKey;
                }

                if ($serviceAppKey) {
                    $p['service_app_key'] = $serviceAppKey;
                }

                if (in_array($actionType, [1, 2])) {
                    $p['action_type'] = $actionType;
                }

                $id = $this->rawInsertBulk('dbTrxn', 'paybill_settings', $p);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is successful',
                    ['code' => 200,
                        'message' => "Editing Paybill Succesfull!"]);
            }

            $updatePaybillSettngsParams = [':id' => $results['settingId']];
            $updatePaybillSettngsSql = "UPDATE paybill_settings SET updated_at=NOW()";

            if (in_array($actionType, [1, 2])) {
                $updatePaybillSettngsParams[':action_type'] = $actionType;
                $updatePaybillSettngsSql .= ",action_type=:action_type";
            }

            if ($initiatorName) {
                $updatePaybillSettngsParams[':initiator_name'] = $initiatorName;
                $updatePaybillSettngsSql .= ",initiator_name=:initiator_name";
            }

            if ($initiatorPwd) {
                // $initiatorPwd = base64_decode($initiatorPwd);

                $updatePaybillSettngsParams[':initiator_pwd'] = $this
                    ->Encrypt($initiatorPwd, $results['paybill_number']);
                $updatePaybillSettngsSql .= ",initiatior_pwd=:initiator_pwd";
            }

            if ($secretKey) {
                $updatePaybillSettngsParams[':secret_key'] = $secretKey;
                $updatePaybillSettngsSql .= ",secret_key=:secret_key";
            }

            if ($consumerKey) {
                $updatePaybillSettngsParams[':consumer_key'] = $consumerKey;
                $updatePaybillSettngsSql .= ",consumer_key=:consumer_key";
            }

            if ($serviceAppKey) {
                $updatePaybillSettngsParams[':service_app_key'] = $serviceAppKey;
                $updatePaybillSettngsSql .= ",service_app_key=:service_app_key";
            }

            if ($nominatedNumber) {
                if ($results['paybill_type'] == 1) {
                    $nominatedNumber = $this->formatMobileNumber($nominatedNumber);
                    $net = $this->getMobileNetwork($nominatedNumber);
                    if ($net == 'UNKNOWN') {
                        return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                            200,
                            'Request is not successful',
                            ['code' => 400,
                                'message' => "Invalid Nominated Number for C2B paybill!"], true);
                    }

                    $updatePaybillSettngsParams[':nominated_number'] = $nominatedNumber;
                    $updatePaybillSettngsSql .= ",nominated_number=:nominated_number";
                }
            }

            $updatePaybillSettngsSql .= " WHERE id=:id LIMIT 1";
            if (!$this->rawUpdateWithParams('dbTrxn', $updatePaybillSettngsSql, $updatePaybillSettngsParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Editing Paybill Failed!"], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Editing Paybill Succesfull!"]);
        } catch (Exception $ex) {
            $this->infologger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EnableDisablePayoutRouting
     * @param type $paybillId
     * @return type
     */
    function EnableDisablePayoutRouting($paybillId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Edit Or Disable Paybill Routing";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $paybillActionType = $data['action_type'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$paybillId || !$paybillActionType) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!in_array($paybillActionType, [1, 2])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid Paybill Action routing!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbTrxn',
                "SELECT paybills.paybill_number,paybills.status,paybill_settings.action_type "
                . "FROM paybills JOIN paybill_settings "
                . "ON paybills.id=paybill_settings.paybill_id WHERE paybills.id=:id "
                . "AND paybills.paybill_type=2",
                [':id' => $paybillId]);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Paybill is does not exists or is not a payout type!"], true);
            }

            if ($results['status'] != 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Sorry, Can't route a Payout "
                            . "Paybill (" . $results['paybill_number'] . ") "
                            . "that is in-active/suspended!"], true);
            }

            $paybillActionName = 'Daraja';
            if ($paybillActionType == 2) {
                $paybillActionName = 'Broker VPN';
            }

            if ($results['action_type'] == $paybillActionType) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Sorry, Update failed found similar route for "
                            . "Paybill (" . $results['paybill_number'] . ") "
                            . "is $paybillActionName!"], true);
            }


            if (!$this->rawUpdateWithParams('dbTrxn',
                "UPDATE paybill_settings SET action_type=:action_type WHERE paybill_id=:id LIMIT 1",
                [':id' => $paybillId, ':action_type' => $paybillActionType])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Sorry, Update failed the current route for "
                            . "Paybill (" . $results['paybill_number'] . ") "
                            . "is $paybillActionName!"], true);
            }

            UserUtils::LogUserActivity(
                ['user_id' => $authResponse['data']['user_id'],
                    'activity' => strtoupper(__FUNCTION__),
                    'request' => ['paybill_details' =>
                        ['id' => $paybillId,
                            'new_route' => $paybillActionType,
                            'old_route' => $results['action_type']]]]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated Paybill "
                        . "(" . $results['paybill_number'] . ") Route to $paybillActionName!"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * AddDeletePaybillIPs
     * @param type $paybillId
     * @return type
     */
    function AddDeletePaybillIPs($paybillId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Add Or Delete Paybill Allowed IPs";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $ip_address = $data['ip_address'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken ||
            !$paybillId || !$ip_address) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        $ips = [];
        foreach (explode(':', $ip_address) as $val) {
            if (filter_var($val, FILTER_VALIDATE_IP)) {
                $ips[] = $val;
                continue;
            }
        }

        if (sizeof($ips) < 1) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Invalid IP Addresses provided!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $checkPaybill = $this->rawSelectOneRecord("dbTrxn",
                "SELECT paybills.id FROM paybills "
                . "JOIN paybill_settings ON paybills.id=paybill_settings.paybill_id "
                . "WHERE paybills.id=:paybill_id AND paybills.status=1",
                [':paybill_id' => $paybillId]);
            if (!$checkPaybill) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "MPESA Paybill doesn't exist or is not in an active state!"], true);
            }

            if (!$this->rawUpdateWithParams('dbTrxn',
                'UPDATE paybill_settings SET allowed_ip=:allowed_ip '
                . 'WHERE paybill_id=:paybill_id LIMIT 1',
                [':paybill_id' => $checkPaybill['id'],
                    ':allowed_ip' => implode(':', $ips)])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Update MPESA Paybill IPs Failed!"], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Update MPESA Paybill IPs Successful!"]);
        } catch (Exception $ex) {
            $this->infologger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "|Exception Trace:" . $ex->getTraceAsString()
                . "|Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EnableDisablePaybill
     * @param type $paybillId
     * @return type
     */
    function EnableDisablePaybill($paybillId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Enable or Disable Paybills";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $paybillStatus = $data['status'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$paybillId || !$paybillStatus) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbTrxn', "SELECT * FROM paybills WHERE id=:id",
                [':id' => $paybillId]);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Paybill is does not exists!!"], true);
            }

            $pDesc = "Deposit";
            if ($results['paybill_type'] == 2) {
                $pDesc = "Payout";
            }

            $desc = 'Activated';
            if ($paybillStatus == 0) {
                $desc = 'De-Activated';
            }

            if ($paybillStatus == 3) {
                $desc = 'Suspended';
            }

            if ($results['status'] == $paybillStatus) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "$pDesc Paybill is $desc!"], true);
            }

            if (!$this->rawUpdateWithParams('dbTrxn',
                "UPDATE paybills SET status=:status WHERE id=:id LIMIT 1",
                [':id' => $paybillId, ':status' => $paybillStatus])) {

                UserUtils::LogUserActivity(
                    ['user_id' => $authResponse['data']['user_id'],
                        'activity' => strtoupper(__FUNCTION__),
                        'request' => ['paybill_details' =>
                            ['id' => $paybillId,
                                'desc' => "Failed to $desc $pDesc Paybill. Try again later!",
                                'new_status' => $paybillStatus,
                                'old_status' => $results['status']]]]);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Failed to $desc $pDesc Paybill. Try again later!"], true);
            }

            UserUtils::LogUserActivity(
                ['user_id' => $authResponse['data']['user_id'],
                    'activity' => strtoupper(__FUNCTION__),
                    'request' => ['paybill_details' =>
                        ['id' => $paybillId,
                            'desc' => "Successfully $desc $pDesc Paybill!",
                            'new_status' => $paybillStatus,
                            'old_status' => $results['status']]]]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully $desc $pDesc Paybill!"]);
        } catch (Exception $ex) {
            $this->infologger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * RegisterPaybillForPullService
     * @param type $paybillId
     * @return type
     */
    function RegisterPaybillForPullService($paybillId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Register MPESA Paybill For PullService(";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$paybillId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $checkPaybill = $this->rawSelectOneRecord("dbTrxn",
                "SELECT paybills.id,paybills.paybill_number"
                . ",paybill_settings.nominated_number"
                . ",paybill_settings.initiator_name"
                . ",paybill_settings.secret_key"
                . ",paybill_settings.consumer_key"
                . ",paybill_settings.action_type"
                . ",paybill_settings.service_app_key FROM paybills "
                . "JOIN paybill_settings ON paybills.id=paybill_settings.paybill_id "
                . "WHERE paybills.id=:paybill_id AND paybills.status=1 "
                . "AND paybills.paybill_type=:paybill_type",
                [':paybill_id' => $paybillId,
                    ':paybill_type' => 1]);
            if (!$checkPaybill) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "C2B MPESA Paybill doesn't exist!"], true);
            }

            if ($checkPaybill['action_type'] == 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "C2B MPESA Paybill is already "
                            . "Registered for the pull service!"], true);
            }

            $paymentIntegrations = new PaymentIntegrations();
            $getToken = $paymentIntegrations
                ->GetC2BAccessTokey($checkPaybill['paybill_number']
                    , $checkPaybill['consumer_key']
                    , $checkPaybill['secret_key']);

            if ($getToken['code'] != 200) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => "C2B MPESA Paybill Authentication failed!"], true);
            }

            $postData = [
                "ShortCode" => $checkPaybill['paybill_number'],
                "RequestType" => "Pull",
                "NominatedNumber" => $checkPaybill['nominated_number'],
                "CallBackURL" => $this->settings['mnoApps']['CallBacks']['MpesaCBPullURL']];

            $postUrl = $this->settings['mnoApps']['Urls']['PullRegisterUrl'];
            $result = $this
                ->SendHttpJsonPostData($postUrl, $postData,
                    ['Authorization: Bearer ' . $getToken['access_token']]);

            if ($result['statusCode'] != 200) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "C2B MPESA Paybill Registration is not successful!"], true);
            }

            $safResponse = (array)json_decode($result['response']);
            //$ResponseRefID = $safResponse['ResponseRefID'] ?? false;
            $ResponseStatus = $safResponse['Response Status'] ?? false;
            $ResponseDesc = $safResponse['Response Description'] ?? false;

            if ($ResponseStatus != 1000) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "C2B MPESA Paybill Pull Registration is not "
                            . "successful. Reason: $ResponseDesc"], true);
            }

            $safResponse['service'] = __FUNCTION__;


            if (!$this->rawUpdateWithParams('dbTrxn',
                'UPDATE paybill_settings SET action_type=1 WHERE paybill_id=:paybill_id LIMIT 1',
                [':paybill_id' => $checkPaybill['id']])) {

                $safResponse['action_response'] = "C2B MPESA Paybill Pull Registration is "
                    . "successful but failed to update final status.";

                UserUtils::LogUserActivity(
                    ['user_id' => $authResponse['data']['user_id'],
                        'activity' => 'C2B DEPOSIT PULL',
                        'request' => $safResponse]);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => $safResponse['action_response']], true);
            }

            $safResponse['action_response'] = "C2B MPESA Paybill Pull Registration is successful. "
                . "Reason: $ResponseDesc";

            UserUtils::LogUserActivity(
                ['user_id' => $authResponse['data']['user_id'],
                    'activity' => 'C2B DEPOSIT PULL',
                    'request' => $safResponse]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => $safResponse['action_response']]);
        } catch (Exception $ex) {
            $this->infologger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "|Exception Trace:" . $ex->getTraceAsString()
                . "|Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * ResendUserOTP
     * @return type
     */
    function ResendUserOTP($user_id)
    {
        $start = $this->getMicrotime();

        $permissionName = "Resend System User OTP";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request ResendUserOTP:".$user_id . "::::" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;

//        if (!$Authorization || !$appKey || !$hashKey || !$user_id || !$timestamp) {
        if (!$Authorization || !$appKey || !$hashKey || !$user_id) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!in_array($authResponse['data']['role_id'], [1, 2, 3])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbUser',
                "SELECT user.id,user.user_name,user.status,user_login.status"
                . ",user_roles.name role_name,user_roles.id role_id"
                . ",user_roles.status role_state,user_login.activation_date"
                . ",user.created_at FROM user "
                . "JOIN user_login ON user.id=user_login.user_id "
                . "JOIN user_roles ON user_login.role_id=user_roles.id "
                . "WHERE user.id=:id and user_roles.status=1 and user.status=1", [':id' => $user_id]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "User account doesn't exists!"], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            if ($results['role_id'] == 1) {
                if ($authResponse['data']['role_id'] != 1) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => $results['role_name'] . " User account "
                                . "cannot be altered by a user with lower access!"], true);
                }
            }

            if ($results['status'] == $this->settings['UserStatus']['Suspended']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Sorry, User account is "
                            . "suspended on all system actions. "
                            . "Contact System Administrator!"], true);
            }

            $issuedAt = new \DateTimeImmutable();
            $exp = $issuedAt->modify('+' . $this->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

            $access_token = $this->Encrypt(json_encode(
                ['uid' => $results['id'],
                    'username' => $results['user_name'],
                    'st' => 1,
                    'rid' => $results['role_id'],
                    'exp' => $exp->format('YmdHis'),
                    'iat' => $issuedAt->getTimestamp(),
                    'nbf' => $issuedAt->getTimestamp(),
                    'iss' => md5($this->settings['ServerName']),
                    'ip' => $this->getClientIPServer()]));
            $api_token = base64_encode(md5($access_token));

            $reset_expiry_date = $issuedAt
                ->modify('+' . $this->settings['Authentication'] ['ResetDelay'] . ' minutes')
                ->format('Y-m-d H:i:s');

            $v_code = $this->randStrGen(5);

            $updateParams = [
                ':verification_code' => $this->Encrypt(md5($v_code . "$" . $results['id'])),
                ":user_id" => $results['id'],
                ':access_token' => $api_token,
                ':reset_expiry_date' => $reset_expiry_date];

            $updateSql = "UPDATE user_login SET reset_attempts=0"
                . ",failed_attempts=0"
                . ",reset_expiry_date=:reset_expiry_date"
                . ",verification_code=:verification_code"
                . ",blocked_timeline=null,last_reset_date=NOW()"
                . ",access_token_expiry_date=null,status=2"
                . ",access_token=:access_token,verification_code=:verification_code "
                . "WHERE user_id=:user_id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbUser', $updateSql, $updateParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Account Reset Error. System is busy!']);
            }

            $msgUtils = new Messaging();

            $result = ['code' => 400];

            $unique_id = $this->ReferenceNumber();
            if (!$this->validateEmail($results['user_name'])) {
                $start = $this->getMicrotime();

                $result = $msgUtils->SendSMs(
                    ['msisdn' => $results['user_name'],
                        'message' => "Reset OTP $v_code  to verify your account",
                        'callback_url' => '',
                        'unique_id' => $unique_id]);

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $results['user_name'] . "]"
                    . "| UniqueId: $unique_id"
                    . "| SendSMs()" . json_encode($result));
            } else {
                $email_body = '<p>'
                    . '<h2>Hello, your reset OTP code is ' . '</br>' . $v_code . '</h2>'
                    . '</br>Link: ' . $this->settings ['AdminWebURL']
                    . '</br>Helpline: ' . $this->settings ['Helpline']
                    . '</p>';
                $result = $msgUtils->SendEmail(
                    ['from' => '<EMAIL>',
                        'decription' => 'Dashboard Notification Service'],
                    [$results['user_name']], 'MB Reset Account Password',
                    $email_body);

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $results['user_name'] . "]"
                    . "| UniqueId: $unique_id"
                    . "| SendSMs()" . json_encode($result));
            }

            if (!$result || ($result['code'] != 200)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Reset Account Password Failed."], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Reset Account Password was successful. "
                        . "Verify Account to continue. OTP Expires in "
                        . "~" . $this->settings['Authentication'] ['ResetDelay'] . " Minutes",
                    'expires_on' => $reset_expiry_date]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * BlacklistUser
     * @return type
     */
    function BlacklistUser($userId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Blacklist a System User";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $status = $data['status'] ?? false;
        $reason = $data['reason'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$userId || !$timestamp || !$reason) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!in_array($status, [1, 3, 5])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 421,
                    'message' => "Invalid Status!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $check = $this->rawSelectOneRecord('dbUser',
                "SELECT * FROM user WHERE id=:id", [':id' => $userId]);
            if (!$check) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "The System user does not exists!"], true);
            }


            $blacklist = $this->rawSelectOneRecord('dbUser',
                "SELECT * FROM user_black_list WHERE user_id=:user_id", [':user_id' => $userId]);

            if (!$blacklist) {
                if ($status == 3) {
                    $this->rawInsertBulk('dbUser',
                        "user_black_list",
                        ['user_id' => $userId,
                            'blacklist_reason' => $reason,
                            'status' => 3,
                            'blacklisted_on' => $this->now(),
                            'created_at' => $this->now()]);

                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => "Blacklisted User Successfully!"], false);
                }

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "User is not blacklisted!"], true);
            }

            $updateParams = [
                ':user_id' => $userId,
            ];
            $updateUser = "UPDATE user_black_list SET updated_at=NOW()";

            if (is_numeric($status)) {
                $updateUser .= ",status=:status";

                $updateParams[':status'] = $status;

                if ($status == 3) {
                    $updateUser .= ",blacklisted_on=NOW(),unblacklisted_on=null";
                }

                if ($status == 1) {
                    $updateUser .= ",blacklisted_on=null,unblacklisted_on=NOW()";
                }
            }

            $updateUser .= " WHERE user_id=:user_id LIMIT 1";

            if (!$this->rawUpdateWithParams("dbUser", $updateUser, $updateParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Blacklisting user failed!"], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => (($status == 1) ?
                            'Unblacklisted' :
                            'Blacklisted') . " User Successfully!"], false);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreateUserAccount
     * @return typeCreateUserAccount
     */
    function CreateUserAccount()
    {
        $start = $this->getMicrotime();

        $permissionName = "Create System User";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $role_id = $data['role_id'] ?? false;
        $username = $data['username'] ?? false;
        $display_name = $data['display_name'] ?? false;
        $permission_acl = $data['permissions_acl'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp ||
            !$accessToken || !$role_id || !$username || !$display_name) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        $username = trim($username);
        if (!$this->validateEmail($username)) {
            $dial_code = $this->settings['mnoApps']['DefaultDialCode'];
            $username = $this->formatMobileNumber($username, $dial_code);
            $network = $this->getMobileNetwork($username, $dial_code);
            if ($network == 'UNKNOWN') {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => 'Validation Error. Invalid Phone number!'], true);
            }
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $check = $this->rawSelectOneRecord('dbUser',
                "SELECT * FROM user WHERE user_name=:user_name", [':user_name' => $username]);
            if ($check) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "A similar system user already exists!"], true);
            }

            $uRoles = $this->rawSelectOneRecord('dbUser',
                "SELECT id,name,permissions_acl FROM user_roles WHERE id=:id "
                . "AND status=1", [':id' => $role_id]);
            $role_id = $uRoles['id'] ?? false;
            if (!$uRoles || !$role_id) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "User role is not available!"], true);
            }

            $msgUtils = new Messaging();
            $issuedAt = new \DateTimeImmutable();
            $password = $this->randStrGen(6);

            $verifyUrl = $this->settings['Site'] . "/verify";

            if ($role_id == 1) {
                $sUser = $this->rawSelectOneRecord('dbUser',
                    "SELECT id FROM user_login WHERE role_id=:role_id", [":role_id" => 1]);
                if ($sUser) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "There can only be one Super Administrator!"], true);
                } else {
                    if (!$sUser) {
                        $permission_arr = $this->rawSelectOneRecord("dbUser",
                            "SELECT group_concat(id separator ':') as acl "
                            . "FROM user_permissions WHERE status=1");

                        $userId = $this->rawInsertBulk('dbUser',
                            'user',
                            ['user_name' => $this->settings['Account']['SuperAdmin']['emailAddress'],
                                'password' => $this->Encrypt($password),
                                'display_name' => $this->settings['Account']['SuperAdmin']['displayName'],
                                'status' => 1,
                                'created_at' => $this->now(),
                                'created_by' => 0]);

                        $exp = $issuedAt->modify('+' . $this
                                ->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

                        $uLoginId = $this->rawInsertBulk('dbUser',
                            'user_login',
                            ['user_id' => $userId,
                                'role_id' => 1,
                                'permission_acl' => $permission_arr['acl'],
                                'verification_code' => $this->Encrypt(md5($password . "$" . $userId)),
                                'access_token' => base64_encode(md5($this->Encrypt(json_encode(
                                    ['uid' => $userId,
                                        'username' => $username,
                                        'st' => 1,
                                        'rid' => 1,
                                        'exp' => $exp->format('YmdHis'),
                                        'iat' => $issuedAt->getTimestamp(),
                                        'nbf' => $issuedAt->getTimestamp(),
                                        'iss' => md5($this->settings['ServerName']),
                                        'ip' => $this->getClientIPServer()])))),
                                'access_token_expiry_date' => $exp->format('Y-m-d H:i:s'),
                                'next_passwd_change_date' => $issuedAt->modify('+'
                                    . $this->settings['Authentication'] ['NextResetDays']
                                    . ' days')->format('Y-m-d H:i:s'),
                                'status' => $this->settings['UserStatus']['Unverified'],
                                'created_at' => $this->now()]);

                        if (!$this->rawUpdateWithParams('dbUser',
                            "UPDATE user SET password=:password WHERE id=:user_id LIMIT 1",
                            [':user_id' => $userId,
                                ':password' => $this->Encrypt(md5($password
                                    . "$" . $userId))])) {
                            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                                200,
                                'Request is not successful',
                                ['code' => 429,
                                    'message' => 'Change Password Error. System is busy!'], true);
                        }

                        if ($this->validateEmail($this->settings['Account']['SuperAdmin']['emailAddress'])) {
                            $result = $msgUtils->SendEmail(
                                ['from' => '<EMAIL>', 'decription' => 'OTP Notification Service'],
                                [$this->settings['Account']['SuperAdmin']['emailAddress']],
                                'Verify New Account OTP',
                                '<p><h2>Hello, your Verify Temp Password is ' . '</br>' . $password . '</h2>'
                                . '</br>Link: ' . $verifyUrl
                                . '</br>Helpline: ' . $this->settings ['Helpline'] . '</p>');

                            $this->infologger->info(__LINE__ . ":" . __CLASS__
                                . "|" . $this->CalculateTAT($start) . " Sec(s)"
                                . "| [" + $this->settings['Account']['SuperAdmin']['emailAddress'] + "]"
                                . "| SendEmail()" . json_encode($result));
                        } else {
                            $unique_id = $this->ReferenceNumber();

                            $result = $msgUtils->SendSMs(
                                ['msisdn' => $this->settings['Account']['SuperAdmin']['emailAddress'],
                                    'user_id' => $userId,
                                    'message' => "Mossbets BO Account created use."
                                        . "\n-\nTemp Password: $password"
                                        . "\n:Verify Link " . $verifyUrl,
                                    'callback_url' => '',
                                    'message_type' => 'NOTIFICATIONS',
                                    'unique_id' => $unique_id]);
                            $this->infologger->info(__LINE__ . ":" . __CLASS__
                                . "|" . $this->CalculateTAT($start) . " Sec(s)"
                                . "| [" + $this->settings['Account']['SuperAdmin']['emailAddress'] + "]"
                                . "| UniqueId: $unique_id"
                                . "| SendSMs()" . json_encode($result));
                        }
                    }
                }
            }

            $password = $this->randStrGen(6);

            if ($permission_acl) {
                $permission_arr = explode(':', $permission_acl);
                $permission_arr = array_unique($permission_arr);
                $permission_check_arr = implode(',', $permission_arr);

//                $this->infologger->info(__LINE__ . ":" . __CLASS__
//                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
//                    . "| [$username]"
//                    . "| Permissions()==>" . $permission_check_arr);

                $permissions = $this->rawSelectOneRecord("dbUser",
                    "SELECT group_concat(id separator ':') as acl "
                    . "FROM user_permissions WHERE status=1 AND id IN($permission_check_arr)");
                $permission_arr = $permissions['acl'] ?? false;
                if (!$permission_arr) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Invalid user permissions!"], true);
                }

                $permission_arr = explode(':', $permission_acl);
                $permission_arr = array_unique($permission_arr);
                if (UserUtils::CheckSuperUserPermission($permission_arr)) {
                    if ($role_id != 1) {
                        if (($key = array_search("1", $permission_arr)) !== false) {
                            unset($permission_arr[$key]);
                        }
                    }
                    $permission_arr = array_values($permission_arr);
                }

                $permission_acl = implode(':', $permission_arr);

//                $this->infologger->info(__LINE__ . ":" . __CLASS__
//                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
//                    . "| [$username]"
//                    . "| Permissions() =>" . $permission_acl);
            }

            $userId = $this->rawInsertBulk('dbUser',
                'user',
                ['user_name' => $username,
                    'password' => $password,
                    'display_name' => $display_name,
                    'status' => 1,
                    'created_at' => $this->now(),
                    'created_by' => $authResponse['data']['user_id']]);

            $exp = $issuedAt->modify('+' . $this->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

            $uLoginId = $this->rawInsertBulk('dbUser',
                'user_login',
                ['user_id' => $userId,
                    'role_id' => $role_id,
                    'permission_acl' => $permission_acl,
                    'verification_code' => $this->Encrypt(md5($password . "$" . $userId)),
                    'access_token' => base64_encode(md5($this->Encrypt(json_encode(
                        ['uid' => $userId,
                            'username' => $username,
                            'st' => 1,
                            'rid' => $role_id,
                            'exp' => $exp->format('YmdHis'),
                            'iat' => $issuedAt->getTimestamp(),
                            'nbf' => $issuedAt->getTimestamp(),
                            'iss' => md5($this->settings['ServerName']),
                            'ip' => $this->getClientIPServer()])))),
                    'access_token_expiry_date' => $exp->format('Y-m-d H:i:s'),
                    'next_passwd_change_date' => $issuedAt->modify('+'
                        . $this->settings['Authentication'] ['NextResetDays']
                        . ' days')->format('Y-m-d H:i:s'),
                    'status' => $this->settings['UserStatus']['Unverified'],
                    'created_at' => $this->now()]);

            if (!$this->rawUpdateWithParams('dbUser',
                "UPDATE user SET password=:password,status=1 "
                . "WHERE id=:user_id LIMIT 1",
                [':user_id' => $userId,
                    ':password' => $this->Encrypt(md5($password
                        . "$" . $userId))])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 429,
                        'message' => 'Change Password Error. System is busy!'], true);
            }

            $msgUtils = new Messaging();
            $unique_id = $this->ReferenceNumber();

            if ($this->validateEmail($username)) {
                $result = $msgUtils->SendEmail(
                    ['from' => '<EMAIL>', 'decription' => 'OTP Notification Service'],
                    [$username],
                    'Verify New Account OTP',
                    '<p><h2>Hello, your Verify Temp Password is ' . '</br>' . $password . '</h2>'
                    . '</br>Link: ' . $verifyUrl
                    . '</br>Helpline: ' . $this->settings ['Helpline'] . '</p>');
//                $this->infologger->info(__LINE__ . ":" . __CLASS__
//                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
//                    . "| [$username]"
//                    . "| SendEmail()" . json_encode($result));
            } else {
                $unique_id = $this->ReferenceNumber();

                $result = $msgUtils->SendSMs(
                    ['msisdn' => $username,
                        'user_id' => $userId,
                        'message' => "Mossbets BO Account created use."
                            . "\n-\nTemp Password: $password"
                            . "\n:Verify Link " . $verifyUrl,
                        'callback_url' => '',
                        'message_type' => 'NOTIFICATIONS',
                        'unique_id' => $unique_id]);
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [$username]"
                    . "| UniqueId: $unique_id"
                    . "| SendSMs()" . json_encode($result));
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => $uRoles['name'] . " User Account Created Successfully!"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

}
