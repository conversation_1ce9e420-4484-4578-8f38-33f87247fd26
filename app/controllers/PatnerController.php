<?php

/**
 * Description of BonusController
 *
 * <AUTHOR>
 */
class PatnerController extends \ControllerBase
{
    //select id,partner_id,service_id,rate_limit_per_minute,filters,status,created_at from partner_services;
    // select id,name,api_key,status,created_at from partners;
    //  select slip_id,partner_id,bet_id,sport_id,sport_id,parent_match_id,parent_market_id,market_id,selection_id,outcome_name,odd_value,pick,pick_name,winning_outcome,ht_scores,ft_scores,et_scores,extra_data,live_bet,status,resulting_type,start_time,created_at,updated_at from partners_bet_slips;
    // select id,name from services;
    /**
     * GetPartners
     * @return type
     */
    public function GetPartners() {
            $start_time = $this->getMicrotime();

            $permissionName = "View Partners";

            $data = $this->request->get();

            if ($this->request->getMethod() == 'GET') {
                unset($data['_url']);
            } else {
                $data = (array)$this->request->getJsonRawBody();
            }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartners :" . json_encode($data));

            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $Authorization = $headers['x-authorization'] ?? false;
            $hashKey = $headers['x-hash-key'] ?? false;
            $appKey = $headers['x-app-key'] ?? false;
            $accessToken = $headers['x-access'] ?? false;

            $timestamp = $data['timestamp'] ?? false;
            $params['limit'] = $data['limit'] ?? false;
            $params['skipCache'] = $data['skip_cache'] ?? false;
            $params['sort'] = $data['sort'] ?? false;
            $page = $data['page'] ?? false;
            $export = $data['export'] ?? false;
            $status = $data['status'] ?? false;
            $start = $data['start'] ?? false;
            $stop = $data['end'] ?? false;

            if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Mandatory field(s) required!"], true);
            }

            if (!$page) {
                $page = 1;
            }

            if (!$params['limit'] || !is_numeric($params['limit'])) {
                $params['limit'] = $this->settings['SelectRecordLimit'];
            }

            if (!in_array($params['skipCache'], [1, 2])) {
                $params['skipCache'] = 1;
            }

            $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
            if (count($order_arr) > 1) {
                $sort = "p." . $this->cleanStrSQL($order_arr[0]) . "";
                $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

                if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                    $order = 'DESC';
                }
            } else {
                $sort = 'p.id';
                $order = 'DESC';
            }

            try {
                $channel = UserUtils::GetApplicationKeys($appKey);
                if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 401,
                            'message' => 'Authorization Error. Invalid Key!'], true);
                }

                if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 401,
                            'message' => 'Security Error. Wrong authentictated request!'], true);
                }

                $authResponse = UserUtils::QuickAuthenticate($accessToken);
                if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request Requires Authentication',
                        ['code' => $authResponse['code'],
                            'message' => $authResponse['message']], true);
                }

                if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 403,
                            'message' => "Action require priviledged access!"], true);
                }

                $searchQuery = " WHERE 1";
                $searchParams = [];


                if (is_numeric($status)) {
                    $searchParams[':status'] = $status;
                    $searchQuery .= " AND p.status = :status";
                }

                if (($stop != null) && ($start != null)) {
                    $searchQuery .= " AND p.created_at BETWEEN :start AND :stop ";
                    $searchParams[':start'] = "$start 00:00:00";
                    $searchParams[':stop'] = "$stop 23:59:59";
                } elseif (($stop != null) && ($start == null)) {
                    $searchQuery .= " AND p.created_at <=:stop";
                    $searchParams[':stop'] = "$stop 23:59:59";
                } elseif ($stop == null && $start != null) {
                    $searchQuery .= " AND p.created_at>=:start";
                    $searchParams[':start'] = "$start 00:00:00";
                }

                $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
                if ($export == 1) {
                    $orderBy = $sort ? "ORDER BY $sort $order" : "";
                    $exportLimit = 50000;
                    $sorting = "$orderBy LIMIT $exportLimit";
                }

                // select id,name,api_key,status,created_at from partners;
                $sql ="SELECT (SELECT COUNT(p.id) FROM partners p  $searchQuery) as trx_count,"
                    . "p.id, p.name, p.api_key, p.status, p.created_at "
                    . "FROM partners p $searchQuery $sorting";

                $results = $this->rawSelect('dbSportsRead', $sql, $searchParams);

                if (!$results) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is not successful',
                        ['code' => 404,
                            'message' => 'Request returned no Partners!'], true);
                }

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is successful',
                    ['code' => 200,
                        'message' => 'Queried ' . $results[0]['trx_count']
                            . ' Partners successfully!',
                        'data' => ['record_count' => $results[0]['trx_count'],
                            'result' => $results]], false, true);

            } catch (Exception $ex) {
                $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                    . "| Exception Trace:" . $ex->getTraceAsString()
                    . "| Message:" . $ex->getMessage());

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 500,
                        'message' => "Internal Server Error."], true);
            }
        }


    }



