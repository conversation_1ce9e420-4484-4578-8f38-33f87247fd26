<?php

class TransactionsController extends \ControllerBase
{

    /**
     * GetDeposits
     * @return type
     */
    function GetDeposits()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customers Deposits";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $amount = $data['amount'] ?? false;
        $mpesa_code = $data['mpesa_code'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['paybill_number'] = $data['paybill_number'] ?? false;
        $params['name'] = $data['name'] ?? false;
        $params['mobile_number'] = $data['mobile_number'] ?? false;
        $params['bill_reference'] = $data['account_number'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "payin_transaction." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'payin_transaction.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($params['paybill_number']) {
                $searchParams[':trxn_paybill'] = $params['paybill_number'];
                $searchQuery .= " AND payin_transaction.trxn_paybill REGEXP :trxn_paybill";

                $key .= '$trxn_paybill:' . $params['paybill_number'];
            }

            if ($mpesa_code) {
                $searchParams[':trxn_code'] = $mpesa_code;
                $searchQuery .= " AND payin_transaction.trxn_code REGEXP :trxn_code";

                $key .= '$mpesa_code:' . $mpesa_code;
            }

            if ($params['bill_reference']) {
                $searchParams[':bill_reference'] = $params['bill_reference'];
                $searchQuery .= " AND payin_transaction.trxn_account REGEXP :bill_reference";

                $key .= '$bill_reference:' . $params['bill_reference'];
            }

            if ($params['mobile_number']) {
                $searchParams[':msisdn'] = $this->formatMobileNumber($params['mobile_number']);
                $searchQuery .= " AND payin_transaction.trxn_msisdn = :msisdn";

                $key .= '$trxn_msisdn:' . $params['mobile_number'];
            }

            if ($amount) {
                $searchParams[':amount'] = $amount;
                $searchQuery .= " AND payin_transaction.trxn_amount=:amount";

                $key .= '$trxn_amount:' . $amount;
            }

            if ($params['name']) {
                $searchParams[':trxn_sender'] = $params['name'];
                $searchQuery .= " AND payin_transaction.trxn_sender REGEXP :trxn_sender";

                $key .= '$trxn_sender:' . $params['name'];
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND payin_transaction.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND payin_transaction.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND payin_transaction.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            if ($params['skipCache'] == 1) {
                $cacheData = RedisUtils::redisRawSelectData($key);
                if ($cacheData) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => 'Queried Cache ' . $cacheData->record_count
                                . ' mpesa deposit request successfully!',
                            'data' => [
                                'record_count' => $cacheData->record_count,
                                'result' => $cacheData->result]], false, true);
                }
            }

            RedisUtils::redisRawDelete($key);

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbTrxn',
                "select (select count(payin_transaction.id) from payin_transaction $searchQuery)trx_count"
                . ",payin_transaction.id,payin_transaction.trxn_code,payin_transaction.trxn_msisdn"
                . ",payin_transaction.trxn_sender,payin_transaction.trxn_amount"
                . ",payin_transaction.trxn_account,payin_transaction.trxn_paybill"
                . ",payin_transaction.trxn_org_balance,payin_transaction.trxn_repayment_type"
                . ",payin_transaction.txn_ip_address,payin_transaction.trxn_extra_data"
                . ",payin_transaction.created_at "
                . "from payin_transaction $searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no mpesa deposit!'], true);
            }

            RedisUtils::redisRawInsertData($key,
                ['record_count' => $results[0]['trx_count'],
                    'result' => $results], 600);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' mpesa deposit successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]
                ], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());


            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetWithdrawals
     */
    function GetWithdrawals()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Withdrawal Transactions";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request GetWithdrawals:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $dlr_status = $data['status'] ?? false;
        $withdraw_desc = $data['withdraw_desc'] ?? false;
        $client_unique_id = $data['client_unique_id'] ?? false;
        $withdraw_amount = $data['withdraw_amount'] ?? false;
        $receipt_number = $data['receipt_number'] ?? false;
        $params['mobile_number'] = $data['mobile_number'] ?? false;
        $mobile_number = $data['mobile_number'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "withdrawal_request." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'withdrawal_request.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";
            $key .= '$:user_id' . $authResponse['data']['user_id'];

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($receipt_number) {
                $searchParams[':receipt_number'] = $receipt_number;
                $searchQuery .= " AND withdrawal_dlr.receipt_number REGEXP :receipt_number";

                $key .= '$receipt_number:' . $receipt_number;
            }

            if ($dlr_status) {
                $searchParams[':dlr_status'] = $dlr_status;
                $searchQuery .= " AND withdrawal_dlr.response_code = :dlr_status";

                $key .= '$dlr_status:' . $dlr_status;
            }

            if ($withdraw_desc) {
                $searchParams[':result_desc'] = $withdraw_desc;
                $searchQuery .= " AND withdrawal_dlr.result_desc = :result_desc";

                $key .= '$withdraw_desc:' . $withdraw_desc;
            }

            if ($withdraw_amount && is_numeric($withdraw_amount)) {
                $searchParams[':withdraw_amount'] = $withdraw_amount;
                $searchQuery .= " AND withdrawal_request.withdraw_amount = :withdraw_amount";

                $key .= '$withdraw_amount:' . $withdraw_amount;
            }

            if ($client_unique_id) {
                $searchParams[':client_unique_id'] = $client_unique_id;
                $searchQuery .= " AND withdrawal_request.client_unique_id = :client_unique_id";

                $key .= '$client_unique_id:' . $client_unique_id;
            }

            if ($params['mobile_number']) {
                $searchParams[':msisdn'] = $this->formatMobileNumber($params['mobile_number']);
                $searchQuery .= " AND p.msisdn = :msisdn";

                $key .= '$ob_msisdn:' . $params['mobile_number'];
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND withdrawal_request.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$start:' . $start . '$stop:' . $stop;
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND withdrawal_request.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$stop:' . $stop;
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND withdrawal_request.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";

                $key .= '$start:' . $start;
            }

            if ($params['skipCache'] == 1) {
                $cacheData = RedisUtils::redisRawSelectData($key);
                if ($cacheData) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => 'Queried Cache ' . $cacheData->record_count
                                . ' Withdrawal history request successfully!',
                            'data' => [
                                'record_count' => $cacheData->record_count,
                                'result' => $cacheData->result]], false, true);
                }
            }

            RedisUtils::redisRawDelete($key);

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = "select (select count(withdrawal_request.id) from withdrawal_request "
                . "join mossbets_profile.profile p ON withdrawal_request.profile_id=p.id "
                . "left join withdrawal_dlr On withdrawal_request.id=withdrawal_dlr.request_id $searchQuery)trx_count"
                . ",withdrawal_request.id,p.msisdn,withdrawal_request.currency"
                . ",withdrawal_request.withdraw_amount,withdrawal_request.charges"
                . ",withdrawal_request.reference_id,withdrawal_request.response_code"
                . ",withdrawal_request.response_desc,withdrawal_request.conversation_id"
                . ",withdrawal_request.originator_conversation_id"
                . ",withdrawal_request.service_status,withdrawal_request.retries"
                . ",withdrawal_request.ip_address source_ip"
                . ",withdrawal_request.channel_source request_channel"
                . ",withdrawal_dlr.response_code result_code,withdrawal_dlr.receipt_number"
                . ",withdrawal_dlr.receiver_name,withdrawal_dlr.org_utility_balance"
                . ",withdrawal_dlr.transaction_completed_date"
                . ",withdrawal_dlr.response_description as result_desc,withdrawal_dlr.ip_address dlr_ip"
                . ",withdrawal_request.created_at from withdrawal_request "
                . "join mossbets_profile.profile p ON withdrawal_request.profile_id=p.id "
                . "left join withdrawal_dlr On withdrawal_request.id=withdrawal_dlr.request_id "
                . "$searchQuery $sorting";

            $results = $this->rawSelect('dbTrxnRead', $sql, $searchParams);

//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . ": SQL: " . $sql
//                . "| Withdrawals params:" . json_encode($searchParams));


            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Withdrawal history!'], true);
            }

            RedisUtils::redisRawInsertData($key,
                ['record_count' => $results[0]['trx_count'],
                    'result' => $results], 600);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Withdrawal history successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetWalletApprovals
     */
    function GetWalletApprovals()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Wallet Approvals";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] |"
//            . "Request GetWalletApprovals: " . json_encode($data)
//            . __LINE__
//        );

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $transaction_id = $data['transaction_id'] ?? false;
        $source = $data['source'] ?? false;
        $min_amount = $data['min_amount'] ?? false;
        $max_amount = $data['max_amount'] ?? false;
        $status = $data['status'] ?? false;
        $approved_by = $data['approved_by'] ?? false;
        $mobile_number = $data['mobile_number'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "tpa." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'tpa.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";
            $key .= '$:user_id' . $authResponse['data']['user_id'];

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($source) {
                $searchParams[':source'] = $source;
                $searchQuery .= " AND tpa.source = :source";
                $key .= '$source:' . $source;
            }
            if ($transaction_id) {
                $searchParams[':transaction_id'] = $transaction_id;
                $searchQuery .= " AND tpa.transaction_id = :transaction_id";
                $key .= '$transaction_id:' . $transaction_id;
            }
            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND tpa.status = :status";
                $key .= '$status:' . $status;
            }
            if ($approved_by) {
                $searchParams[':approved_by'] = $approved_by;
                $searchQuery .= " AND tpa.approved_by = :approved_by";
                $key .= '$approved_by:' . $approved_by;
            }
            if ($min_amount && $max_amount) {
                $searchParams[':min_amount'] = $min_amount;
                $searchParams[':max_amount'] = $max_amount;
                $searchQuery .= " AND tpa.amount BETWEEN :min_amount AND :max_amount";
                $key .= '$min_amount:' . $min_amount;
                $key .= '$max_amount:' . $max_amount;
            }

            if ($mobile_number) {
                $searchParams[':msisdn'] = $this->formatMobileNumber($mobile_number);
                $searchQuery .= " AND p.msisdn = :msisdn";

                $key .= '$ob_msisdn:' . $mobile_number;
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND tpa.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$start:' . $start . '$stop:' . $stop;
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND tpa.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$stop:' . $stop;
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND tpa.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";

                $key .= '$start:' . $start;
            }

            if ($params['skipCache'] == 1) {
                $cacheData = ''; //RedisUtils::redisRawSelectData($key);
                if ($cacheData) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => 'Queried Cache ' . $cacheData->record_count
                                . ' Withdrawal history request successfully!',
                            'data' => [
                                'record_count' => $cacheData->record_count,
                                'result' => $cacheData->result]], false, true);
                }
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = 'SELECT (SELECT  count(tpa.id) FROM transaction_pending_approval tpa '
                . 'JOIN mossbets_profile.profile p ON tpa.profile_id = p.id '
                . $searchQuery . ') as trx_count, tpa.id, p.msisdn, p.name, tpa.transaction_id, '
                . 'tpa.currency, tpa.amount, tpa.source, tpa.description, '
                . 'tpa.status, tpa.reason, tpa.approved_by, tpa.approved_date, tpa.created_at, tpa.updated_at '
                . 'FROM transaction_pending_approval tpa '
                . 'JOIN mossbets_profile.profile p ON tpa.profile_id = p.id '
                . $searchQuery . ' ' . $sorting;

            $results = $this->rawSelect('dbTrxnRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Withdrawal history!'], true);
            }

            RedisUtils::redisRawDelete($key);
            RedisUtils::redisRawInsertData($key,
                ['record_count' => $results[0]['trx_count'],
                    'result' => $results], 600);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Withdrawal history successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UploadRiskApprovals
     */
    function UploadRiskApprovals()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Upload Risk Approvals";

        // Convert JSON raw body to array
        $jsonRaw = $this->request->getJsonRawBody();
        $data = json_decode(json_encode($jsonRaw), true); // Convert object to array

        // Validate that user_data array is provided and well-formed
        if (!isset($data['user_data']) || !is_array($data['user_data']) || empty($data['user_data'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Missing or empty 'user_data' array with phone_number, amount, and source"], true);
        }

        // Validate individual records inside user_data
        foreach ($data['user_data'] as $index => $record) {
            if (
                !isset($record['phone_number']) || empty($record['phone_number']) ||
                !isset($record['amount']) || !is_numeric($record['amount']) ||
                !isset($record['source']) || empty($record['source'])
            ) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Invalid record at index $index: each record must have valid phone_number, amount, and source"], true);
            }
        }

        // Validate headers
        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $requiredHeaders = ['x-authorization', 'x-hash-key', 'x-app-key', 'x-access'];
        foreach ($requiredHeaders as $header) {
            if (empty($headers[$header])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Missing required header: $header"], true);
            }
        }

        $Authorization = $headers['x-authorization'];
        $hashKey = $headers['x-hash-key'];
        $appKey = $headers['x-app-key'];
        $accessToken = $headers['x-access'];

        // Validate required fields outside user_data
        $requiredFields = ['timestamp',];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Missing required field: $field"], true);
            }
        }

        $timestamp = $data['timestamp'];
        $reason = $data['reason'] ?? null;
        $description = $data['description'] ?? null;
        $status = $data['status'];

        try {
            // Authentication and authorization checks
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            $userId = $authResponse['data']['user_id'] ?? null;

            // Get profile IDs for all phone numbers
            $phoneNumbers = array_column($data['user_data'], 'phone_number');
            $profileMap = [];

            if (!empty($phoneNumbers)) {
                $profiles = $this->rawSelect(
                    'dbProfile',
                    "SELECT id, msisdn FROM profile WHERE msisdn IN (".implode(',', array_fill(0, count($phoneNumbers), '?')).")",
                    $phoneNumbers
                );

                foreach ($profiles as $profile) {
                    $profileMap[$profile['msisdn']] = $profile['id'];
                }
            }

            function generateReferenceId() {
                $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                $result = 'SPD'; // Prefix
                for ($i = 0; $i < 6; $i++) {
                    $result .= $characters[rand(0, strlen($characters) - 1)];
                }
                return $result;
            }

            $now = $this->now();
            $transactionIds = [];
            $successCount = 0;

            // Process each record
            foreach ($data['user_data'] as $record) {
                $profileId = $profileMap[$record['phone_number']] ?? null;
                if (!$profileId) {
                    continue; // Skip if no profile found
                }
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "| Processing record for phone_number: " . $record['phone_number']);

                // Insert into transactions table
                $transactionData = [
                    'profile_id' => $profileId,
                    'reference_type_id' => 1,
                    'transaction_type_id' => 1,
                    'reference_id' => generateReferenceId(),
                    'amount' => $record['amount'],
                    'currency' => 'KES',
                    'source' => $record['source'],
                    'description' => $description ?? null,
                    'extra_data' => json_encode(['reason' => $reason]),
                    'created_at' => $now
                ];

                $transactionId = $this->rawInsert(
                    'dbTrxn',
                    "INSERT INTO transaction (profile_id, reference_type_id, transaction_type_id, reference_id, 
                 amount, currency, source, description, extra_data, created_at) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    array_values($transactionData)
                );

                if (!$transactionId) {
                    continue; // Skip if transaction creation failed
                }

                // Insert into transaction_pending_approval
                $approvalData = [
                    'profile_id' => $profileId,
                    'transaction_id' => $transactionId,
                    'currency' => 'KES',
                    'amount' => $record['amount'],
                    'source' => $record['source'],
                    'description' => $description ?? null,
                    'reason' => $reason ?? null,
                    'status' => $status ?? 0,
                    'approved_by' => $userId,
                    'approved_date' => $now,
                    'completed_on' => null,
                    'created_at' => $now,
                    'updated_at' => $now
                ];

                $columns = array_keys($approvalData);
                $placeholders = implode(',', array_fill(0, count($columns), '?'));

                $inserted = $this->rawInsert(
                    'dbTrxn',
                    "INSERT INTO transaction_pending_approval (" . implode(',', $columns) . ") 
                 VALUES ($placeholders)",
                    array_values($approvalData)
                );

                if ($inserted) {
                    $successCount++;
                }
            }

            if ($successCount === 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 500,
                        'message' => "Failed to process any records"], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Risk approvals successfully processed",
                    'count' => $successCount]);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * RiskApprovals
     */
    function RiskApprovals($trxId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Risk Approvals";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] |"
//            . "Request RiskApprovals: " . json_encode($data)
//        );

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $reason = $data['reason'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$status) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelectOneRecord('dbTrxnRead',
                "SELECT tpa.id, tpa.currency, tpa.amount, tpa.status "
                . "FROM transaction_pending_approval tpa WHERE id=:id AND tpa.status=0",
                [':id' => $trxId]);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Approval Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':id' => $trxId,
                ':approved_by' => (int)$authResponse['data']['user_id']];

            $fields[] = "approved_by=:approved_by";

            $fields[] = "status=:status";
            $params[":status"] = (int)$status;

            if ($status === "3") {
                $fields[] = "completed_on=:completed_on";
                $params[":completed_on"] = $this->now();

                $fields[] = "reason = :reason";
                $params[':reason'] = $reason;
            } else {
                $fields[] = "reason=:reason";
                $params[":reason"] = "Approved by: " . $authResponse['data']['user_name'];
            }

            $fields[] = "approved_date = :approved_date";
            $params[":approved_date"] = $this->now();

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE transaction_pending_approval SET "
                . implode(', ', $fields) . " WHERE id = :id Limit 1";
            if (!$this->rawUpdateWithParams('dbTrxn', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Update failed for Withdrawal ID: $trxId",
                    ], true);
            }

            // Queue

            $qN = "RISK_APPROVALS";

            $queue = new Queue();
            $res = $queue->ConnectAndPublishToQueue(
                ['id' => $trxId,
                    'date' => $this->now(),
                    'signature' => md5($trxId . '' . $authResponse['data']['user_id'])],
                $qN, $qN, $qN, null, null, null, null, "/", null);
            if (!$res) {
                $sql = "UPDATE transaction_pending_approval "
                    . "SET completed_on=null,reason=null,approved_date=null "
                    . "WHERE id = :id Limit 1";
                $this->rawUpdateWithParams('dbTrxn', $sql, [':id' => $trxId]);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful', [
                        'code' => 400,
                        'message' => "Sorry, Update failed for Withdrawal ID: $trxId"
                    ], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Risk Approval successfully updated"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetTransactions
     * @return type
     */
    function GetTransactions()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Transactions";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;
        $transaction_type_id = $data['transaction_type_id'] ?? false;
        $reference_type_id = $data['reference_type_id'] ?? false;
        $msisdn = $data['mobile_number'] ?? false;
        $amount = $data['amount'] ?? false;
        $transaction_id = $data['transaction_id'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "t." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 't.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];
            if ($msisdn) {
                $searchParams[':msisdn'] = $this->formatMobileNumber($msisdn);
                $searchQuery .= " AND p.msisdn = :msisdn";
            }

            if ($transaction_id) {
                $searchParams[':transaction_id'] = $transaction_id;
                $searchQuery .= " AND t.transaction_id = :transaction_id";
            }

            if ($reference_type_id) {
                $searchParams[':reference_type_id'] = $reference_type_id;
                $searchQuery .= " AND t.reference_type_id = :reference_type_id";
            }

            if ($transaction_type_id) {
                $searchParams[':transaction_type_id'] = $transaction_type_id;
                $searchQuery .= " AND t.transaction_type_id = :transaction_type_id";
            }

            if ($amount) {
                $searchParams[':amount'] = $amount;
                $searchQuery .= " AND t.amount = :amount";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND t.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND t.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND t.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            // Build conditional joins based on identifier_name and source
            $conditionalJoins = '';
            $createdByField = 'NULL as created_by';

            // Add conditional joins for softgaming_bets or virtuals_bet based on criteria
            $conditionalJoins .= ' LEFT JOIN mossbets_bets.softgaming_bets sg ON t.id = sg.bet_transaction_id '
                . 'AND r.identifier_name IN (\'VOID\', \'STAKE\', \'PAYOUTS\', \'REFUND\') '
                . 'AND t.source LIKE \'%SOFTGAMES%\' ';

            $conditionalJoins .= ' LEFT JOIN mossbets_bets.virtuals_bet vb ON t.id = vb.bet_transaction_id '
                . 'AND r.identifier_name IN (\'VOID\', \'STAKE\', \'PAYOUTS\', \'REFUND\') '
                . 'AND t.source NOT LIKE \'%SOFTGAMES%\' ';

            // Use COALESCE to get created_by from either table
            $createdByField = 'COALESCE(sg.created_by, vb.created_by) as created_by';

            $sql = 'SELECT (SELECT count(t.id) FROM transaction t '
                . 'JOIN transaction_reference tr ON t.transaction_type_id = tr.id '
                . 'JOIN mossbets_profile.profile p ON t.profile_id = p.id '
                . 'JOIN reference_types r ON t.reference_type_id = r.id '
                . $conditionalJoins . $searchQuery . ') as trx_count, '
                . 't.id, t.profile_id, t.reference_type_id, tr.reference_name as transaction_type, '
                . 'p.msisdn, t.transaction_type_id, r.name as reference_name, r.identifier_name, '
                . 't.reference_id, t.amount, t.currency, t.source, t.description, '
                . 't.extra_data, t.created_at, ' . $createdByField . ', '
                . 'JSON_UNQUOTE(JSON_EXTRACT(t.extra_data, \'$.i_gamedesc\')) as created_by2 '
                . 'FROM transaction t '
                . 'JOIN transaction_reference tr ON t.transaction_type_id = tr.id '
                . 'JOIN reference_types r ON t.reference_type_id = r.id '
                . 'JOIN mossbets_profile.profile p ON t.profile_id = p.id '
                . $conditionalJoins . $searchQuery . ' ' . $sorting;

            $results = $this->rawSelect('dbTrxnRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Customer Transaction!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Customer Transaction successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetKRATaxPaymentSummary
     * @return type
     */
    function GetKRATaxPaymentSummary()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View KRA Tax Payment Summary";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['summary_date'] = $data['summary_date'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "compliance_prn_payments_dlr." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'compliance_prn_payments_dlr.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#page$page";

            $searchQuery = ' WHERE 1';
            $searchParams = [];

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND compliance_prn_payments_dlr.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$start:' . $start . '$stop:' . $stop;
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND compliance_prn_payments_dlr.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$stop:' . $stop;
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND compliance_prn_payments_dlr.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";

                $key .= '$start:' . $start;
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbTrxn',
                "select (select count(compliance_prn_payments_dlr.id) from compliance_prn_payments_dlr "
                . "join compliance_prn_generation on "
                . "compliance_prn_payments_dlr.prn_number=compliance_prn_generation.prn_number $searchQuery)trx_count"
                . ",compliance_prn_payments_dlr.id,compliance_prn_payments_dlr.prn_number"
                . ",compliance_prn_generation.prn_amount,compliance_prn_generation.prn_reg_date"
                . ",compliance_prn_generation.tax_type,compliance_prn_payments_dlr.conversation_id"
                . ",compliance_prn_payments_dlr.original_conversation_id,compliance_prn_payments_dlr.response_code"
                . ",compliance_prn_payments_dlr.response_description,compliance_prn_payments_dlr.reciept_number"
                . ",compliance_prn_payments_dlr.callback_extra_data,compliance_prn_payments_dlr.status"
                . ",compliance_prn_payments_dlr.created_at from compliance_prn_payments_dlr "
                . "join compliance_prn_generation on "
                . "compliance_prn_payments_dlr.prn_number=compliance_prn_generation.prn_number "
                . "$searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Taxes summary records!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Taxes summary  records successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetKRATaxSummary
     * @return type
     */
    function GetKRATaxSummary()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View KRA Tax Summary";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['summary_date'] = $data['summary_date'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "compliance_transaction_summary." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'compliance_transaction_summary.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#page$page";

            $searchQuery = ' WHERE 1';
            $searchParams = [];

            if ($params['summary_date']) {
                $searchParams[':summary_date'] = $params['summary_date'];
                $searchQuery .= " AND compliance_transaction_summary.summary_date=:summary_date";

                $key .= '$summary_date:' . $params['summary_date'];
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND compliance_transaction_summary.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$start:' . $start . '$stop:' . $stop;
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND compliance_transaction_summary.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$stop:' . $stop;
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND compliance_transaction_summary.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";

                $key .= '$start:' . $start;
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbTrxn',
                "select (select count(id) from compliance_transaction_summary $searchQuery)trx_count"
                . ",id summary_id,bets total_bets,stake total_stke,payout total_payout"
                . ",(stake-payout) as total_ggr,excirce_tax excise_tax,witholding_tax,summary_date "
                . "from compliance_transaction_summary $searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Taxes summary records!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Taxes summary  records successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetKRATaxes
     * @return type
     */
    function GetKRATaxes()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View KRA Taxes records";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $params['bet_id'] = $data['bet_id'] ?? false;
        $params['mobile_number'] = $data['mobile_number'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                422,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "compliance_data_transmission." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'compliance_data_transmission.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#page$page";

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($params['mobile_number']) {
                $searchParams[':msisdn'] = $params['mobile_number'];
                $searchQuery .= " AND compliance_data_transmission.mobile_number=:msisdn";

                $key .= '$msisdn:' . $params['mobile_number'];
            }

            if ($params['bet_id']) {
                $searchParams[':bet_id'] = $params['bet_id'];
                $searchQuery .= " AND compliance_data_transmission.bet_id=:bet_id";

                $key .= '$bet_id:' . $params['bet_id'];
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND compliance_data_transmission.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$start:' . $start . '$stop:' . $stop;
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND compliance_data_transmission.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$stop:' . $stop;
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND compliance_data_transmission.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";

                $key .= '$start:' . $start;
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbTrxn',
                "select (select count(id) from compliance_data_transmission $searchQuery)trx_count"
                . ",id,bet_id,bet_type,bet_amount,bet_odds,account_number,mobile_number"
                . ",post_desc,stake_post_date,extra_data,status,created_at "
                . "from compliance_data_transmission $searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Taxes records!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Taxes records successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetAuthChannels
     * @return type
     */
    function GetAuthChannels()
    {
        $start = $this->getMicrotime();

        $permissionName = "View Authentication Channels";

        $data = (array)$this->request->get();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $channel_name = $data['channel_name'] ?? false;
        $status = $data['status'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $results = $this->rawSelect('dbProfile',
                "select (select count(id) from auth_channels)trx_count,id channel_id"
                . ",name channel_name,status from auth_channels", []);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Auth Channels doesn't exists!"], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Auth Channels successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPaybills
     * @return type
     */
    function GetPaybills()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Mpesa Paybills";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $paybill_number = $data['paybill_number'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;
        $export = $data['export'] ?? false;
        $paybill_type = $data['paybill_type'] ?? false;
        $paybill_status = $data['paybill_status'] ?? false;
        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "paybills." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'paybills.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request Requires Authentication',
                    ['code' => $authResponse['code'],
                        'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => "Action require priviledged access!"], true);
            }

            $key = __CLASS__ . "$" . __FUNCTION__;
            $key .= '$limit:' . $params['limit'] . "#Page$page";
            $key .= '$:user_id' . $authResponse['data']['user_id'];

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($paybill_status) {
                $searchParams[':paybill_status'] = $paybill_status;
                $searchQuery .= " AND paybills.status = :paybill_status";

                $key .= '$paybill_status:' . $paybill_status;
            }


            if ($paybill_number) {
                $searchParams[':paybill_number'] = $paybill_number;
                $searchQuery .= " AND paybills.paybill_number REGEXP :paybill_number";

                $key .= '$paybill_number:' . $paybill_number;
            }

            if ($paybill_type) {
                $searchParams[':paybill_type'] = $paybill_type;
                $searchQuery .= " AND paybills.paybill_type = :paybill_type";

                $key .= '$paybill_type:' . $paybill_type;
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND paybills.created_at "
                    . "BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$start:' . $start . '$stop:' . $stop;
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND paybills.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";

                $key .= '$stop:' . $stop;
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND paybills.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";

                $key .= '$start:' . $start;
            }

            if ($params['skipCache'] == 1) {
                $cacheData = RedisUtils::redisRawSelectData($key);
                if ($cacheData) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is successful',
                        ['code' => 200,
                            'message' => 'Queried Cache ' . $cacheData->record_count
                                . ' MPESA Paybills successfully!',
                            'data' => [
                                'record_count' => $cacheData->record_count,
                                'result' => $cacheData->result]], false, true);
                }
            }

            RedisUtils::redisRawDelete($key);

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $results = $this->rawSelect('dbTrxnRead',
                "select (select count(paybills.id) from paybills left join "
                . "paybill_settings on paybills.id=paybill_settings.paybill_id $searchQuery)trx_count"
                . ",paybills.id,paybills.paybill_number,paybills.paybill_type"
                . ",paybill_settings.initiator_name,paybills.reference_id"
                . ",paybill_settings.paybill_balance,paybill_settings.action_type"
                . ",paybill_settings.allowed_ip ip_whitelist,paybills.status,paybills.created_at "
                . "from paybills left join paybill_settings on paybills.id=paybill_settings.paybill_id "
                . "$searchQuery $sorting", $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no MPESA Paybills!'], true);
            }

            RedisUtils::redisRawInsertData($key,
                ['record_count' => $results[0]['trx_count'],
                    'result' => $results], 600);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' MPESA Paybills successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }
}
