<?php

use <PERSON>alcon\Mvc\Controller;
use ControllerBase as base;

class RedisUtils extends Controller {

    /**
     * SetTimeoutInSeconds
     * @param type $time
     * @return type
     */
    public static function SetTimeoutInSeconds($time) {
        return $time * 60;
    }

    /**
     * redisRawSelectData
     * @param type $key
     * @return boolean
     * @throws Exception
     */
    public static function redisRawSelectData($key) {
        $base = new base();

        try {
            $connection = $base->di->getShared('redis');
            if ($connection !== false) {
                $data = $connection->get($base->settings['systemName'] . '_' . $key);

                $connection = null;
                if ($data) {
                    return json_decode($data);
                }
            }

            return false;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * redisRawInsertData
     * @param string $key
     * @param type $data
     * @param type $timeout
     * @return \stdClass
     * @throws Exception
     */
    public static function redisRawInsertData($key, $data, $timeout = false) {
        $result = new \stdClass();
        $base = new base();

        $key = $base->settings['systemName'] . '_' . $key;
        try {
            $connection = $base->di->getShared('redis');

            $data = json_encode($data);
            if ($connection !== false) {
                if (is_numeric($timeout)) {
                    $connection->setex($key, $timeout, $data);
                } else {
                    $connection->set($key, $data);
                }

                $connection->bgSave();
                $connection = null;

                $result->status = true;
                $result->description = "Set '$key' successfully";
                return $result;
            }

            $result->status = false;
            $result->description = "Connection to Redis Server failed";
            return $result;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * redisRawDelete
     * @param string $key
     * @return boolean
     * @throws Exception
     */
    public static function redisRawDelete($key,$allMatches = false) {
        $base = new base();

        $key = $base->settings['systemName'] . '_' . $key;

        if($allMatches){
            $key = $base->settings['systemName'] . '_' . $key . '*';
        }

        try {
            $connection = $base->di->getShared('redis');
            if ($connection !== false) {
                if($allMatches){
                    $keys = $connection->keys($key);
                    if($keys){
                        foreach($keys as $k){
                            $connection->del($k);
                        }
                    }

                    return true;
                }
                return $connection->del($key);
            }

            return false;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

}
