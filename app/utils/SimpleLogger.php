<?php

/**
 * Simple Logger replacement for Monolog
 * Compatible with PHP 7.4
 */
class SimpleLogger {
    
    const DEBUG = 100;
    const INFO = 200;
    const NOTICE = 250;
    const WARNING = 300;
    const ERROR = 400;
    const CRITICAL = 500;
    const ALERT = 550;
    const EMERGENCY = 600;
    
    private $name;
    private $handlers = [];
    
    public function __construct($name = 'app') {
        $this->name = $name;
    }
    
    public function pushHandler($handler) {
        $this->handlers[] = $handler;
    }
    
    public function debug($message, array $context = []) {
        $this->log(self::DEBUG, $message, $context);
    }
    
    public function info($message, array $context = []) {
        $this->log(self::INFO, $message, $context);
    }
    
    public function notice($message, array $context = []) {
        $this->log(self::NOTICE, $message, $context);
    }
    
    public function warning($message, array $context = []) {
        $this->log(self::WARNING, $message, $context);
    }
    
    public function error($message, array $context = []) {
        $this->log(self::ERROR, $message, $context);
    }
    
    public function critical($message, array $context = []) {
        $this->log(self::CRITICAL, $message, $context);
    }
    
    public function alert($message, array $context = []) {
        $this->log(self::ALERT, $message, $context);
    }
    
    public function emergency($message, array $context = []) {
        $this->log(self::EMERGENCY, $message, $context);
    }
    
    private function log($level, $message, array $context = []) {
        foreach ($this->handlers as $handler) {
            $handler->handle($level, $message, $context);
        }
    }
}

/**
 * Simple Stream Handler replacement
 */
class SimpleStreamHandler {
    
    private $stream;
    private $level;
    private $formatter;
    
    public function __construct($stream, $level = SimpleLogger::DEBUG) {
        $this->stream = $stream;
        $this->level = $level;
    }
    
    public function setFormatter($formatter) {
        $this->formatter = $formatter;
    }
    
    public function handle($level, $message, array $context = []) {
        if ($level < $this->level) {
            return;
        }
        
        $formatted = $this->formatter ? 
            $this->formatter->format($level, $message, $context) : 
            $this->defaultFormat($level, $message, $context);
            
        file_put_contents($this->stream, $formatted, FILE_APPEND | LOCK_EX);
    }
    
    private function defaultFormat($level, $message, array $context = []) {
        $levelName = $this->getLevelName($level);
        $datetime = date('Y-m-d H:i:s');
        return "{$datetime} - [{$levelName}] - {$message}\n";
    }
    
    private function getLevelName($level) {
        $levels = [
            SimpleLogger::DEBUG => 'DEBUG',
            SimpleLogger::INFO => 'INFO',
            SimpleLogger::NOTICE => 'NOTICE',
            SimpleLogger::WARNING => 'WARNING',
            SimpleLogger::ERROR => 'ERROR',
            SimpleLogger::CRITICAL => 'CRITICAL',
            SimpleLogger::ALERT => 'ALERT',
            SimpleLogger::EMERGENCY => 'EMERGENCY',
        ];
        
        return $levels[$level] ?? 'UNKNOWN';
    }
}

/**
 * Simple Line Formatter replacement
 */
class SimpleLineFormatter {
    
    private $format;
    private $dateFormat;
    
    public function __construct($format = null, $dateFormat = null) {
        $this->format = $format ?: "%datetime% - [%level_name%] - %message%\n";
        $this->dateFormat = $dateFormat ?: 'Y-m-d H:i:s';
    }
    
    public function format($level, $message, array $context = []) {
        $levelName = $this->getLevelName($level);
        $datetime = date($this->dateFormat);
        
        $formatted = str_replace(
            ['%datetime%', '%level_name%', '%message%'],
            [$datetime, $levelName, $message],
            $this->format
        );
        
        return $formatted;
    }
    
    private function getLevelName($level) {
        $levels = [
            SimpleLogger::DEBUG => 'DEBUG',
            SimpleLogger::INFO => 'INFO',
            SimpleLogger::NOTICE => 'NOTICE',
            SimpleLogger::WARNING => 'WARNING',
            SimpleLogger::ERROR => 'ERROR',
            SimpleLogger::CRITICAL => 'CRITICAL',
            SimpleLogger::ALERT => 'ALERT',
            SimpleLogger::EMERGENCY => 'EMERGENCY',
        ];
        
        return $levels[$level] ?? 'UNKNOWN';
    }
}

// Compatibility aliases for existing code
class_alias('SimpleLogger', 'Monolog\Logger');
class_alias('SimpleStreamHandler', 'Monolog\Handler\StreamHandler');
class_alias('SimpleLineFormatter', 'Monolog\Formatter\LineFormatter');
