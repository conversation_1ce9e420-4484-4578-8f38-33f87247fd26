<?php

use <PERSON>alcon\Mvc\Controller;
use ControllerBase as base;

/**
 * Description of Authenticate
 *
 * <AUTHOR>
 */
class Authenticate extends Controller {

    /**
     * ApiKeyCheck
     * @param type $apiKey
     * @return type
     * @throws Exception
     */
    public static function ApiKeyCheck($apiKey) {
        $base = new base();
        try {
            if (!$apiKey) {
                return false;
            }

            try {
                $details = (array) json_decode($base->Decrypt($apiKey));
                $user_id = $details['user_id'] ?? false;
                $ch_code = $details['ch_code'] ?? false;
                $ch_id = $details['ch_id'] ?? false;
                $ip_address = $details['ip_address'] ?? false;
                $expires_on = $details['expires_on'] ?? false;
                $hash = hash('sha256', $apiKey);

                $base->infologger->info(__CLASS__ . ":" . __LINE__
                        . "| IP:" . $base->ipAddress
                        . "| AuthIP:" . $ip_address
                        . "| AuthIP:" . $ip_address
                        . "| Expires:" . $expires_on);

                $keyx = "MB:Authorise$" . $ch_code . "$" . $ch_id . "$" . $user_id;
                $data = RedisUtils::redisRawSelectData($keyx);
                if (!$data) {
                    RedisUtils::redisRawDelete($keyx);
                    return false;
                }

                if (!in_array($base->ipAddress, $base->settings['Account']['IP_Whitelist'])) {
                    $base->errorlogger->error(__CLASS__ . ":" . __LINE__
                            . "| Invalid IP:" . $base->ipAddress
                            . "| Whitelist:" . json_encode($base->settings['Account']['IP_Whitelist']));
//                    RedisUtils::redisRawDelete($keyx);
//                    return false;
                }

                $results = $base->rawSelectOneRecord('dbWrite',
                        "SELECT * FROM user_login WHERE user_id=:user_id "
                        . "AND token_key=:token_key AND expires_on>NOW()",
                        [':token_key' => $hash, ':user_id' => $user_id]);
                if (!$results) {
                    RedisUtils::redisRawDelete($keyx);
                    return false;
                }

                if ($ch_id != $results['channel_id']) {//Check if user has a valid Channel ID
                    RedisUtils::redisRawDelete($keyx);
                    return false;
                }

                return [
                    'user_id' => $user_id,
                    'channel_id' => $results['channel_id']
                ];
            } catch (Exception $ex) {
                return false;
            }
        } catch (Exception $ex) {
            $base->errorlogger->emergency(__CLASS__ . ":" . __LINE__
                    . "| IP:" . $base->ipAddress
                    . "| Exceptions:" . $ex->getMessage());

            throw $ex;
        }
    }

    /**
     * GetAppKeyDetails
     * @param type $appKey
     * @return boolean
     * @throws Exception
     */
    public static function GetAppKeyDetails($appKey) {
        $base = new base();
        try {
            $result = $base->rawSelectOneRecord('dbRead',
                    "SELECT id,ch_name,ch_code FROM channels WHERE status=1 AND ch_key=:ch_key",
                    [':ch_key' => $appKey]);
            if (!$result) {
                return false;
            }

            return $result;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * HashCreateSignature
     * @param type $request
     * @param type $tokenKey
     * @return type
     */
    public static function HashCreateSignature($request, $tokenKey) {
        $request = (array) $request;
        $hashkey = "";
        ksort($request, SORT_ASC);
        foreach ($request as $key => $value) {
            if (is_array($value) || is_object($value)) {
                $value = (array) $value;
                ksort($value, SORT_ASC);
                foreach ($value as $keyVal => $val) {
                    if (is_array($val) || is_object($val)) {
                        $val = (array) $val;
                        ksort($val, SORT_ASC);
                        $key = [];
                        foreach ($val as $kVal => $vl) {
                            if (is_array($vl) || is_object($vl)) {
                                $vl = (array) $vl;
                                ksort($vl, SORT_ASC);
                                $key[$kVal] = $vl;
                                continue;
                            }
                            $key[$kVal] = $vl;
                        }
                        $val = $key;
                    }
                    $hashkey .= "&$keyVal=" . md5(json_encode($val));
                }
                continue;
            }

            $hashkey .= "&$key=$value";
        }

        return md5(substr($hashkey, 1) . '' . $tokenKey);
    }

    /**
     * HashCalculate
     * @param type $request
     * @param type $hashKey
     * @param type $tokenKey
     * @return boolean
     */
    public static function HashCalculate($request, $hashKey, $tokenKey): bool {
        $hashkeyStr = self::HashCreateSignature($request, $tokenKey);

        if ($hashkeyStr == $hashKey) {
            return true;
        }
        return false;
    }

    /**
     * HashCreate
     * @param type $request
     */
    public static function HashCreate($request, $tokenKey) {
        $base = new base();
        $request = (array) $request;
        $hashkey = "";
        ksort($request, SORT_ASC);
        foreach ($request as $key => $value) {
            if (is_array($value)) {
                foreach ($value as $keyVal => $val) {
                    $hashkey .= "&$keyVal=" . md5(json_encode($val));
                }
                continue;
            }

            $hashkey .= "&$key=$value";
        }

        return md5(substr($hashkey, 1) . '' . $tokenKey);
    }

    /**
     * HashCalculate
     * @param type $hashKey
     * @return boolean
     */
    public static function HashCalculate1($hashKey) {
        $base = new base();

        try {
            if (!$hashKey || ($hashKey == null) || ($hashKey == '')) {
                return false;
            }

            $decrypted = $base->Decrypt($hashKey);
            if (!$decrypted) {
                return false;
            }

            $decrypted = json_decode($decrypted);
            $service = $decrypted->service_name ?? false;
            $aKey = boolval($decrypted->app_key) ?? false;

            if (strtolower($service) != 'service_app') {
                return false;
            }

            if (!$aKey) {
                return false;
            }

            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

}
