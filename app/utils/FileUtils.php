<?php

/**
 * Description of FileUtils
 *
 * <AUTHOR>
 */
use Phalcon\Mvc\Controller,
    ControllerBase as base;

class FileUtils extends Controller {

    /**
     * ValidateDepositDocument
     * @param type $filePath
     * @return bool
     */
    public static function ValidateDepositDocument($filePath): bool {
        $base = new base();
        try {
            if (!file_exists($filePath)) {
                return false;
            }

            $error = false;
            $rowNumber = 1;
            $fp = fopen($filePath, 'r');
            while (($row = fgetcsv($fp)) !== false && $rowNumber <= 7) {
                $base->infoLogger->info(__LINE__ . ":" . __CLASS__
                        . "[$filePath]"
                        . "| Row $rowNumber#" . json_encode($row));

                $columns = count($row);
                if ($rowNumber > 0 && $rowNumber <= 6) {
                    if ($columns > 2) {
                        $error = true;
                        break;
                    }

                    if ($rowNumber === 1) {
                        if ($row[0] !== 'Account Holder:') {
                            $error = true;
                            break;
                        }
                    }

                    if ($rowNumber === 2) {
                        if ($row[0] !== 'Short Code:') {
                            $error = true;
                            break;
                        }

                        if (!in_array($row[1], $base->settings['Account']['Mpesa']['PayinPaybills'])) {
                            $base->infoLogger->info(__LINE__ . ":" . __CLASS__
                                    . "[$filePath]"
                                    . "| Row $rowNumber#"
                                    . "| Required Paybills:"
                                    . json_encode($base->settings['Account']['Mpesa']['PayinPaybills'])
                                    . "| Invalid Paybill:" . $row[1]);

                            $error = true;
                            break;
                        }
                    }
                }

                if ($rowNumber == 7) {
                    $base->infoLogger->info(__LINE__ . ":" . __CLASS__
                            . "[$filePath]"
                            . "| Row $rowNumber#"
                            . "| Column Count" . count($row));
                }

                $rowNumber++;
            }

            if ($error === true) {
                return false;
            }

            return true;
        } catch (Exception $ex) {
            $base->errorLogger->emergency(__LINE__ . ":" . __CLASS__
                    . "| Exception"
                    . "| Trace::" . $ex->getTraceAsString()
                    . "| Message::" . $ex->getMessage());
            return false;
        }
    }

}
